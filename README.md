# Echo v2 - AI-Powered Customer Support Platform

Echo v2 is a comprehensive AI-powered customer support and communication platform that enables businesses to manage customer interactions, automate responses, and gain insights through advanced analytics and AI capabilities.

## 🚀 Features

### 📊 Dashboard & Analytics

- **Real-time Metrics**: Monitor total messages, human interventions, and credit balance
- **Conversation Analytics**: Track message trends, user engagement, and language distribution
- **Performance Insights**: Average processing time, evaluation counts, and topic analysis
- **Interactive Charts**: Visual representation of data with ECharts integration

### 💬 Multi-Channel Communication

- **Unified Inbox**: Manage conversations from multiple channels in one interface
- **Real-time Chat**: Live customer support with WebSocket integration
- **Message Management**: Send, receive, and track customer communications
- **Customer Profiles**: Detailed customer information and interaction history

### 🤖 AI Agent Management

- **Intelligent Responses**: AI-powered automated customer support
- **Agent Configuration**: Customize AI behavior and response styles
- **Human Intervention**: Seamless handoff between AI and human agents
- **Response Personalization**: Tailored responses based on customer context

### 📚 Knowledge Base

- **Document Management**: Upload and organize support documents
- **AI-Powered Search**: Intelligent content retrieval and recommendations
- **Table of Contents**: Structured document navigation
- **File Processing**: Support for multiple document formats including PDF

### 👥 User & Tenant Management

- **Multi-tenant Architecture**: Support for multiple organizations
- **Role-based Access**: Admin, agent, and user role management
- **User Onboarding**: Comprehensive tenant setup wizard
- **Team Collaboration**: Invite and manage team members

### ⚙️ Advanced Configuration

- **Business Setup**: Configure business information, goals, and preferences
- **Language Support**: Multi-language capabilities with auto-detection
- **CTA Management**: Customizable call-to-action configurations
- **Email Integration**: Email notification and recipient management

## 🛠️ Technology Stack

### Frontend

- **React 18.3.1** - Modern React with hooks and functional components
- **Vite 7.0.2** - Fast build tool and development server
- **Ant Design 5.21.6** - Professional UI component library
- **Redux Toolkit 2.4.0** - State management
- **React Router 6.27.0** - Client-side routing
- **Sass/SCSS** - Advanced CSS preprocessing

### Key Libraries

- **Axios** - HTTP client for API communication
- **ECharts** - Interactive data visualization
- **React Markdown** - Markdown rendering with GFM support
- **React PDF** - PDF document viewing
- **CKEditor 5** - Rich text editing
- **React Dropzone** - File upload functionality
- **Lodash** - Utility functions

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd echo-frontend
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file in the root directory:

   ```env
   VITE_API_BASE_URL=your_api_base_url
   VITE_WS_BASE_URL=your_websocket_base_url
   ```

4. **Start the development server**

   ```bash
   npm run dev
   # or
   npm start
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📜 Available Scripts

### Development

```bash
npm run dev        # Start development server with Vite
npm start          # Alternative start command
```

### Production

```bash
npm run build      # Build for production
npm run preview    # Preview production build locally
```

### Testing

```bash
npm test           # Run test suite
```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components and containers
│   ├── dashboard/      # Analytics and metrics
│   ├── channels/       # Communication interface
│   ├── manageAgents/   # User and agent management
│   ├── knowledgeBase/  # Document management
│   ├── tenantSetup/    # Onboarding wizard
│   └── settings/       # Configuration pages
├── services/           # API service functions
├── store/             # Redux store and slices
├── utils/             # Utility functions
├── hooks/             # Custom React hooks
├── constants/         # Application constants
├── configs/           # Configuration files
├── layouts/           # Layout components
└── styles/            # Global styles and themes
```

## 🔧 Configuration

### Environment Variables

- `VITE_API_BASE_URL` - Backend API base URL
- `VITE_WS_BASE_URL` - WebSocket server URL

### Build Configuration

The project uses Vite with custom configuration for:

- React JSX support
- SCSS/Less preprocessing
- Ant Design theme customization
- Path aliases (`@` for `src/`)
- Environment variable injection

## 🌐 Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## 📱 Responsive Design

Echo v2 is fully responsive and optimized for:

- Desktop computers
- Tablets
- Mobile devices
- Various screen sizes and orientations

## 🔐 Authentication & Security

- JWT-based authentication
- Role-based access control
- Secure API communication
- Protected routes and components
- Session management

## 🚀 Deployment

### Production Build

```bash
npm run build
```

The build artifacts will be stored in the `build/` directory, ready for deployment to any static hosting service.

### Deployment Options

- Static hosting (Netlify, Vercel, GitHub Pages)
- CDN deployment
- Docker containerization
- Traditional web servers

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and questions, please contact the development team or refer to the internal documentation.

---

**Echo v2** - Empowering businesses with intelligent customer support solutions.
