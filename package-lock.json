{"name": "echo-v2", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "echo-v2", "version": "0.1.0", "dependencies": {"@ckeditor/ckeditor5-build-classic": "^44.1.0", "@ckeditor/ckeditor5-react": "^9.4.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^2.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.21.6", "axios": "^1.7.7", "echarts": "^5.6.0", "file-saver": "^2.0.5", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.3", "react-masonry-css": "^1.0.16", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-simplemde-editor": "^5.2.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "sass": "^1.82.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "less": "^4.3.0", "vite": "^7.0.2"}}, "node_modules/@adobe/css-tools": {"version": "4.4.0", "license": "MIT"}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@ant-design/colors": {"version": "7.1.0", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.6.1"}}, "node_modules/@ant-design/cssinjs": {"version": "1.21.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "@emotion/hash": "^0.8.0", "@emotion/unitless": "^0.7.5", "classnames": "^2.3.1", "csstype": "^3.1.3", "rc-util": "^5.35.0", "stylis": "^4.3.3"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/@ant-design/cssinjs-utils": {"version": "1.1.1", "license": "MIT", "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@babel/runtime": "^7.23.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@ant-design/fast-color": {"version": "2.0.6", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7"}, "engines": {"node": ">=8.x"}}, "node_modules/@ant-design/icons": {"version": "5.5.1", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-svg": "^4.4.0", "@babel/runtime": "^7.24.8", "classnames": "^2.2.6", "rc-util": "^5.31.1"}, "engines": {"node": ">=8"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/@ant-design/icons-svg": {"version": "4.4.2", "license": "MIT"}, "node_modules/@ant-design/react-slick": {"version": "1.1.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.4", "classnames": "^2.2.5", "json2mq": "^0.2.0", "resize-observer-polyfill": "^1.5.1", "throttle-debounce": "^5.0.0"}, "peerDependencies": {"react": ">=16.9.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.26.0", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ckeditor/ckeditor5-adapter-ckfinder": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-alignment": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-autoformat": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-autosave": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-basic-styles": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-block-quote": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-bookmark": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-build-classic": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "44.1.0", "@ckeditor/ckeditor5-autoformat": "44.1.0", "@ckeditor/ckeditor5-basic-styles": "44.1.0", "@ckeditor/ckeditor5-block-quote": "44.1.0", "@ckeditor/ckeditor5-ckbox": "44.1.0", "@ckeditor/ckeditor5-ckfinder": "44.1.0", "@ckeditor/ckeditor5-cloud-services": "44.1.0", "@ckeditor/ckeditor5-easy-image": "44.1.0", "@ckeditor/ckeditor5-editor-classic": "44.1.0", "@ckeditor/ckeditor5-essentials": "44.1.0", "@ckeditor/ckeditor5-heading": "44.1.0", "@ckeditor/ckeditor5-image": "44.1.0", "@ckeditor/ckeditor5-indent": "44.1.0", "@ckeditor/ckeditor5-link": "44.1.0", "@ckeditor/ckeditor5-list": "44.1.0", "@ckeditor/ckeditor5-media-embed": "44.1.0", "@ckeditor/ckeditor5-paragraph": "44.1.0", "@ckeditor/ckeditor5-paste-from-office": "44.1.0", "@ckeditor/ckeditor5-table": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-ckbox": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "blurhash": "2.0.5", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-ckfinder": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-clipboard": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-cloud-services": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-code-block": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-core": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-watchdog": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-easy-image": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-editor-balloon": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-editor-classic": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-editor-decoupled": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-editor-inline": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-editor-multi-root": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-engine": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-utils": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-enter": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-essentials": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-select-all": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-undo": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-find-and-replace": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-font": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-heading": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-paragraph": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-highlight": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-horizontal-line": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-html-embed": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-html-support": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-image": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-undo": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-indent": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-integrations-common": {"version": "2.2.2", "license": "GPL-2.0-or-later", "engines": {"node": ">=18.0.0"}, "peerDependencies": {"ckeditor5": ">=42.0.0 || ^0.0.0-nightly"}}, "node_modules/@ckeditor/ckeditor5-language": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-link": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-list": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-markdown-gfm": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "ckeditor5": "44.1.0", "marked": "4.0.12", "turndown": "7.2.0", "turndown-plugin-gfm": "1.0.2"}}, "node_modules/@ckeditor/ckeditor5-media-embed": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-undo": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-mention": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-minimap": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-page-break": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-paragraph": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-paste-from-office": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-react": {"version": "9.4.0", "license": "GPL-2.0-or-later", "dependencies": {"@ckeditor/ckeditor5-integrations-common": "^2.2.2", "prop-types": "^15.7.2"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"ckeditor5": ">=42.0.0 || ^0.0.0-nightly", "react": "^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/@ckeditor/ckeditor5-remove-format": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-restricted-editing": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-select-all": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-show-blocks": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-source-editing": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-theme-lark": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-special-characters": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-style": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-table": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-theme-lark": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-typing": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-ui": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "color-convert": "2.0.1", "color-parse": "1.4.2", "lodash-es": "4.17.21", "vanilla-colorful": "0.7.2"}}, "node_modules/@ckeditor/ckeditor5-undo": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-upload": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0"}}, "node_modules/@ckeditor/ckeditor5-utils": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-watchdog": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-widget": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ckeditor/ckeditor5-word-count": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "ckeditor5": "44.1.0", "lodash-es": "4.17.21"}}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@emotion/hash": {"version": "0.8.0", "license": "MIT"}, "node_modules/@emotion/unitless": {"version": "0.7.5", "license": "MIT"}, "node_modules/@esbuild/linux-x64": {"version": "0.25.5", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils/node_modules/jest-get-type": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.10", "dev": true, "license": "MIT", "optional": true, "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@mixmark-io/domino": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@parcel/watcher": {"version": "2.5.0", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.0", "@parcel/watcher-darwin-arm64": "2.5.0", "@parcel/watcher-darwin-x64": "2.5.0", "@parcel/watcher-freebsd-x64": "2.5.0", "@parcel/watcher-linux-arm-glibc": "2.5.0", "@parcel/watcher-linux-arm-musl": "2.5.0", "@parcel/watcher-linux-arm64-glibc": "2.5.0", "@parcel/watcher-linux-arm64-musl": "2.5.0", "@parcel/watcher-linux-x64-glibc": "2.5.0", "@parcel/watcher-linux-x64-musl": "2.5.0", "@parcel/watcher-win32-arm64": "2.5.0", "@parcel/watcher-win32-ia32": "2.5.0", "@parcel/watcher-win32-x64": "2.5.0"}}, "node_modules/@parcel/watcher-linux-x64-glibc": {"version": "2.5.0", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-x64-musl": {"version": "2.5.0", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@rc-component/async-validator": {"version": "5.0.4", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.4"}, "engines": {"node": ">=14.x"}}, "node_modules/@rc-component/color-picker": {"version": "2.0.1", "license": "MIT", "dependencies": {"@ant-design/fast-color": "^2.0.6", "@babel/runtime": "^7.23.6", "classnames": "^2.2.6", "rc-util": "^5.38.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/context": {"version": "1.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/mini-decimal": {"version": "1.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0"}, "engines": {"node": ">=8.x"}}, "node_modules/@rc-component/mutate-observer": {"version": "1.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/portal": {"version": "1.1.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/qrcode": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/tour": {"version": "1.15.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/portal": "^1.0.0-9", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/trigger": {"version": "2.2.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.2", "@rc-component/portal": "^1.1.0", "classnames": "^2.3.2", "rc-motion": "^2.0.0", "rc-resize-observer": "^1.3.1", "rc-util": "^5.38.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@react-pdf-viewer/attachment": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/bookmark": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/core": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "peerDependencies": {"pdfjs-dist": "^2.16.105 || ^3.0.279", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/default-layout": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/attachment": "3.12.0", "@react-pdf-viewer/bookmark": "3.12.0", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/thumbnail": "3.12.0", "@react-pdf-viewer/toolbar": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/full-screen": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/get-file": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/open": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/page-navigation": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/print": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/properties": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/rotate": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/scroll-mode": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/search": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/selection-mode": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/theme": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/thumbnail": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/toolbar": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/full-screen": "3.12.0", "@react-pdf-viewer/get-file": "3.12.0", "@react-pdf-viewer/open": "3.12.0", "@react-pdf-viewer/page-navigation": "3.12.0", "@react-pdf-viewer/print": "3.12.0", "@react-pdf-viewer/properties": "3.12.0", "@react-pdf-viewer/rotate": "3.12.0", "@react-pdf-viewer/scroll-mode": "3.12.0", "@react-pdf-viewer/search": "3.12.0", "@react-pdf-viewer/selection-mode": "3.12.0", "@react-pdf-viewer/theme": "3.12.0", "@react-pdf-viewer/zoom": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@react-pdf-viewer/zoom": {"version": "3.12.0", "license": "https://react-pdf-viewer.dev/license", "dependencies": {"@react-pdf-viewer/core": "3.12.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@reduxjs/toolkit": {"version": "2.4.0", "license": "MIT", "dependencies": {"immer": "^10.0.3", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "reselect": "^5.1.0"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18", "react-redux": "^7.2.1 || ^8.1.3 || ^9.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-redux": {"optional": true}}}, "node_modules/@reduxjs/toolkit/node_modules/immer": {"version": "10.1.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/@remix-run/router": {"version": "1.20.0", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.19", "dev": true, "license": "MIT"}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.44.2", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.44.2", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@testing-library/dom": {"version": "10.4.0", "license": "MIT", "peer": true, "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "engines": {"node": ">=18"}}, "node_modules/@testing-library/dom/node_modules/aria-query": {"version": "5.3.0", "license": "Apache-2.0", "peer": true, "dependencies": {"dequal": "^2.0.3"}}, "node_modules/@testing-library/jest-dom": {"version": "5.17.0", "license": "MIT", "dependencies": {"@adobe/css-tools": "^4.0.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1", "aria-query": "^5.0.0", "chalk": "^3.0.0", "css.escape": "^1.5.1", "dom-accessibility-api": "^0.5.6", "lodash": "^4.17.15", "redent": "^3.0.0"}, "engines": {"node": ">=8", "npm": ">=6", "yarn": ">=1"}}, "node_modules/@testing-library/jest-dom/node_modules/chalk": {"version": "3.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/@testing-library/react": {"version": "13.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0", "@types/react-dom": "^18.0.0"}, "engines": {"node": ">=12"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}, "node_modules/@testing-library/react/node_modules/@testing-library/dom": {"version": "8.20.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.1.3", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "engines": {"node": ">=12"}}, "node_modules/@testing-library/react/node_modules/aria-query": {"version": "5.1.3", "license": "Apache-2.0", "dependencies": {"deep-equal": "^2.0.5"}}, "node_modules/@testing-library/user-event": {"version": "13.5.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "engines": {"node": ">=10", "npm": ">=6"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}}, "node_modules/@types/aria-query": {"version": "5.0.4", "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/codemirror": {"version": "5.60.15", "license": "MIT", "dependencies": {"@types/tern": "*"}}, "node_modules/@types/debug": {"version": "4.1.12", "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/estree": {"version": "1.0.8", "license": "MIT"}, "node_modules/@types/estree-jsx": {"version": "1.0.5", "license": "MIT", "dependencies": {"@types/estree": "*"}}, "node_modules/@types/hast": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/unist": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.14", "license": "MIT", "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/jest/node_modules/@jest/schemas": {"version": "29.6.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/@jest/types": {"version": "29.6.3", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/@sinclair/typebox": {"version": "0.27.8", "license": "MIT"}, "node_modules/@types/jest/node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/jest/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@types/jest/node_modules/diff-sequences": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/expect": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-diff": {"version": "29.7.0", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-get-type": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-matcher-utils": {"version": "29.7.0", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-message-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/pretty-format": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/@types/marked": {"version": "4.3.2", "license": "MIT", "peer": true}, "node_modules/@types/mdast": {"version": "4.0.4", "license": "MIT", "dependencies": {"@types/unist": "*"}}, "node_modules/@types/ms": {"version": "2.1.0", "license": "MIT"}, "node_modules/@types/node": {"version": "24.0.10", "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/prop-types": {"version": "15.7.13", "license": "MIT"}, "node_modules/@types/react": {"version": "18.3.12", "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.3.1", "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "license": "MIT"}, "node_modules/@types/tern": {"version": "0.23.9", "license": "MIT", "dependencies": {"@types/estree": "*"}}, "node_modules/@types/testing-library__jest-dom": {"version": "5.14.9", "license": "MIT", "dependencies": {"@types/jest": "*"}}, "node_modules/@types/unist": {"version": "3.0.3", "license": "MIT"}, "node_modules/@types/use-sync-external-store": {"version": "0.0.3", "license": "MIT"}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT"}, "node_modules/@ungap/structured-clone": {"version": "1.2.0", "license": "ISC"}, "node_modules/@vitejs/plugin-react": {"version": "4.6.0", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.19", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}}, "node_modules/acorn": {"version": "8.15.0", "dev": true, "license": "MIT", "optional": true, "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/antd": {"version": "5.21.6", "license": "MIT", "dependencies": {"@ant-design/colors": "^7.1.0", "@ant-design/cssinjs": "^1.21.1", "@ant-design/cssinjs-utils": "^1.1.1", "@ant-design/icons": "^5.5.1", "@ant-design/react-slick": "~1.1.2", "@babel/runtime": "^7.25.6", "@ctrl/tinycolor": "^3.6.1", "@rc-component/color-picker": "~2.0.1", "@rc-component/mutate-observer": "^1.1.0", "@rc-component/qrcode": "~1.0.0", "@rc-component/tour": "~1.15.1", "@rc-component/trigger": "^2.2.3", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "rc-cascader": "~3.28.2", "rc-checkbox": "~3.3.0", "rc-collapse": "~3.8.0", "rc-dialog": "~9.6.0", "rc-drawer": "~7.2.0", "rc-dropdown": "~4.2.0", "rc-field-form": "~2.4.0", "rc-image": "~7.11.0", "rc-input": "~1.6.3", "rc-input-number": "~9.2.0", "rc-mentions": "~2.16.1", "rc-menu": "~9.15.1", "rc-motion": "^2.9.3", "rc-notification": "~5.6.2", "rc-pagination": "~4.3.0", "rc-picker": "~4.6.15", "rc-progress": "~4.0.0", "rc-rate": "~2.13.0", "rc-resize-observer": "^1.4.0", "rc-segmented": "~2.5.0", "rc-select": "~14.15.2", "rc-slider": "~11.1.7", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-table": "~7.47.5", "rc-tabs": "~15.3.0", "rc-textarea": "~1.8.2", "rc-tooltip": "~6.2.1", "rc-tree": "~5.9.0", "rc-tree-select": "~5.23.0", "rc-upload": "~4.8.1", "rc-util": "^5.43.0", "scroll-into-view-if-needed": "^3.1.0", "throttle-debounce": "^5.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/aria-query": {"version": "5.3.2", "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.5", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-tree-filter": {"version": "2.1.0", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/attr-accept": {"version": "2.2.5", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axios": {"version": "1.7.7", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axios/node_modules/form-data": {"version": "4.0.1", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/bail": {"version": "2.0.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/bl": {"version": "4.1.0", "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/blurhash": {"version": "2.0.5", "license": "MIT"}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true, "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT", "optional": true, "peer": true}, "node_modules/call-bind": {"version": "1.0.7", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caniuse-lite": {"version": "1.0.30001726", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/ccount": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/character-entities": {"version": "2.0.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/character-entities-html4": {"version": "2.1.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/character-entities-legacy": {"version": "3.0.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/character-reference-invalid": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ckeditor5": {"version": "44.1.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "44.1.0", "@ckeditor/ckeditor5-alignment": "44.1.0", "@ckeditor/ckeditor5-autoformat": "44.1.0", "@ckeditor/ckeditor5-autosave": "44.1.0", "@ckeditor/ckeditor5-basic-styles": "44.1.0", "@ckeditor/ckeditor5-block-quote": "44.1.0", "@ckeditor/ckeditor5-bookmark": "44.1.0", "@ckeditor/ckeditor5-ckbox": "44.1.0", "@ckeditor/ckeditor5-ckfinder": "44.1.0", "@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-cloud-services": "44.1.0", "@ckeditor/ckeditor5-code-block": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-easy-image": "44.1.0", "@ckeditor/ckeditor5-editor-balloon": "44.1.0", "@ckeditor/ckeditor5-editor-classic": "44.1.0", "@ckeditor/ckeditor5-editor-decoupled": "44.1.0", "@ckeditor/ckeditor5-editor-inline": "44.1.0", "@ckeditor/ckeditor5-editor-multi-root": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-essentials": "44.1.0", "@ckeditor/ckeditor5-find-and-replace": "44.1.0", "@ckeditor/ckeditor5-font": "44.1.0", "@ckeditor/ckeditor5-heading": "44.1.0", "@ckeditor/ckeditor5-highlight": "44.1.0", "@ckeditor/ckeditor5-horizontal-line": "44.1.0", "@ckeditor/ckeditor5-html-embed": "44.1.0", "@ckeditor/ckeditor5-html-support": "44.1.0", "@ckeditor/ckeditor5-image": "44.1.0", "@ckeditor/ckeditor5-indent": "44.1.0", "@ckeditor/ckeditor5-language": "44.1.0", "@ckeditor/ckeditor5-link": "44.1.0", "@ckeditor/ckeditor5-list": "44.1.0", "@ckeditor/ckeditor5-markdown-gfm": "44.1.0", "@ckeditor/ckeditor5-media-embed": "44.1.0", "@ckeditor/ckeditor5-mention": "44.1.0", "@ckeditor/ckeditor5-minimap": "44.1.0", "@ckeditor/ckeditor5-page-break": "44.1.0", "@ckeditor/ckeditor5-paragraph": "44.1.0", "@ckeditor/ckeditor5-paste-from-office": "44.1.0", "@ckeditor/ckeditor5-remove-format": "44.1.0", "@ckeditor/ckeditor5-restricted-editing": "44.1.0", "@ckeditor/ckeditor5-select-all": "44.1.0", "@ckeditor/ckeditor5-show-blocks": "44.1.0", "@ckeditor/ckeditor5-source-editing": "44.1.0", "@ckeditor/ckeditor5-special-characters": "44.1.0", "@ckeditor/ckeditor5-style": "44.1.0", "@ckeditor/ckeditor5-table": "44.1.0", "@ckeditor/ckeditor5-theme-lark": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-undo": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-watchdog": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "@ckeditor/ckeditor5-word-count": "44.1.0"}}, "node_modules/classnames": {"version": "2.5.1", "license": "MIT"}, "node_modules/clsx": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/codemirror": {"version": "5.65.19", "license": "MIT", "peer": true}, "node_modules/codemirror-spell-checker": {"version": "1.1.2", "license": "MIT", "peer": true, "dependencies": {"typo-js": "*"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-parse": {"version": "1.4.2", "license": "MIT", "dependencies": {"color-name": "^1.0.0"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/comma-separated-tokens": {"version": "2.0.3", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/compute-scroll-into-view": {"version": "3.1.0", "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/copy-anything": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"is-what": "^3.14.1"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/copy-to-clipboard": {"version": "3.3.3", "license": "MIT", "dependencies": {"toggle-selection": "^1.0.6"}}, "node_modules/country-flag-icons": {"version": "1.5.19", "license": "MIT"}, "node_modules/css.escape": {"version": "1.5.1", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decode-named-character-reference": {"version": "1.0.2", "license": "MIT", "dependencies": {"character-entities": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/deep-equal": {"version": "2.2.3", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.5", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.2", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.1", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "optional": true, "engines": {"node": ">=4.0.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dequal": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/detect-libc": {"version": "1.0.3", "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/devlop": {"version": "1.1.0", "license": "MIT", "dependencies": {"dequal": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/dom-accessibility-api": {"version": "0.5.16", "license": "MIT"}, "node_modules/easymde": {"version": "2.20.0", "license": "MIT", "peer": true, "dependencies": {"@types/codemirror": "^5.60.10", "@types/marked": "^4.0.7", "codemirror": "^5.65.15", "codemirror-spell-checker": "1.1.2", "marked": "^4.1.0"}}, "node_modules/easymde/node_modules/marked": {"version": "4.3.0", "license": "MIT", "peer": true, "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 12"}}, "node_modules/echarts": {"version": "5.6.0", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/echarts/node_modules/tslib": {"version": "2.3.0", "license": "0BSD"}, "node_modules/electron-to-chromium": {"version": "1.5.179", "dev": true, "license": "ISC"}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "optional": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/errno": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/es-define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-get-iterator": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/esbuild": {"version": "0.25.5", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/estree-util-is-identifier-name": {"version": "3.0.0", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/expand-template": {"version": "2.0.3", "license": "(MIT OR WTFPL)", "optional": true, "engines": {"node": ">=6"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/file-saver": {"version": "2.0.5", "license": "MIT"}, "node_modules/file-selector": {"version": "2.1.2", "license": "MIT", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">= 12"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.3", "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/fs-constants": {"version": "1.0.0", "license": "MIT", "optional": true}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/github-from-package": {"version": "0.0.0", "license": "MIT", "optional": true}, "node_modules/gopd": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/has-bigints": {"version": "1.0.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hast-util-from-parse5": {"version": "8.0.3", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "@types/unist": "^3.0.0", "devlop": "^1.0.0", "hastscript": "^9.0.0", "property-information": "^7.0.0", "vfile": "^6.0.0", "vfile-location": "^5.0.0", "web-namespaces": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hast-util-from-parse5/node_modules/property-information": {"version": "7.0.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/hast-util-parse-selector": {"version": "4.0.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hast-util-raw": {"version": "9.1.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "@types/unist": "^3.0.0", "@ungap/structured-clone": "^1.0.0", "hast-util-from-parse5": "^8.0.0", "hast-util-to-parse5": "^8.0.0", "html-void-elements": "^3.0.0", "mdast-util-to-hast": "^13.0.0", "parse5": "^7.0.0", "unist-util-position": "^5.0.0", "unist-util-visit": "^5.0.0", "vfile": "^6.0.0", "web-namespaces": "^2.0.0", "zwitch": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hast-util-raw/node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/hast-util-raw/node_modules/parse5": {"version": "7.2.1", "license": "MIT", "dependencies": {"entities": "^4.5.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/hast-util-to-jsx-runtime": {"version": "2.3.2", "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "@types/hast": "^3.0.0", "@types/unist": "^3.0.0", "comma-separated-tokens": "^2.0.0", "devlop": "^1.0.0", "estree-util-is-identifier-name": "^3.0.0", "hast-util-whitespace": "^3.0.0", "mdast-util-mdx-expression": "^2.0.0", "mdast-util-mdx-jsx": "^3.0.0", "mdast-util-mdxjs-esm": "^2.0.0", "property-information": "^6.0.0", "space-separated-tokens": "^2.0.0", "style-to-object": "^1.0.0", "unist-util-position": "^5.0.0", "vfile-message": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hast-util-to-parse5": {"version": "8.0.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "comma-separated-tokens": "^2.0.0", "devlop": "^1.0.0", "property-information": "^6.0.0", "space-separated-tokens": "^2.0.0", "web-namespaces": "^2.0.0", "zwitch": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hast-util-whitespace": {"version": "3.0.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hastscript": {"version": "9.0.1", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "comma-separated-tokens": "^2.0.0", "hast-util-parse-selector": "^4.0.0", "property-information": "^7.0.0", "space-separated-tokens": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/hastscript/node_modules/property-information": {"version": "7.0.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/html-url-attributes": {"version": "3.0.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/html-void-elements": {"version": "3.0.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/image-size": {"version": "0.5.5", "dev": true, "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/immutable": {"version": "5.0.3", "license": "MIT"}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC", "optional": true}, "node_modules/ini": {"version": "1.3.8", "license": "ISC", "optional": true}, "node_modules/inline-style-parser": {"version": "0.2.4", "license": "MIT"}, "node_modules/input-format": {"version": "0.3.14", "license": "MIT", "dependencies": {"prop-types": "^15.8.1"}, "peerDependencies": {"react": ">=18.1.0", "react-dom": ">=18.1.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}}, "node_modules/internal-slot": {"version": "1.0.7", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.0", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-alphabetical": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/is-alphanumerical": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-alphabetical": "^2.0.0", "is-decimal": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/is-arguments": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.0.4", "license": "MIT", "dependencies": {"has-bigints": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-decimal": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "optional": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-hexadecimal": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-string": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-what": {"version": "3.14.1", "dev": true, "license": "MIT"}, "node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/jsesc": {"version": "3.1.0", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json2mq": {"version": "0.2.0", "license": "MIT", "dependencies": {"string-convert": "^0.2.0"}}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/less": {"version": "4.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=14"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}}, "node_modules/less/node_modules/make-dir": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/less/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/libphonenumber-js": {"version": "1.12.8", "license": "MIT"}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/lodash.memoize": {"version": "4.1.2", "license": "MIT"}, "node_modules/lodash.reduce": {"version": "4.6.0", "license": "MIT"}, "node_modules/lodash.startswith": {"version": "4.2.1", "license": "MIT"}, "node_modules/longest-streak": {"version": "3.1.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "5.1.1", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/lz-string": {"version": "1.5.0", "license": "MIT", "bin": {"lz-string": "bin/bin.js"}}, "node_modules/make-cancellable-promise": {"version": "1.3.2", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/make-cancellable-promise?sponsor=1"}}, "node_modules/make-event-props": {"version": "1.6.2", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/make-event-props?sponsor=1"}}, "node_modules/markdown-table": {"version": "3.0.4", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/marked": {"version": "4.0.12", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 12"}}, "node_modules/mdast-util-find-and-replace": {"version": "3.0.2", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "escape-string-regexp": "^5.0.0", "unist-util-is": "^6.0.0", "unist-util-visit-parents": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mdast-util-from-markdown": {"version": "2.0.2", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "@types/unist": "^3.0.0", "decode-named-character-reference": "^1.0.0", "devlop": "^1.0.0", "mdast-util-to-string": "^4.0.0", "micromark": "^4.0.0", "micromark-util-decode-numeric-character-reference": "^2.0.0", "micromark-util-decode-string": "^2.0.0", "micromark-util-normalize-identifier": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0", "unist-util-stringify-position": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm": {"version": "3.0.0", "license": "MIT", "dependencies": {"mdast-util-from-markdown": "^2.0.0", "mdast-util-gfm-autolink-literal": "^2.0.0", "mdast-util-gfm-footnote": "^2.0.0", "mdast-util-gfm-strikethrough": "^2.0.0", "mdast-util-gfm-table": "^2.0.0", "mdast-util-gfm-task-list-item": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm-autolink-literal": {"version": "2.0.1", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "ccount": "^2.0.0", "devlop": "^1.0.0", "mdast-util-find-and-replace": "^3.0.0", "micromark-util-character": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm-footnote": {"version": "2.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "devlop": "^1.1.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0", "micromark-util-normalize-identifier": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm-strikethrough": {"version": "2.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm-table": {"version": "2.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "devlop": "^1.0.0", "markdown-table": "^3.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-gfm-task-list-item": {"version": "2.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "devlop": "^1.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-mdx-expression": {"version": "2.0.1", "license": "MIT", "dependencies": {"@types/estree-jsx": "^1.0.0", "@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "devlop": "^1.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-mdx-jsx": {"version": "3.2.0", "license": "MIT", "dependencies": {"@types/estree-jsx": "^1.0.0", "@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "@types/unist": "^3.0.0", "ccount": "^2.0.0", "devlop": "^1.1.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0", "parse-entities": "^4.0.0", "stringify-entities": "^4.0.0", "unist-util-stringify-position": "^4.0.0", "vfile-message": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-mdxjs-esm": {"version": "2.0.1", "license": "MIT", "dependencies": {"@types/estree-jsx": "^1.0.0", "@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "devlop": "^1.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-phrasing": {"version": "4.1.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "unist-util-is": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-to-hast": {"version": "13.2.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "@ungap/structured-clone": "^1.0.0", "devlop": "^1.0.0", "micromark-util-sanitize-uri": "^2.0.0", "trim-lines": "^3.0.0", "unist-util-position": "^5.0.0", "unist-util-visit": "^5.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-to-markdown": {"version": "2.1.2", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "@types/unist": "^3.0.0", "longest-streak": "^3.0.0", "mdast-util-phrasing": "^4.0.0", "mdast-util-to-string": "^4.0.0", "micromark-util-classify-character": "^2.0.0", "micromark-util-decode-string": "^2.0.0", "unist-util-visit": "^5.0.0", "zwitch": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-to-string": {"version": "4.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/merge-refs": {"version": "1.3.0", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/merge-refs?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/micromark": {"version": "4.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"@types/debug": "^4.0.0", "debug": "^4.0.0", "decode-named-character-reference": "^1.0.0", "devlop": "^1.0.0", "micromark-core-commonmark": "^2.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-chunked": "^2.0.0", "micromark-util-combine-extensions": "^2.0.0", "micromark-util-decode-numeric-character-reference": "^2.0.0", "micromark-util-encode": "^2.0.0", "micromark-util-normalize-identifier": "^2.0.0", "micromark-util-resolve-all": "^2.0.0", "micromark-util-sanitize-uri": "^2.0.0", "micromark-util-subtokenize": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-core-commonmark": {"version": "2.0.2", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"decode-named-character-reference": "^1.0.0", "devlop": "^1.0.0", "micromark-factory-destination": "^2.0.0", "micromark-factory-label": "^2.0.0", "micromark-factory-space": "^2.0.0", "micromark-factory-title": "^2.0.0", "micromark-factory-whitespace": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-chunked": "^2.0.0", "micromark-util-classify-character": "^2.0.0", "micromark-util-html-tag-name": "^2.0.0", "micromark-util-normalize-identifier": "^2.0.0", "micromark-util-resolve-all": "^2.0.0", "micromark-util-subtokenize": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-extension-gfm": {"version": "3.0.0", "license": "MIT", "dependencies": {"micromark-extension-gfm-autolink-literal": "^2.0.0", "micromark-extension-gfm-footnote": "^2.0.0", "micromark-extension-gfm-strikethrough": "^2.0.0", "micromark-extension-gfm-table": "^2.0.0", "micromark-extension-gfm-tagfilter": "^2.0.0", "micromark-extension-gfm-task-list-item": "^2.0.0", "micromark-util-combine-extensions": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-autolink-literal": {"version": "2.1.0", "license": "MIT", "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-sanitize-uri": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-footnote": {"version": "2.1.0", "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-core-commonmark": "^2.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-normalize-identifier": "^2.0.0", "micromark-util-sanitize-uri": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-strikethrough": {"version": "2.1.0", "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-util-chunked": "^2.0.0", "micromark-util-classify-character": "^2.0.0", "micromark-util-resolve-all": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-table": {"version": "2.1.1", "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-tagfilter": {"version": "2.0.0", "license": "MIT", "dependencies": {"micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-extension-gfm-task-list-item": {"version": "2.1.0", "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/micromark-factory-destination": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-factory-label": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-factory-space": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-factory-title": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-factory-whitespace": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-character": {"version": "2.1.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-chunked": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^2.0.0"}}, "node_modules/micromark-util-classify-character": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-combine-extensions": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-chunked": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-decode-numeric-character-reference": {"version": "2.0.2", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^2.0.0"}}, "node_modules/micromark-util-decode-string": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"decode-named-character-reference": "^1.0.0", "micromark-util-character": "^2.0.0", "micromark-util-decode-numeric-character-reference": "^2.0.0", "micromark-util-symbol": "^2.0.0"}}, "node_modules/micromark-util-encode": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-html-tag-name": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-normalize-identifier": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^2.0.0"}}, "node_modules/micromark-util-resolve-all": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-sanitize-uri": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-encode": "^2.0.0", "micromark-util-symbol": "^2.0.0"}}, "node_modules/micromark-util-subtokenize": {"version": "2.0.4", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"devlop": "^1.0.0", "micromark-util-chunked": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}}, "node_modules/micromark-util-symbol": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-types": {"version": "2.0.1", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "optional": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/min-indent": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "optional": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp-classic": {"version": "0.5.3", "license": "MIT", "optional": true}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/napi-build-utils": {"version": "2.0.0", "license": "MIT", "optional": true}, "node_modules/needle": {"version": "3.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.3", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 4.4.x"}}, "node_modules/node-abi": {"version": "3.74.0", "license": "MIT", "optional": true, "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=10"}}, "node_modules/node-addon-api": {"version": "7.1.1", "license": "MIT", "optional": true}, "node_modules/node-releases": {"version": "2.0.19", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.5", "define-properties": "^1.2.1", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "optional": true, "dependencies": {"wrappy": "1"}}, "node_modules/parse-entities": {"version": "4.0.2", "license": "MIT", "dependencies": {"@types/unist": "^2.0.0", "character-entities-legacy": "^3.0.0", "character-reference-invalid": "^2.0.0", "decode-named-character-reference": "^1.0.0", "is-alphanumerical": "^2.0.0", "is-decimal": "^2.0.0", "is-hexadecimal": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/parse-entities/node_modules/@types/unist": {"version": "2.0.11", "license": "MIT"}, "node_modules/parse-node-version": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/path2d": {"version": "0.2.2", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/path2d-polyfill": {"version": "2.0.1", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=8"}}, "node_modules/pdfjs-dist": {"version": "3.11.174", "license": "Apache-2.0", "peer": true, "engines": {"node": ">=18"}, "optionalDependencies": {"canvas": "^2.11.2", "path2d-polyfill": "^2.0.1"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/possible-typed-array-names": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.5.6", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prebuild-install": {"version": "7.1.3", "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^2.0.0", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=10"}}, "node_modules/prebuild-install/node_modules/decompress-response": {"version": "6.0.0", "license": "MIT", "optional": true, "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/prebuild-install/node_modules/detect-libc": {"version": "2.0.3", "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8"}}, "node_modules/prebuild-install/node_modules/mimic-response": {"version": "3.1.0", "license": "MIT", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/prebuild-install/node_modules/simple-get": {"version": "4.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true, "dependencies": {"decompress-response": "^6.0.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/pretty-format": {"version": "27.5.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/property-information": {"version": "6.5.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/pump": {"version": "3.0.2", "license": "MIT", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "optional": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc-cascader": {"version": "3.28.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "array-tree-filter": "^2.1.0", "classnames": "^2.3.1", "rc-select": "~14.15.0", "rc-tree": "~5.9.0", "rc-util": "^5.37.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-checkbox": {"version": "3.3.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.25.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-collapse": {"version": "3.8.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.3.4", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dialog": {"version": "9.6.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-8", "classnames": "^2.2.6", "rc-motion": "^2.3.0", "rc-util": "^5.21.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-drawer": {"version": "7.2.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@rc-component/portal": "^1.1.1", "classnames": "^2.2.6", "rc-motion": "^2.6.1", "rc-util": "^5.38.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dropdown": {"version": "4.2.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-util": "^5.17.0"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}}, "node_modules/rc-field-form": {"version": "2.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/async-validator": "^5.0.3", "rc-util": "^5.32.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-image": {"version": "7.11.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/portal": "^1.0.2", "classnames": "^2.2.6", "rc-dialog": "~9.6.0", "rc-motion": "^2.6.2", "rc-util": "^5.34.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-input": {"version": "1.6.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.18.1"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/rc-input-number": {"version": "9.2.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/mini-decimal": "^1.0.1", "classnames": "^2.2.5", "rc-input": "~1.6.0", "rc-util": "^5.40.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-mentions": {"version": "2.16.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.22.5", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-input": "~1.6.0", "rc-menu": "~9.15.1", "rc-textarea": "~1.8.0", "rc-util": "^5.34.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-menu": {"version": "9.15.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.0.0", "classnames": "2.x", "rc-motion": "^2.4.3", "rc-overflow": "^1.3.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-motion": {"version": "2.9.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.43.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-notification": {"version": "5.6.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.9.0", "rc-util": "^5.20.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-overflow": {"version": "1.3.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-resize-observer": "^1.0.0", "rc-util": "^5.37.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-pagination": {"version": "4.3.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-picker": {"version": "4.6.15", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.1", "rc-overflow": "^1.3.2", "rc-resize-observer": "^1.4.0", "rc-util": "^5.43.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"date-fns": ">= 2.x", "dayjs": ">= 1.x", "luxon": ">= 3.x", "moment": ">= 2.x", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}}}, "node_modules/rc-progress": {"version": "4.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.6", "rc-util": "^5.16.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-rate": {"version": "2.13.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.0.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-resize-observer": {"version": "1.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.7", "classnames": "^2.2.1", "rc-util": "^5.38.0", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-segmented": {"version": "2.5.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-motion": "^2.4.4", "rc-util": "^5.17.0"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/rc-select": {"version": "14.15.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.1.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-overflow": "^1.3.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-slider": {"version": "11.1.7", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-steps": {"version": "6.0.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.16.7", "classnames": "^2.2.3", "rc-util": "^5.16.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-switch": {"version": "4.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "classnames": "^2.2.1", "rc-util": "^5.30.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-table": {"version": "7.47.5", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/context": "^1.4.0", "classnames": "^2.2.5", "rc-resize-observer": "^1.1.0", "rc-util": "^5.41.0", "rc-virtual-list": "^3.14.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tabs": {"version": "15.3.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "classnames": "2.x", "rc-dropdown": "~4.2.0", "rc-menu": "~9.15.1", "rc-motion": "^2.6.2", "rc-resize-observer": "^1.0.0", "rc-util": "^5.34.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-textarea": {"version": "1.8.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.1", "rc-input": "~1.6.0", "rc-resize-observer": "^1.0.0", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tooltip": {"version": "6.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-tree": {"version": "5.9.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.1"}, "engines": {"node": ">=10.x"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-tree-select": {"version": "5.23.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-select": "~14.15.0", "rc-tree": "~5.9.0", "rc-util": "^5.16.1"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-upload": {"version": "4.8.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.5", "rc-util": "^5.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-util": {"version": "5.43.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "react-is": "^18.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-util/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/rc-virtual-list": {"version": "3.15.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.0", "classnames": "^2.2.6", "rc-resize-observer": "^1.0.0", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/react": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-dropzone": {"version": "14.3.5", "license": "MIT", "dependencies": {"attr-accept": "^2.2.4", "file-selector": "^2.1.0", "prop-types": "^15.8.1"}, "engines": {"node": ">= 10.13"}, "peerDependencies": {"react": ">= 16.8 || 18.0.0"}}, "node_modules/react-icons": {"version": "5.4.0", "license": "MIT", "peerDependencies": {"react": "*"}}, "node_modules/react-intersection-observer": {"version": "9.16.0", "license": "MIT", "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}, "node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/react-markdown": {"version": "9.0.3", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "devlop": "^1.0.0", "hast-util-to-jsx-runtime": "^2.0.0", "html-url-attributes": "^3.0.0", "mdast-util-to-hast": "^13.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.0.0", "unified": "^11.0.0", "unist-util-visit": "^5.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "peerDependencies": {"@types/react": ">=18", "react": ">=18"}}, "node_modules/react-masonry-css": {"version": "1.0.16", "license": "MIT", "peerDependencies": {"react": ">=16.0.0"}}, "node_modules/react-pdf": {"version": "9.2.1", "license": "MIT", "dependencies": {"clsx": "^2.0.0", "dequal": "^2.0.3", "make-cancellable-promise": "^1.3.1", "make-event-props": "^1.6.0", "merge-refs": "^1.3.0", "pdfjs-dist": "4.8.69", "tiny-invariant": "^1.0.0", "warning": "^4.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-pdf?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-pdf/node_modules/canvas": {"version": "3.1.0", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1"}, "engines": {"node": "^18.12.0 || >= 20.9.0"}}, "node_modules/react-pdf/node_modules/pdfjs-dist": {"version": "4.8.69", "license": "Apache-2.0", "engines": {"node": ">=18"}, "optionalDependencies": {"canvas": "^3.0.0-rc2", "path2d": "^0.2.1"}}, "node_modules/react-phone-input-2": {"version": "2.15.1", "license": "MIT", "dependencies": {"classnames": "^2.2.6", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.reduce": "^4.6.0", "lodash.startswith": "^4.2.1", "prop-types": "^15.7.2"}, "peerDependencies": {"react": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0", "react-dom": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0"}}, "node_modules/react-phone-number-input": {"version": "3.4.12", "license": "MIT", "dependencies": {"classnames": "^2.5.1", "country-flag-icons": "^1.5.17", "input-format": "^0.3.10", "libphonenumber-js": "^1.11.20", "prop-types": "^15.8.1"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/react-redux": {"version": "9.1.2", "license": "MIT", "dependencies": {"@types/use-sync-external-store": "^0.0.3", "use-sync-external-store": "^1.0.0"}, "peerDependencies": {"@types/react": "^18.2.25", "react": "^18.0", "redux": "^5.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "redux": {"optional": true}}}, "node_modules/react-refresh": {"version": "0.17.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-router": {"version": "6.27.0", "license": "MIT", "dependencies": {"@remix-run/router": "1.20.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/react-router-dom": {"version": "6.27.0", "license": "MIT", "dependencies": {"@remix-run/router": "1.20.0", "react-router": "6.27.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/react-simplemde-editor": {"version": "5.2.0", "license": "MIT", "dependencies": {"@types/codemirror": "~5.60.5"}, "peerDependencies": {"easymde": ">= 2.0.0 < 3.0.0", "react": ">=16.8.2", "react-dom": ">=16.8.2"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/redent": {"version": "3.0.0", "license": "MIT", "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/redux": {"version": "5.0.1", "license": "MIT"}, "node_modules/redux-thunk": {"version": "3.1.0", "license": "MIT", "peerDependencies": {"redux": "^5.0.0"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.5.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/rehype-raw": {"version": "7.0.0", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "hast-util-raw": "^9.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/remark-gfm": {"version": "4.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-gfm": "^3.0.0", "micromark-extension-gfm": "^3.0.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "unified": "^11.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/remark-parse": {"version": "11.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-from-markdown": "^2.0.0", "micromark-util-types": "^2.0.0", "unified": "^11.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/remark-rehype": {"version": "11.1.1", "license": "MIT", "dependencies": {"@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "mdast-util-to-hast": "^13.0.0", "unified": "^11.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/remark-stringify": {"version": "11.0.0", "license": "MIT", "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-to-markdown": "^2.0.0", "unified": "^11.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/reselect": {"version": "5.1.1", "license": "MIT"}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "license": "MIT"}, "node_modules/rollup": {"version": "4.44.2", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.44.2", "@rollup/rollup-android-arm64": "4.44.2", "@rollup/rollup-darwin-arm64": "4.44.2", "@rollup/rollup-darwin-x64": "4.44.2", "@rollup/rollup-freebsd-arm64": "4.44.2", "@rollup/rollup-freebsd-x64": "4.44.2", "@rollup/rollup-linux-arm-gnueabihf": "4.44.2", "@rollup/rollup-linux-arm-musleabihf": "4.44.2", "@rollup/rollup-linux-arm64-gnu": "4.44.2", "@rollup/rollup-linux-arm64-musl": "4.44.2", "@rollup/rollup-linux-loongarch64-gnu": "4.44.2", "@rollup/rollup-linux-powerpc64le-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-musl": "4.44.2", "@rollup/rollup-linux-s390x-gnu": "4.44.2", "@rollup/rollup-linux-x64-gnu": "4.44.2", "@rollup/rollup-linux-x64-musl": "4.44.2", "@rollup/rollup-win32-arm64-msvc": "4.44.2", "@rollup/rollup-win32-ia32-msvc": "4.44.2", "@rollup/rollup-win32-x64-msvc": "4.44.2", "fsevents": "~2.3.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/sass": {"version": "1.82.0", "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "node_modules/sass/node_modules/chokidar": {"version": "4.0.1", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/sass/node_modules/readdirp": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">= 14.16.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/sax": {"version": "1.4.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/scheduler": {"version": "0.23.2", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/scroll-into-view-if-needed": {"version": "3.1.0", "license": "MIT", "dependencies": {"compute-scroll-into-view": "^3.0.2"}}, "node_modules/semver": {"version": "7.6.3", "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/side-channel": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/simple-concat": {"version": "1.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "optional": true, "peer": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/space-separated-tokens": {"version": "2.0.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/stack-utils": {"version": "2.0.6", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/stop-iteration-iterator": {"version": "1.0.0", "license": "MIT", "dependencies": {"internal-slot": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "optional": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-convert": {"version": "0.2.1", "license": "MIT"}, "node_modules/stringify-entities": {"version": "4.0.4", "license": "MIT", "dependencies": {"character-entities-html4": "^2.0.0", "character-entities-legacy": "^3.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/strip-indent": {"version": "3.0.0", "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/style-to-object": {"version": "1.0.8", "license": "MIT", "dependencies": {"inline-style-parser": "0.2.4"}}, "node_modules/stylis": {"version": "4.3.4", "license": "MIT"}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tar-fs": {"version": "2.1.2", "license": "MIT", "optional": true, "dependencies": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}}, "node_modules/tar-fs/node_modules/chownr": {"version": "1.1.4", "license": "ISC", "optional": true}, "node_modules/tar-stream": {"version": "2.2.0", "license": "MIT", "optional": true, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.36.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true, "peer": true, "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT", "optional": true, "peer": true}, "node_modules/throttle-debounce": {"version": "5.0.2", "license": "MIT", "engines": {"node": ">=12.22"}}, "node_modules/tiny-invariant": {"version": "1.3.3", "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.6", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toggle-selection": {"version": "1.0.6", "license": "MIT"}, "node_modules/trim-lines": {"version": "3.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/trough": {"version": "2.2.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/turndown": {"version": "7.2.0", "license": "MIT", "dependencies": {"@mixmark-io/domino": "^2.2.0"}}, "node_modules/turndown-plugin-gfm": {"version": "1.0.2", "license": "MIT"}, "node_modules/typo-js": {"version": "1.2.5", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/undici-types": {"version": "7.8.0", "license": "MIT"}, "node_modules/unified": {"version": "11.0.5", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "bail": "^2.0.0", "devlop": "^1.0.0", "extend": "^3.0.0", "is-plain-obj": "^4.0.0", "trough": "^2.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/unified/node_modules/is-plain-obj": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/unist-util-is": {"version": "6.0.0", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/unist-util-position": {"version": "5.0.0", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/unist-util-stringify-position": {"version": "4.0.0", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/unist-util-visit": {"version": "5.0.0", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "unist-util-is": "^6.0.0", "unist-util-visit-parents": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/unist-util-visit-parents": {"version": "6.0.1", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "unist-util-is": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/use-sync-external-store": {"version": "1.2.2", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT", "optional": true}, "node_modules/vanilla-colorful": {"version": "0.7.2", "license": "MIT"}, "node_modules/vfile": {"version": "6.0.3", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "vfile-message": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/vfile-location": {"version": "5.0.3", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "vfile": "^6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/vfile-message": {"version": "4.0.2", "license": "MIT", "dependencies": {"@types/unist": "^3.0.0", "unist-util-stringify-position": "^4.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/vite": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.6", "picomatch": "^4.0.2", "postcss": "^8.5.6", "rollup": "^4.40.0", "tinyglobby": "^0.2.14"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^20.19.0 || >=22.12.0", "jiti": ">=1.21.0", "less": "^4.0.0", "lightningcss": "^1.21.0", "sass": "^1.70.0", "sass-embedded": "^1.70.0", "stylus": ">=0.54.8", "sugarss": "^5.0.0", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.6", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/warning": {"version": "4.0.3", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/web-namespaces": {"version": "2.0.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/web-vitals": {"version": "2.1.4", "license": "Apache-2.0"}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC", "optional": true}, "node_modules/yallist": {"version": "3.1.1", "dev": true, "license": "ISC"}, "node_modules/zrender": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "2.3.0"}}, "node_modules/zrender/node_modules/tslib": {"version": "2.3.0", "license": "0BSD"}, "node_modules/zwitch": {"version": "2.0.4", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}}}