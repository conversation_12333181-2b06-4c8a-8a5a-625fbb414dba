{"name": "echo-v2", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^44.1.0", "@ckeditor/ckeditor5-react": "^9.4.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^2.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.21.6", "axios": "^1.7.7", "echarts": "^5.6.0", "file-saver": "^2.0.5", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.3", "react-masonry-css": "^1.0.16", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-simplemde-editor": "^5.2.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "sass": "^1.82.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "less": "^4.3.0", "vite": "^7.0.2"}}