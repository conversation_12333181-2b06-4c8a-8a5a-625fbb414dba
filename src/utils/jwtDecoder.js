export const decodeJwt = (token) => {
  try {
    if (!token) return null;

    // JWT structure: header.payload.signature
    const base64Url = token.split(".")[1];
    if (!base64Url) return null;

    // Replace characters for base64 URL decoding
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");

    // Decode and parse the payload
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error("Error decoding JWT:", error);
    return null;
  }
};
