// src/utils/api.js
import axios from 'axios';
import { USE_DUMMY_DATA } from './config';
import users from '../data/users.json';

export const login = async (username, password) => {
  if (USE_DUMMY_DATA) {
    // Simulate API call with dummy data
    const user = users.find(
      (user) => user.username === username && user.password === password
    );
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        user ? resolve(user) : reject('Invalid credentials');
      }, 500);
    });
  } else {
    // Real API call
    try {
      const response = await axios.post('/api/login', { username, password });
      return response.data;
    } catch (error) {
      throw error.response.data.message;
    }
  }
};
