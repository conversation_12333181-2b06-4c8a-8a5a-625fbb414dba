import { Card, Typography, Button } from "antd";
import {
  FormOutlined,
  MessageOutlined,
  RocketOutlined,
  LeftOutlined,
  RightOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import Stage1BusinessInfo from "./components/Stage1BusinessInfo";
import Stage2AIGoalAndResponse from "./components/Stage2AIGoalAndResponse";
import Stage3Completion from "./components/Stage3Completion";
import "./tenantSetup.redesigned.scss";

const { Title, Text } = Typography;

const TenantSetupComponent = ({
  currentStage,
  setupData,
  loading,
  loadingMessage,
  onStage1Submit,
  onStage2Submit,
  onStage3Submit,
  onStageNavigation,
}) => {
  const steps = [
    {
      number: 1,
      title: "Organization & Agent Setup",
      icon: <FormOutlined />,
    },
    {
      number: 2,
      title: "AI Configuration",
      icon: <MessageOutlined />,
    },
    {
      number: 3,
      title: "Review & Confirmation",
      icon: <RocketOutlined />,
    },
  ];

  const handleNext = () => {
    // Trigger form submission for current stage
    if (currentStage === 1) {
      // Trigger Stage1 form submission
      const stage1Form = document.querySelector(".stage1-business-info form");
      if (stage1Form) {
        const submitEvent = new Event("submit", {
          bubbles: true,
          cancelable: true,
        });
        stage1Form.dispatchEvent(submitEvent);
      }
    } else if (currentStage === 2) {
      // Trigger Stage2 form submission
      const stage2Form = document.querySelector(
        ".stage2-ai-goal-response form"
      );
      if (stage2Form) {
        const submitEvent = new Event("submit", {
          bubbles: true,
          cancelable: true,
        });
        stage2Form.dispatchEvent(submitEvent);
      }
    } else if (currentStage < 3) {
      onStageNavigation("next");
    }
  };

  const handlePrevious = () => {
    if (currentStage > 1) {
      onStageNavigation("back");
    }
  };

  const handleComplete = () => {
    // Trigger Stage3 completion
    onStage3Submit();
  };

  const renderStageContent = () => {
    switch (currentStage) {
      case 1:
        return (
          <Stage1BusinessInfo
            initialData={setupData.stage1}
            onSubmit={onStage1Submit}
            loading={loading}
            loadingMessage={loadingMessage}
          />
        );
      case 2:
        return (
          <Stage2AIGoalAndResponse
            stage1Response={setupData.stage1Response}
            onSubmit={onStage2Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      case 3:
        return (
          <Stage3Completion
            onComplete={onStage3Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="tenant-setup-redesigned">
      <div className="tenant-setup-redesigned__container">
        {/* Header */}
        <div className="tenant-setup-redesigned__header">
          <Title level={1} className="tenant-setup-redesigned__title">
            Getting Things Ready
          </Title>
          <Text className="tenant-setup-redesigned__subtitle">
            Setup your organization information and configure your AI assistant in a few simple steps
          </Text>
        </div>

        {/* Progress Steps */}
        <div className="tenant-setup-redesigned__progress">
          <div className="tenant-setup-redesigned__steps-container">
            {steps.map((step, index) => (
              <div
                key={step.number}
                className="tenant-setup-redesigned__step-wrapper"
              >
                <div className="tenant-setup-redesigned__step">
                  <div
                    className={`tenant-setup-redesigned__step-circle ${
                      step.number === currentStage
                        ? "tenant-setup-redesigned__step-circle--current"
                        : step.number < currentStage
                        ? "tenant-setup-redesigned__step-circle--completed"
                        : "tenant-setup-redesigned__step-circle--pending"
                    }`}
                  >
                    {step.number < currentStage ? (
                      <CheckOutlined className="tenant-setup-redesigned__step-icon" />
                    ) : (
                      step.number
                    )}
                  </div>
                  <div className="tenant-setup-redesigned__step-content">
                    <div
                      className={`tenant-setup-redesigned__step-title ${
                        step.number === currentStage
                          ? "tenant-setup-redesigned__step-title--current"
                          : ""
                      }`}
                    >
                      {step.title}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`tenant-setup-redesigned__step-connector ${
                      step.number < currentStage
                        ? "tenant-setup-redesigned__step-connector--completed"
                        : ""
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="tenant-setup-redesigned__form-container">
          <Card className="tenant-setup-redesigned__form-card">
            <div className="tenant-setup-redesigned__form-content">
              {renderStageContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="tenant-setup-redesigned__navigation">
              <Button
                type="default"
                onClick={handlePrevious}
                disabled={currentStage === 1 || loading}
                className="tenant-setup-redesigned__nav-button tenant-setup-redesigned__nav-button--prev"
                icon={<LeftOutlined />}
              >
                Previous
              </Button>

              {currentStage < 3 ? (
                <Button
                  type="primary"
                  onClick={handleNext}
                  loading={loading}
                  disabled={loading}
                  className="tenant-setup-redesigned__nav-button tenant-setup-redesigned__nav-button--next"
                >
                  {loading ? (loadingMessage || "Loading...") : "Next"}
                  {!loading && <RightOutlined />}
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={handleComplete}
                  loading={loading}
                  disabled={loading}
                  className="tenant-setup-redesigned__nav-button tenant-setup-redesigned__nav-button--complete"
                  icon={!loading ? <CheckOutlined /> : undefined}
                >
                  {loading ? "Launching..." : "Complete Setup & Launch"}
                </Button>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TenantSetupComponent;
