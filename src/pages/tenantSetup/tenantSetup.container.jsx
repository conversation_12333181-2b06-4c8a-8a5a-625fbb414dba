import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import TenantSetupComponent from "./tenantSetup.component";
import { useAppDispatch } from "../../hooks/reduxHooks";
import {
  getTenantSetupCheckApi,
  updateTenantStageOneApi,
  addDummyDataApi,
} from "../../services/tenantSetup.service";

const TenantSetupContainer = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // State management for three-stage setup
  const [currentStage, setCurrentStage] = useState(1);
  const [setupData, setSetupData] = useState({
    stage1: {},
    stage2: {},
    stage3: {},
  });
  const [loading, setLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("");

  // Check if tenant setup is already completed
  useEffect(() => {
    const setupCompleted = localStorage.getItem("tenantSetupCompleted");
    if (setupCompleted === "true") {
      navigate("/dashboard");
    }
  }, [navigate]);

  // Handle stage 1 form submission
  const handleStage1Submit = (formData) => {
    setLoading(true);
    setLoadingMessage("Reading your input...");

    // Prepare data according to the required format
    const stage1ApiData = {
      org_name: formData.org_name,
      org_type: formData.org_type,
      org_description: formData.org_description,
      org_goal: formData.org_description, // Same as org_description
      org_contact: formData.org_contact,
      org_email: formData.org_email,
      agent_name: formData.agent_name || "AI Bot",
      agent_goal: formData.agent_goal,
      agent_goal_type: formData.agent_goal_type,
      additional_agent_goal: formData.additional_agent_goal || "",
      language: formData.language || ["Auto Detect"],
    };

    // Step 1: Show "Reading your input..." for 1 second
    setTimeout(() => {
      setLoadingMessage("Setting up knowledge base...");

      // Step 2: Call addDummyDataApi first
      dispatch(
        addDummyDataApi({
          params: {
            org_type: formData.org_type,
          },
          finalCallback: () => {
            // Don't set loading to false here, continue with stage one API
          },
          successCallback: (dummyDataResponse) => {
            // Dummy data created successfully, now check knowledge base
            setLoadingMessage("Checking knowledge base...");

            setTimeout(() => {
              setLoadingMessage("Generating sample questions...");

              // Step 3: Call updateTenantStageOneApi
              dispatch(
                updateTenantStageOneApi({
                  params: stage1ApiData,
                  finalCallback: () => {
                    // Don't set loading to false here, continue with finalizing
                  },
                  successCallback: (response) => {
                    // Stage 1 API completed, now finalize
                    setLoadingMessage("Finalizing...");

                    setTimeout(() => {
                      setLoading(false);
                      setLoadingMessage("");
                      message.success("Setup completed successfully!");
                      setSetupData((prev) => ({
                        ...prev,
                        stage1: formData,
                        stage1Response: response,
                        dummyDataResponse: dummyDataResponse,
                      }));
                      setCurrentStage(2); // Proceed to stage 2
                    }, 1000); // Finalizing for 1 second
                  },
                  failureCallback: () => {
                    setLoading(false);
                    setLoadingMessage("");
                    message.error("Failed to complete stage 1 setup. Please try again.");
                  },
                })
              );
            }, 1000); // Checking knowledge base for 1 second
          },
          failureCallback: () => {
            setLoading(false);
            setLoadingMessage("");
            message.error("Failed to set up knowledge base. Please try again.");
          },
        })
      );
    }, 1000); // Reading your input for 1 second
  };

  // Handle stage 2 AI goal and response selection
  const handleStage2Submit = (stage2Data) => {
    setLoading(true);

    // Simulate API call or add actual API call here
    setTimeout(() => {
      setSetupData((prev) => ({
        ...prev,
        stage2: stage2Data,
      }));
      setCurrentStage(3);
      setLoading(false);
    }, 500); // Minimal delay to show loading state
  };

  // Handle final setup completion
  const handleStage3Submit = (confirmationData) => {
    // Transform data to match the required structure
    // const finalSetupData = {
    //   org_name: setupData.stage1?.org_name,
    //   org_type: setupData.stage1?.org_type,
    //   org_description: setupData.stage1?.org_description,
    //   org_goal: setupData.stage1?.org_goal, // Same as org_description
    //   org_contact: setupData.stage1?.org_contact,
    //   agent_name: setupData.stage1?.agent_name || "AI Bot",
    //   agent_role: setupData.stage1?.agent_role,
    //   language: setupData.stage1?.language || ["English"],
    //   prompt: setupData.stage2?.prompt,
    //   tools: setupData.stage2?.tools || [],
    //   selectedResponse: setupData.stage2?.selectedResponse,
    //   completedAt: new Date().toISOString(),
    //   ...confirmationData,
    // };

    // setLoading(true);

    // // Complete setup
    // setTimeout(() => {
    //   message.success("🎉 Tenant setup completed successfully!");
    //   localStorage.setItem("tenantSetupCompleted", "true");
    //   localStorage.setItem("tenantSetupData", JSON.stringify(finalSetupData));
    //   setLoading(false);
    //   navigate("/dashboard");
    // }, 2000);

    dispatch(
      getTenantSetupCheckApi({
        params: {},
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          message.success("Tenant setup completed successfully!");
          localStorage.setItem("tenantSetupCompleted", "true");
          setLoading(false);
          navigate("/dashboard");
        },
        failureCallback: () => {
          message.error(
            "Tenant setup not completed. Check your data and try again."
          );
        },
      })
    );
  };

  // Handle navigation between stages
  const handleStageNavigation = (direction) => {
    if (direction === "next" && currentStage < 3) {
      setCurrentStage(currentStage + 1);
    } else if (direction === "back" && currentStage > 1) {
      setCurrentStage(currentStage - 1);
    }
  };

  return (
    <TenantSetupComponent
      currentStage={currentStage}
      setupData={setupData}
      loading={loading}
      loadingMessage={loadingMessage}
      onStage1Submit={handleStage1Submit}
      onStage2Submit={handleStage2Submit}
      onStage3Submit={handleStage3Submit}
      onStageNavigation={handleStageNavigation}
    />
  );
};

export default TenantSetupContainer;
