// Redesigned Tenant Setup Styles - Matching TenantLayout.tsx Pattern
.tenant-setup-redesigned {
  min-height: 100vh;
  // background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%, #489eff 100%);
  background: linear-gradient(
    181deg,
    rgba(0, 0, 255, 0.04),
    rgba(0, 0, 255, 0.03)
  );
  -webkit-background: linear-gradient(
    181deg,
    rgba(0, 0, 255, 0.04),
    rgba(0, 0, 255, 0.03)
  );
  -moz-background: linear-gradient(
    181deg,
    rgba(0, 0, 255, 0.04),
    rgba(0, 0, 255, 0.03)
  );
  padding: 32px 16px;

  &__container {
    max-width: 1400px;
    margin: 0 auto;
    width: 95%;
  }

  // Header Section
  &__header {
    text-align: center;
    margin-bottom: 48px;
  }

  &__title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #1a1a1a !important;
    margin-bottom: 8px !important;
    letter-spacing: -0.5px;
  }

  &__subtitle {
    color: #666 !important;
    font-size: 1rem;
    font-weight: 400;
  }

  // Progress Steps Section
  &__progress {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
  }

  &__steps-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 40px;
  }

  &__step-wrapper {
    display: flex;
    align-items: center;
  }

  &__step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 180px;
  }

  &__step-circle {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    font-weight: 700;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 8px;
    position: relative;
    z-index: 2;

    &--pending {
      background: linear-gradient(135deg, #f8fafc, #e2e8f0);
      color: #64748b;
      border: 2px solid #e2e8f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    &--current {
      background: linear-gradient(135deg, #1e40af, #1d4ed8);
      color: white;
      border: 2px solid #2563eb;
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
        0 4px 12px rgba(59, 130, 246, 0.3);
      transform: scale(1.05);
    }

    &--completed {
      background: linear-gradient(135deg, #0f766e, #059669);
      color: white;
      border: 2px solid #059669;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
    }
  }

  &__step-icon {
    font-size: 16px;
    font-weight: 700;
  }

  &__step-content {
    text-align: center;
    transition: all 0.3s ease;
  }

  &__step-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    line-height: 1.3;
    transition: all 0.3s ease;
    max-width: 180px;
    word-wrap: break-word;
    hyphens: auto;

    &--current {
      color: #1e40af;
      font-weight: 700;
      transform: translateY(-1px);
    }
  }

  &__step-connector {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, #e5e7eb, #d1d5db);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1px;
    align-self: center;
    margin: 0 32px;
    margin-top: -26px; // Offset to align with step circles
    z-index: 1;
    min-width: 120px;

    &--completed {
      background: linear-gradient(90deg, #0f766e, #059669);
      box-shadow: 0 1px 4px rgba(16, 185, 129, 0.2);
    }
  }

  // Form Section
  &__form-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }

  &__form-card {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    overflow: hidden;

    .ant-card-body {
      padding: 0 !important;
    }
  }

  &__form-content {
    padding: 32px;
    min-height: 400px;
  }

  // Navigation Section
  &__navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }

  &__nav-button {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
    padding: 0 24px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &--prev {
      background: white;
      border: 1px solid #d9d9d9;
      color: #666;

      &:hover:not(:disabled) {
        border-color: #40a9ff;
        color: #40a9ff;
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    &--next,
    &--complete {
      background: #1890ff;
      border: 1px solid #1890ff;
      color: white;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
        transform: translateY(-1px);
      }

      // Fix for loading state text color
      &.ant-btn-loading {
        color: white !important;

        .ant-btn-loading-icon {
          color: white !important;
        }
      }
    }

    &--complete {
      background: #52c41a;
      border-color: #52c41a;

      &:hover {
        background: #73d13d;
        border-color: #73d13d;
      }

      // Fix for loading state text color
      &.ant-btn-loading {
        color: white !important;

        .ant-btn-loading-icon {
          color: white !important;
        }
      }
    }
  }
}

// Stage Content Styling
.tenant-setup-redesigned {
  // Remove extra margins from stage components
  .stage1-business-info,
  .stage2-ai-goal-response,
  .stage3-completion {
    &__header {
      margin-bottom: 24px;
      text-align: center;

      .ant-typography {
        margin-bottom: 8px !important;
      }
    }
  }

  // Form styling within the card
  .ant-form {
    .ant-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-form-item-label {
      padding-bottom: 6px;

      label {
        font-weight: 500;
        color: #262626;
      }
    }
  }

  // Input styling
  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-picker {
    border-radius: 6px !important;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.2s ease !important;

    &:hover {
      border-color: #40a9ff !important;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
    }
  }

  // Remove inner border for input with prefix icon
  .ant-input-affix-wrapper {
    .ant-input {
      border: none !important;
      box-shadow: none !important;

      &:focus {
        border: none !important;
        box-shadow: none !important;
      }
    }
  }

  // Button styling within forms
  .ant-btn {
    border-radius: 6px;
    transition: all 0.2s ease;

    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
        transform: translateY(-1px);
      }
    }
  }

  // Card styling within stages
  .ant-card {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f0f0f0 !important;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 20px;

      .ant-card-head-title {
        font-size: 15px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // View Example Button Styling
  .view-example-btn {
    &.ant-btn {
      transition: all 0.2s ease !important;

      &:hover,
      &:focus {
        color: #1890ff !important;
        border-color: #1890ff !important;
        background-color: #f6f9ff !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.15) !important;
      }

      .anticon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .tenant-setup-redesigned {
    padding: 16px 8px;

    &__title {
      font-size: 1.5rem !important;
    }

    &__progress {
      margin-bottom: 24px;
      max-width: 100%;
    }

    &__steps-container {
      padding: 0 16px;
      justify-content: space-between;
    }

    &__step {
      min-width: 100px;
    }

    &__step-circle {
      width: 36px;
      height: 36px;
      font-size: 13px;
      margin-bottom: 6px;
    }

    &__step-connector {
      margin: 0 16px;
      margin-top: -20px;
      min-width: 60px;
    }

    &__step-title {
      font-size: 11px;
      max-width: 100px;
    }

    &__form-content {
      padding: 24px 16px;
    }

    &__navigation {
      padding: 16px;
      flex-direction: column;
      gap: 12px;

      .tenant-setup-redesigned__nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .tenant-setup-redesigned {
    &__steps-container {
      flex-direction: column;
      gap: 20px;
      align-items: center;
    }

    &__step-wrapper {
      flex-direction: column;
      width: 100%;
      max-width: 220px;
    }

    &__step {
      width: 100%;
    }

    &__step-connector {
      width: 2px;
      height: 24px;
      margin: 8px 0;
      margin-top: 8px;
      align-self: center;
    }

    &__step-title {
      font-size: 13px;
      white-space: normal;
      max-width: 200px;
    }
  }
}
