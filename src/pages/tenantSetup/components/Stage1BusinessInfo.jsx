import { useState, useEffect } from "react";
import {
  Form,
  Input,
  Select,
  Row,
  Col,
  Typography,
  Space,
  Card,
  Tooltip,
  Button,
  message,
  Modal,
  Tag,
} from "antd";
import {
  BankOutlined,
  ExclamationCircleFilled,
  RetweetOutlined,
  RobotOutlined,
  MailOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import PhoneInput from "react-phone-input-2";
import {
  isPossiblePhoneNumber,
  parsePhoneNumber,
} from "react-phone-number-input";
import "react-phone-input-2/lib/style.css";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  getBusinessInfoApi,
  getTenantLanguageListApi,
  getTenantOrgTypesApi,
} from "../../../services/tenantSetup.service";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Stage1BusinessInfo = ({ initialData, onSubmit, loading, loadingMessage }) => {
  const [form] = Form.useForm();
  const [phoneError, setPhoneError] = useState("");
  const [organizationTypes, setOrganizationType] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [phoneInputValue, setPhoneInputValue] = useState("");
  const [primaryLanguage, setPrimaryLanguage] = useState(null);
  const dispatch = useAppDispatch();

  // Utility function to parse phone number and extract country code
  const parsePhoneNumberData = (phoneNumber) => {
    if (!phoneNumber) {
      return {
        phone_input: "",
        country_code: "",
        phone_number: "",
      };
    }

    try {
      // If the phone number doesn't start with +, add it
      const formattedPhone = phoneNumber.startsWith("+")
        ? phoneNumber
        : `+${phoneNumber}`;

      // Parse the phone number
      const parsed = parsePhoneNumber(formattedPhone);

      if (parsed && parsed.country && parsed.nationalNumber) {
        return {
          phone_input: phoneNumber, // Keep original format for PhoneInput
          country_code: `+${parsed.countryCallingCode}`,
          phone_number: parsed.nationalNumber,
        };
      }
    } catch (error) {
      // Error parsing phone number, will use fallback
    }

    // Fallback - return the original number as phone_input
    return {
      phone_input: phoneNumber,
      country_code: "",
      phone_number: phoneNumber.replace(/^\+/, ""), // Remove + if present for fallback
    };
  };

  useEffect(() => {
    (async () => {
      dispatch(
        getTenantOrgTypesApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setOrganizationType(response);
          },
          failureCallback: () => {},
        })
      );

      dispatch(
        getTenantLanguageListApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setLanguages(response);
          },
          failureCallback: () => {},
        })
      );
    })();
  }, [dispatch]);

  const selectedOrgType = Form.useWatch("org_type", form);

  useEffect(() => {
    if (!selectedOrgType || !organizationTypes.length) return;

    const selectedType = organizationTypes.find(
      (t) => t.name === selectedOrgType
    );

    if (!selectedType?.agent_goals?.length) return;

    // Set the first agent goal as default
    const defaultGoal = selectedType.agent_goals[0];
    form.setFieldValue("agent_goal_type", defaultGoal.prompt_type);
  }, [organizationTypes, selectedOrgType, form]);

  useEffect(() => {
    if (initialData) {
      let formInitialValues = {
        agent_name: "AI Bot",
        language: ["Auto Detect"],
        ...initialData,
      };

      // Handle org_contact field from initialData if present
      if (initialData.org_contact && !initialData.phone_input) {
        const phoneData = parsePhoneNumberData(initialData.org_contact);
        formInitialValues = {
          ...formInitialValues,
          ...phoneData,
        };
        setPhoneInputValue(initialData.org_contact);
      } else {
        // Use existing phone data if available
        formInitialValues = {
          ...formInitialValues,
          country_code: initialData.country_code || "",
          phone_number: initialData.phone_number || "",
          phone_input: initialData.phone_input || "",
        };

        // Set phone input value state to ensure PhoneInput displays correctly
        if (initialData.phone_input) {
          setPhoneInputValue(initialData.phone_input);
        }
      }

      form.setFieldsValue(formInitialValues);

      // Set primary language (first language in the array is considered primary)
      const languageArray = initialData.language || [];
      if (languageArray.length > 0 && languageArray[0] !== "Auto Detect") {
        setPrimaryLanguage(languageArray[0]);
      }
    }
  }, [initialData, form]);

  useEffect(() => {
    (() => {
      // getBusinessInfoApi call this and get the business data and populate the form
      dispatch(
        getBusinessInfoApi({
          finalCallback: () => {},
          successCallback: (response) => {
            // Parse org_contact to populate phone fields
            if (response.org_contact) {
              const phoneNumber = response.org_contact;
              const phoneData = parsePhoneNumberData(phoneNumber);

              // Set the phone input value for the PhoneInput component
              setPhoneInputValue(phoneNumber);

              // Parse and set form values with proper phone field mapping
              const formData = {
                ...response,
                ...phoneData,
              };

              form.setFieldsValue(formData);

              // Set primary language (first language in the array is considered primary)
              const languageArray = response.language || [];
              if (languageArray.length > 0 && languageArray[0] !== "Auto Detect") {
                setPrimaryLanguage(languageArray[0]);
              }
            } else {
              form.setFieldsValue(response);

              // Set primary language (first language in the array is considered primary)
              const languageArray = response.language || [];
              if (languageArray.length > 0 && languageArray[0] !== "Auto Detect") {
                setPrimaryLanguage(languageArray[0]);
              }
            }
          },
          failureCallback: () => {},
        })
      );
    })();
  }, [dispatch, form]);

  const handleSubmit = (values) => {
    // Validate phone number if provided
    if (values.phone_input) {
      const phoneToValidate = values.phone_input.startsWith("+")
        ? values.phone_input
        : `+${values.phone_input}`;

      const isPhoneNumberValid = isPossiblePhoneNumber(phoneToValidate);

      if (!isPhoneNumberValid) {
        setPhoneError("Please enter a valid phone number");
        return;
      }
    }

    setPhoneError("");

    // Prepare data with org_goal same as org_description
    const formData = {
      org_name: values.org_name,
      org_type: values.org_type,
      org_email: values.org_email,
      org_description: values.org_description,
      org_goal: values.org_description, // Same value as org_description
      org_contact: values.phone_input || "", // Use full formatted phone number
      agent_name: values.agent_name || "AI Bot",
      agent_goal_type: values.agent_goal_type,
      additional_agent_goal: values.additional_agent_goal || "",
      language: (() => {
        const selectedLanguages = values.language || ["Auto Detect"];
        if (!primaryLanguage || !selectedLanguages.includes(primaryLanguage)) {
          return selectedLanguages;
        }
        // Place primary language first
        const otherLanguages = selectedLanguages.filter(lang => lang !== primaryLanguage);
        return [primaryLanguage, ...otherLanguages];
      })(),
    };

    onSubmit(formData);
  };

  return (
    <div className="stage1-business-info">
      <div className="stage1-business-info__header">
        <Title
          level={2}
          style={{ marginBottom: 8, color: "#262626", textAlign: "center" }}
        >
          Organization & Agent Setup
        </Title>
        <Paragraph
          style={{
            textAlign: "center",
            fontSize: "15px",
            color: "#8c8c8c",
            marginBottom: 24,
          }}
        >
          Set up your organization details and configure your AI agent
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={loading}
        initialValues={{
          agent_name: "AI Bot",
          language: ["Auto Detect"],
          ...initialData,
          // Handle phone number defaults - if org_contact exists but phone_input doesn't, parse it
          ...(initialData?.org_contact && !initialData?.phone_input
            ? parsePhoneNumberData(initialData.org_contact)
            : {
                country_code: initialData?.country_code || "",
                phone_number: initialData?.phone_number || "",
                phone_input: initialData?.phone_input || "",
              }),
        }}
        size="large"
        requiredMark={false}
      >
        {/* Organization Information Section */}
        <Card
          title={
            <Space>
              <BankOutlined style={{ color: "#1890ff", fontSize: 18 }} />
              <Text strong style={{ fontSize: 18 }}>
                Organization Information
              </Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 20]} style={{ padding: 24 }}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="org_name"
                label={
                  <span>
                    Organization Name <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please enter your organization name",
                  },
                ]}
              >
                <Input
                  prefix={<BankOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter your organization name"
                  className="professional-input"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="org_type"
                label={
                  <span>
                    Organization Type <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please select your organization type",
                  },
                ]}
              >
                <Select
                  placeholder="Select organization type"
                  className="professional-select"
                >
                  {organizationTypes.map((orgType) => (
                    <Option key={orgType.name} value={orgType.name}>
                      {orgType.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="org_description"
                label={
                  <span>
                    Organization Description{" "}
                    <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please describe your organization",
                  },
                ]}
              >
                <TextArea
                  placeholder="Briefly describe what your organization does..."
                  rows={4}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="phone_input"
                label={
                  <span>
                    Organization Contact Number{" "}
                    <span style={{ color: "red" }}>*</span>
                  </span>
                }
                validateStatus={phoneError ? "error" : ""}
                help={phoneError || ""}
              >
                <PhoneInput
                  country={"np"}
                  enableSearch={true}
                  inputClass="form-input"
                  containerClass="phone-input-container"
                  buttonClass="country-dropdown"
                  searchClass="country-search"
                  dropdownClass="country-dropdown-list"
                  disabled={loading}
                  value={
                    phoneInputValue || form.getFieldValue("phone_input") || ""
                  }
                  inputStyle={{
                    width: "100%",
                    height: "40px",
                    fontSize: "14px",
                    fontWeight: "400",
                    borderRadius: "0 8px 8px 0",
                    backgroundColor: loading ? "#f5f5f5" : "#ffffff",
                    color: loading ? "#00000040" : "#000000",
                    cursor: loading ? "not-allowed" : "text",
                  }}
                  buttonStyle={{
                    borderRadius: "8px 0 0 8px",
                    borderRight: "none",
                    backgroundColor: loading ? "#f5f5f5" : "#fafafa",
                    cursor: loading ? "not-allowed" : "pointer",
                  }}
                  onChange={(phone, countryData) => {
                    setPhoneError("");
                    setPhoneInputValue(phone);
                    const countryCode = countryData.dialCode
                      ? `+${countryData.dialCode}`
                      : "";
                    const fullNumber = phone;
                    const phoneNumberOnly = countryData.dialCode
                      ? fullNumber.replace(countryData.dialCode, "")
                      : fullNumber;
                    const cleanedPhoneNumber = phoneNumberOnly.replace(
                      /\D/g,
                      ""
                    );

                    form.setFieldsValue({
                      country_code: countryCode,
                      phone_number: cleanedPhoneNumber,
                      phone_input: phone,
                    });
                  }}
                  preferredCountries={["in", "us", "np", "gb"]}
                  autoFormat={true}
                  placeholder="Enter your organization contact number"
                  disableSearchIcon={false}
                  searchPlaceholder="Search country"
                />
              </Form.Item>

              {/* Hidden fields for country code and phone number */}
              <Form.Item name="country_code" hidden={true}>
                <Input />
              </Form.Item>

              <Form.Item name="phone_number" hidden={true}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="org_email"
                label={
                  <span>
                    Organization Email <span style={{ color: "red" }}>*</span>
                  </span>
                }
                // rules={[
                //   {
                //     required: true,
                //     message: "Please enter your organization email",
                //   },
                // ]}
                // validate the email
                rules={[
                  {
                    type: "email",
                    message: "Please enter a valid email address",
                  },
                  {
                    required: true,
                    message: "Please enter your organization email",
                  },
                ]}
              >
                <Input
                  type="email"
                  prefix={<MailOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter your organization email"
                  className="professional-input"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Agent Setup Section */}
        <Card
          title={
            <Space>
              <RobotOutlined style={{ color: "#52c41a", fontSize: 18 }} />
              <Text strong style={{ fontSize: 18 }}>
                AI Agent Setup
              </Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 20]} style={{ padding: 24 }}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="agent_name"
                label={
                  <span>
                    Agent Name <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please enter the agent name",
                  },
                ]}
              >
                <Input
                  prefix={<RobotOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter agent name"
                  // className="professional-input"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="language"
                label={
                  <span>
                    Language <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please select at least one language",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select languages"
                  className="professional-select"
                  value={form.getFieldValue("language") || []}
                  onChange={(selected) => {
                    if (
                      selected.includes("Auto Detect") &&
                      selected.length > 1
                    ) {
                      if (selected[selected.length - 1] === "Auto Detect") {
                        form.setFieldsValue({ language: ["Auto Detect"] });
                        setPrimaryLanguage(null);
                      } else {
                        const filteredSelected = selected.filter((v) => v !== "Auto Detect");
                        form.setFieldsValue({ language: filteredSelected });
                        // If primary was removed, reset it
                        if (!filteredSelected.includes(primaryLanguage)) {
                          setPrimaryLanguage(filteredSelected[0] || null);
                        }
                      }
                    } else {
                      form.setFieldsValue({ language: selected });
                      // If primary was removed, reset it
                      if (!selected.includes(primaryLanguage)) {
                        setPrimaryLanguage(selected[0] || null);
                      }
                    }
                  }}
                  showSearch
                  optionFilterProp="children"
                  dropdownStyle={{ minWidth: 200 }}
                  tagRender={(props) => {
                    const { label, value, closable, onClose } = props;
                    const selectedLanguages = form.getFieldValue("language") || [];
                    const isPrimary = value === primaryLanguage;
                    const showPrimaryOption = selectedLanguages.length > 1 &&
                                            value !== "Auto Detect" &&
                                            selectedLanguages.includes(value);

                    return (
                      <Tag
                        color={isPrimary ? 'blue' : 'default'}
                        closable={closable}
                        onClose={onClose}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginRight: 4,
                          marginBottom: 4,
                          marginTop: 4,
                        }}
                      >
                        {label}
                        {showPrimaryOption && !isPrimary && (
                          <Tooltip title="The primary language will be used to respond when the user sends a message in a language other than the selected ones.">
                            <Button
                              size="small"
                              type="link"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setPrimaryLanguage(value);
                              }}
                              onMouseDown={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                              style={{
                                marginLeft: 8,
                                fontSize: 12,
                                color: '#1890ff',
                                cursor: 'pointer',
                              }}
                            >
                              Set Primary
                            </Button>
                          </Tooltip>
                        )}
                        {isPrimary && <CheckOutlined style={{ marginLeft: 8 }} />}
                      </Tag>
                    );
                  }}
                >
                  <Select.OptGroup label="Smart Options">
                    <Option value="Auto Detect">🌐 Auto Detect</Option>
                  </Select.OptGroup>
                  <Select.OptGroup label="Languages">
                    {languages.map((lang) => (
                      <Option key={lang} value={lang}>
                        {lang}
                      </Option>
                    ))}
                  </Select.OptGroup>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="agent_goal_type"
                label={
                  <span>
                    Agent Goal <span style={{ color: "red" }}>*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: "Please select an agent goal type",
                  },
                ]}
              >
                <Select
                  placeholder="Select agent goal type"
                  className="professional-select"
                >
                  {selectedOrgType &&
                    organizationTypes
                      .find((t) => t.name === selectedOrgType)
                      ?.agent_goals?.map((goal) => (
                        <Option key={goal.prompt_type} value={goal.prompt_type}>
                          {goal.prompt_summary}
                        </Option>
                      ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="additional_agent_goal"
                label="Additional Agent Instruction"
              >
                <TextArea
                  placeholder="Describe any additional goals or behaviors for your AI agent..."
                  rows={3}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default Stage1BusinessInfo;
