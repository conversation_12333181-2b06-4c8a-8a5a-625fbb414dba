import { useState, useEffect } from "react";

// Add styles for suggestion tags and response style cards
const componentStyles = `
  .suggestion-tag:hover {
    background-color: #e6f7ff !important;
    border-color: #1890ff !important;
    color: #1890ff !important;
    transform: translateY(-1px);
  }

  .response-style-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 32px rgba(0,0,0,0.08) !important;
  }

  .response-style-card.selected:hover {
    transform: translateY(-6px) !important;
  }

  .view-example-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = componentStyles;
  document.head.appendChild(styleElement);
}
import {
  Card,
  Typography,
  Space,
  Form,
  Select,
  message,
  Modal,
  Button,
  Input,
  Tag,
} from "antd";
import {
  CheckOutlined,
  BulbOutlined,
  <PERSON>boltOutlined,
  CloseOutlined as CloseIcon,
  EyeOutlined,
  LeftOutlined,
  RightOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  getBusinessInfoApi,
  getTenantToolsApi,
  updateTenantStageTwoApi,
} from "../../../services/tenantSetup.service";
import { useAppDispatch } from "../../../hooks/reduxHooks";
// import { Icon } from "@iconify/react";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const Stage2AIGoalAndResponse = ({ stage1Response, onSubmit }) => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [ctaToolsList, setCtaToolsList] = useState([]);
  const [businessInfo, setBusinessInfo] = useState(null);

  useEffect(() => {
    (() => {
      dispatch(
        getBusinessInfoApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setBusinessInfo(response);
            form.setFieldsValue({
              tools: response.used_tools || [],
              ticket_required_info: response.ticket_required_info || [], // Keep all fields in form data
              AI_greeting: response.AI_greeting !== undefined ? response.AI_greeting : false,
            });
            setSelectedResponse(response.preferred_prompt);
          },
          failureCallback: () => {},
        })
      );
    })();
  }, [dispatch, form]);

  useEffect(() => {
    (async () => {
      dispatch(
        getTenantToolsApi({
          finalCallback: () => {},
          successCallback: (response) => {
            console.log("cta tools", response);
            setCtaToolsList(response);
          },
          failureCallback: () => {},
        })
      );
    })();
  }, [dispatch]);

  // Console log the stage 1 response when component mounts or stage1Response changes
  useEffect(() => {
    if (stage1Response) {
      console.log(
        "Stage 1 API Response in Stage 2:",
        JSON.stringify(stage1Response, null, 2)
      );
    }
  }, [stage1Response]);

  // Get AI responses from stage1Response (API response)
  const getAIResponses = () => {
    if (!stage1Response?.prompt_variants) return [];

    return stage1Response.prompt_variants.map((variant) => ({
      id: variant.style,
      title: variant.style.charAt(0).toUpperCase() + variant.style.slice(1),
      description: getStyleDescription(variant.style),
      icon: getStyleIcon(variant.style),
      question: variant.user_query,
      response: variant.response_example,
    }));
  };

  const getStyleDescription = (style) => {
    switch (style) {
      case "simplified":
        return "Clear, concise answers";
      case "standard":
        return "Comprehensiver explanations";
      case "elaborated":
        return "Technical details with step-by-step guidance";
      default:
        return "AI response style";
    }
  };

  const getStyleIcon = (style) => {
    switch (style) {
      case "simplified":
        return "📝";
      case "standard":
        return "📚";
      case "elaborated":
        return "🔧";
      default:
        return "🤖";
    }
  };

  const handleGoalSubmit = async (values) => {
    if (!selectedResponse) {
      message.error("Please select a response style");
      return;
    }

    const selected = getAIResponses().find(
      (response) => response.id === selectedResponse
    );

    if (!selected) {
      message.error("Invalid response style selected");
      return;
    }

    // Prepare data for API
    const ticketInfo = values.ticket_required_info || [];
    const hiddenFields = ["_issue_type", "_description"];

    // Remove any existing hidden fields to prevent duplicates
    const cleanTicketInfo = ticketInfo.filter(field => !hiddenFields.includes(field));

    const apiData = {
      prompt: selected.id, // Use the selected style (prompt_variants.style field)
      tools: values.tools || [],
      ticket_required_info: [
        ...cleanTicketInfo,
        ...hiddenFields
      ],
      AI_greeting: values.AI_greeting !== undefined ? values.AI_greeting : false,
    };

    // Call the API
    dispatch(
      updateTenantStageTwoApi({
        params: apiData,
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("Stage 2 API success:", response);
          message.success("Configuration saved successfully!");
          // Call the parent onSubmit callback with the data
          onSubmit({
            tools: values.tools,
            ticket_required_info: values.ticket_required_info,
            AI_greeting: values.AI_greeting,
            selectedResponse: selected,
            apiResponse: response,
          });
        },
        failureCallback: (error) => {
          console.error("Stage 2 API error:", error);
          message.error("Failed to save configuration. Please try again.");
        },
      })
    );
  };

  return (
    <div className="stage2-ai-goal-response" style={{ padding: "0 16px" }}>
      <div className="stage2-ai-goal-response__header">
        <Title
          level={2}
          style={{ marginBottom: 8, color: "#262626", textAlign: "center" }}
        >
          AI Configuration
        </Title>
        <Paragraph
          style={{
            textAlign: "center",
            fontSize: "15px",
            color: "#8c8c8c",
            marginBottom: 32,
          }}
        >
          Configure your AI assistant's behavior and available tools
        </Paragraph>
      </div>

      {/* CTA Tools Selection Form */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleGoalSubmit}
        style={{ marginBottom: 32 }}
      >
        {/* Tools Selection */}
        <Card
          title={
            <Space>
              <ThunderboltOutlined style={{ color: "#fa8c16", fontSize: 18 }} />
              <Text strong style={{ fontSize: 18 }}>
                Call to Action Options
              </Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 32 }}
        >
          <Form.Item
            name="tools"
            label="Select tools and capabilities for your AI assistant"
            rules={[
              {
                required: true,
                message: "Please select at least one tool",
              },
            ]}
            style={{
              padding: 24,
            }}
          >
            <Select
              mode="multiple"
              placeholder="Select tools"
              className="professional-select"
              style={{ width: "100%" }}
              optionLabelProp="label"
              tagRender={({ label, closable, onClose }) => (
                <div
                  style={{
                    margin: 3,
                    padding: "2px 6px",
                    backgroundColor: "#f0f0f0",
                    borderRadius: 4,
                    display: "inline-flex",
                    alignItems: "center",
                  }}
                >
                  <span style={{ margin: 4 }}>{label}</span>
                  {closable && (
                    <span
                      onClick={onClose}
                      style={{
                        cursor: "pointer",
                        display: "inline-flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginLeft: 4,
                      }}
                    >
                      <CloseIcon style={{ fontSize: 10 }} />
                    </span>
                  )}
                </div>
              )}
            >
              {ctaToolsList.map((tool) => (
                <Option
                  key={tool.name}
                  value={tool.name}
                  label={tool.label}
                  title={tool.description}
                >
                  <div>
                    <div style={{ fontWeight: 500 }}>{tool.label}</div>
                    <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
                      {tool.description}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Ticket Required Information - moved inside CTA Options */}
          <Form.Item
            name="ticket_required_info"
            label={
              <span>
                <Text strong style={{ fontSize: 14 }}>
                  Required Information Fields
                </Text>
                <span style={{ color: "#ff4d4f", marginLeft: 4 }}>*</span>
              </span>
            }
            style={{ padding: 24, paddingTop: 0 }}
          >
            <TicketRequiredInfoInput />
          </Form.Item>
        </Card>





        {/* AI Configuration */}
        <ResponseStyleSelector
          stage1Response={stage1Response}
          selectedResponse={selectedResponse}
          setSelectedResponse={setSelectedResponse}
          businessInfo={businessInfo}
          form={form}
        />
      </Form>
    </div>
  );
};

// Ticket Required Info Input Component
const TicketRequiredInfoInput = ({ value = [], onChange }) => {
  const [inputValue, setInputValue] = useState("");
  const [inputVisible, setInputVisible] = useState(false);

  // Hidden fields that should not appear in UI but will be added to API
  const hiddenFields = ["_issue_type", "_description"];

  // Filter out hidden fields for UI display
  const visibleValue = value.filter(item => !hiddenFields.includes(item));

  const suggestions = [
    "Full Name",
    "Contact Number",
    "Email Address",
    "Age",
    "Address"
  ];

  const handleInputConfirm = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && !visibleValue.includes(trimmedValue)) {
      // Keep hidden fields and add new visible field
      const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
      const newValue = [...visibleValue, trimmedValue, ...hiddenFieldsInValue];
      onChange?.(newValue);
    }
    setInputValue("");
    setInputVisible(false);
  };

  const handleTagClose = (removedTag) => {
    // Remove only from visible fields, keep hidden fields
    const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
    const newVisibleValue = visibleValue.filter((tag) => tag !== removedTag);
    const newValue = [...newVisibleValue, ...hiddenFieldsInValue];
    onChange?.(newValue);
  };

  const handleSuggestionClick = (suggestion) => {
    if (!visibleValue.includes(suggestion)) {
      // Keep hidden fields and add new visible field
      const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
      const newValue = [...visibleValue, suggestion, ...hiddenFieldsInValue];
      onChange?.(newValue);
    }
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const hideInput = () => {
    setInputValue("");
    setInputVisible(false);
  };

  // Calculate available suggestions based on visible value only
  const availableSuggestions = suggestions.filter(suggestion => !visibleValue.includes(suggestion));

  return (
    <div>
      {/* Selected Fields */}
      <div style={{ marginBottom: 16 }}>
        {visibleValue.map((tag, index) => (
          <Tag
            key={`selected-${tag}-${index}`}
            closable
            onClose={() => handleTagClose(tag)}
            style={{
              marginBottom: 8,
              marginRight: 8,
              padding: "4px 8px",
              fontSize: "13px",
              borderRadius: "6px"
            }}
            color="blue"
          >
            {tag}
          </Tag>
        ))}
        {inputVisible ? (
          <Input
            type="text"
            size="large"
            style={{
              width: 280,
              marginBottom: 8,
              borderRadius: "6px"
            }}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                hideInput();
              }
            }}
            placeholder="Enter custom field name"
            autoFocus
          />
        ) : (
          <Tag
            onClick={showInput}
            style={{
              background: "#fff",
              borderStyle: "dashed",
              cursor: "pointer",
              marginBottom: 8,
              padding: "4px 12px",
              fontSize: "13px",
              borderRadius: "6px",
              color: "#1890ff"
            }}
          >
            <PlusOutlined /> Add Custom Field
          </Tag>
        )}
      </div>

      {/* Suggestions */}
      {availableSuggestions.length > 0 && (
        <div key={`suggestions-${visibleValue.length}-${visibleValue.join(',')}`}>
          <Text strong style={{ fontSize: 13, color: "#666", marginBottom: 8, display: "block" }}>
            Quick Add Suggestions:
          </Text>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "6px" }}>
            {availableSuggestions.map((suggestion) => (
              <Tag
                key={`suggestion-${suggestion}`}
                onClick={() => handleSuggestionClick(suggestion)}
                style={{
                  cursor: "pointer",
                  backgroundColor: "#f0f0f0",
                  border: "1px solid #d9d9d9",
                  borderRadius: "4px",
                  fontSize: "12px",
                  padding: "2px 8px",
                  transition: "all 0.2s"
                }}
                className="suggestion-tag"
              >
                + {suggestion}
              </Tag>
            ))}
          </div>
        </div>
      )}

      {/* Description */}
      <div style={{ marginTop: 16, padding: 12, backgroundColor: "#f6f6f6", borderRadius: 6 }}>
        <Text style={{ fontSize: 12, color: "#666" }}>
          <strong>What this does:</strong> When customers create tickets or use call-to-action features, they'll be asked to provide this information.
          Choose what details will help your team assist them better and resolve their requests efficiently.
        </Text>
      </div>
    </div>
  );
};

// Compact Greeting Selector Component
const CompactGreetingSelector = ({ value, onChange, agentName, orgName }) => {
  const [selectedOption, setSelectedOption] = useState(value ? "enabled" : "disabled");

  // Update local state when value prop changes
  useEffect(() => {
    setSelectedOption(value ? "enabled" : "disabled");
  }, [value]);

  const handleOptionChange = (option) => {
    setSelectedOption(option);
    onChange?.(option === "enabled");
  };

  return (
    <div>
      {/* Compact Toggle with Examples */}
      <div style={{
        border: "1px solid #f0f0f0",
        borderRadius: "8px",
        backgroundColor: "#fafafa",
        padding: "16px"
      }}>
        {/* Toggle Options */}
        <div style={{ display: "flex", gap: "8px", marginBottom: 16 }}>
          <div
            onClick={() => handleOptionChange("enabled")}
            style={{
              flex: 1,
              padding: "12px 16px",
              border: selectedOption === "enabled" ? "2px solid #1890ff" : "1px solid #d9d9d9",
              borderRadius: "6px",
              cursor: "pointer",
              backgroundColor: selectedOption === "enabled" ? "#f0f5ff" : "#fff",
              transition: "all 0.2s ease",
              textAlign: "center"
            }}
          >
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: "50%",
                  border: "2px solid",
                  borderColor: selectedOption === "enabled" ? "#1890ff" : "#d9d9d9",
                  backgroundColor: selectedOption === "enabled" ? "#1890ff" : "transparent",
                  marginRight: 8,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center"
                }}
              >
                {selectedOption === "enabled" && (
                  <CheckOutlined style={{ fontSize: 10, color: "white" }} />
                )}
              </div>
              <Text strong style={{ color: selectedOption === "enabled" ? "#1890ff" : "#8c8c8c", fontSize: 14 }}>
                Friendly Greeting
              </Text>
            </div>
          </div>

          <div
            onClick={() => handleOptionChange("disabled")}
            style={{
              flex: 1,
              padding: "12px 16px",
              border: selectedOption === "disabled" ? "2px solid #1890ff" : "1px solid #d9d9d9",
              borderRadius: "6px",
              cursor: "pointer",
              backgroundColor: selectedOption === "disabled" ? "#f0f5ff" : "#fff",
              transition: "all 0.2s ease",
              textAlign: "center"
            }}
          >
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: "50%",
                  border: "2px solid",
                  borderColor: selectedOption === "disabled" ? "#1890ff" : "#d9d9d9",
                  backgroundColor: selectedOption === "disabled" ? "#1890ff" : "transparent",
                  marginRight: 8,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center"
                }}
              >
                {selectedOption === "disabled" && (
                  <CheckOutlined style={{ fontSize: 10, color: "white" }} />
                )}
              </div>
              <Text strong style={{ color: selectedOption === "disabled" ? "#1890ff" : "#8c8c8c", fontSize: 14 }}>
                Direct Response
              </Text>
            </div>
          </div>
        </div>

        {/* Example Preview */}
        <div style={{
          padding: "12px",
          backgroundColor: "#fff",
          borderRadius: "6px",
          border: "1px solid #f0f0f0"
        }}>
          <Text style={{ fontSize: 12, color: "#8c8c8c", marginBottom: 8, display: "block" }}>
            <strong>AI Resppnse Example:</strong>
          </Text>

          <div style={{ marginBottom: 8 }}>
            <Text style={{ fontSize: 12, color: "#666" }}>
              <strong>Customer:</strong> Hello!
            </Text>
          </div>

          <div>
            <Text style={{ fontSize: 12 }}>
              <strong>{agentName}:</strong> {selectedOption === "enabled"
                ? `Hello! I'm ${agentName}, your AI support assistant. Welcome to ${orgName || 'our platform'}. How can I assist you today?`
                : "Hi! How can I assist you today?"
              }
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Stage2AIGoalAndResponse;

// import { Card, Typography, Space, Divider, Carousel } from 'antd';
// import { CheckOutlined, BulbOutlined } from '@ant-design/icons';

// const { Text } = Typography;

// import { Space } from "antd";
// const { Text } = Typography;

// 🔑 Map style → icon and colors
const STYLE_CONFIG = {
  simplified: {
    icon: "mingcute:flash-line",
    color: "#1890ff", // Blue
    bgColor: "#f0f5ff",
    borderColor: "#91d5ff"
  },
  standard: {
    icon: "mingcute:chat-3-line",
    color: "#1890ff", // Blue
    bgColor: "#f0f5ff",
    borderColor: "#91d5ff"
  },
  elaborated: {
    icon: "mingcute:book-2-line",
    color: "#1890ff", // Blue
    bgColor: "#f0f5ff",
    borderColor: "#91d5ff"
  }
};

// 🔑 Map style → tagline/description
const STYLE_DESCRIPTIONS = {
  simplified: "Quick, concise answers",
  standard: "Comprehensive explanations",
  elaborated: "In-depth technical details with step-by-step guidance",
};

const ResponseStyleSelector = ({
  stage1Response,
  selectedResponse,
  setSelectedResponse,
  businessInfo,
  form,
}) => {
  const [exampleModalVisible, setExampleModalVisible] = useState(false);
  const [currentStyle, setCurrentStyle] = useState(null);
  const [currentExampleIndex, setCurrentExampleIndex] = useState(0);

  if (!stage1Response?.prompt_variants?.length) return null;

  // group examples by response style
  const groupedByStyle = stage1Response.prompt_variants.reduce(
    (acc, variant) => {
      if (!acc[variant.style]) acc[variant.style] = [];
      acc[variant.style].push(variant);
      return acc;
    },
    {}
  );

  const handleViewExample = (style, e) => {
    e.stopPropagation(); // Prevent card selection
    setCurrentStyle(style);
    setCurrentExampleIndex(0);
    setExampleModalVisible(true);
  };

  const getCurrentExample = () => {
    if (!currentStyle || !groupedByStyle[currentStyle]) return null;
    return groupedByStyle[currentStyle][currentExampleIndex];
  };

  const hasMultipleExamples = () => {
    return currentStyle && groupedByStyle[currentStyle]?.length > 1;
  };

  const goToPrevExample = () => {
    if (currentStyle && groupedByStyle[currentStyle]) {
      const maxIndex = groupedByStyle[currentStyle].length - 1;
      setCurrentExampleIndex(
        currentExampleIndex === 0 ? maxIndex : currentExampleIndex - 1
      );
    }
  };

  const goToNextExample = () => {
    if (currentStyle && groupedByStyle[currentStyle]) {
      const maxIndex = groupedByStyle[currentStyle].length - 1;
      setCurrentExampleIndex(
        currentExampleIndex === maxIndex ? 0 : currentExampleIndex + 1
      );
    }
  };

  const isError = (responseExample) => {
    return (
      typeof responseExample === "string" &&
      responseExample.toLowerCase().includes("error")
    );
  };

  return (
    <Card
      title={
        <Space>
          <BulbOutlined style={{ color: "#595959", fontSize: 18 }} />
          <Text style={{ fontSize: 18 }} strong>
            AI Configuration
          </Text>
        </Space>
      }
      className="form-section-card"
      style={{ marginBottom: 32 }}
    >
      {/* Initial Greeting Configuration */}
      <div style={{ padding: "24px 24px 32px 24px" }}>
        <Form.Item
          name="AI_greeting"
          label={
            <span>
              <span style={{ color: "#ff4d4f", fontWeight: 600, marginRight: 4 }}>
                *
              </span>
              <Text style={{ fontSize: 14 }}>
                Initial AI Greeting
              </Text>
              <div style={{ fontSize: 12, color: "#8c8c8c", marginTop: 4 }}>
                Choose how your AI greets users when they first start a conversation
              </div>
            </span>
          }
          valuePropName="checked"
        >
          <CompactGreetingSelector agentName={businessInfo?.agent_name || "AI Bot"} orgName={businessInfo?.org_name} />
        </Form.Item>
      </div>

      {/* Response Style Selection */}
      <div style={{ marginBottom: 24, padding: "0 24px" }}>
        <span style={{ color: "#ff4d4f", fontWeight: 600, marginRight: 4 }}>
          *
        </span>
        <Text style={{ fontWeight: 500, fontSize: 14 }}>
          Choose response style for customer queries
        </Text>
      </div>

      {/* 3 cards in a row */}
      <div
        style={{
          padding: "0 24px 24px 24px",
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: 20,
        }}
      >
        {Object.entries(groupedByStyle).map(([style]) => {
          const isSelected = selectedResponse === style;
          const config = STYLE_CONFIG[style];
          return (
            <div
              key={style}
              onClick={() => setSelectedResponse(style)}
              className={`response-style-card ${isSelected ? 'selected' : ''}`}
              style={{
                cursor: "pointer",
                border: isSelected
                  ? `2px solid ${config.color}`
                  : "1px solid #e8e8e8",
                borderRadius: 16,
                transition: "all 0.3s ease",
                boxShadow: isSelected
                  ? `0 8px 24px ${config.color}20`
                  : "0 2px 8px rgba(0,0,0,0.04)",
                background: isSelected ? config.bgColor : "#fff",
                padding: 28,
                textAlign: "center",
                minHeight: 180,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "space-between",
                position: "relative",
                transform: isSelected ? "translateY(-2px)" : "translateY(0)",
              }}
            >
              {/* Selection indicator */}
              <div
                style={{
                  position: "absolute",
                  top: 16,
                  right: 16,
                  width: 20,
                  height: 20,
                  borderRadius: "50%",
                  border: "2px solid",
                  borderColor: isSelected ? config.color : "#d9d9d9",
                  backgroundColor: isSelected ? config.color : "transparent",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "all 0.3s ease",
                }}
              >
                {isSelected && (
                  <CheckOutlined style={{ fontSize: 12, color: "white" }} />
                )}
              </div>

              {/* Content Container */}
              <div style={{ flex: 1, display: "flex", flexDirection: "column", alignItems: "center" }}>
                {/* Icon */}
                <iconify-icon
                  icon={config.icon}
                  style={{
                    fontSize: 48,
                    color: isSelected ? config.color : "#8c8c8c",
                    marginBottom: 16,
                    transition: "color 0.3s ease",
                  }}
                />

                {/* Style name */}
                <Text
                  strong
                  style={{
                    textTransform: "capitalize",
                    fontSize: 20,
                    color: isSelected ? config.color : "#262626",
                    marginBottom: 8,
                    fontWeight: 600,
                  }}
                >
                  {style}
                </Text>

                {/* Tagline/Description */}
                <Text
                  style={{
                    fontSize: 14,
                    color: isSelected ? "#666" : "#8c8c8c",
                    lineHeight: 1.5,
                    marginBottom: 20,
                    textAlign: "center",
                  }}
                >
                  {STYLE_DESCRIPTIONS[style] || "AI response style"}
                </Text>
              </div>

              {/* View Response Example Button */}
              <Button
                type="default"
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => handleViewExample(style, e)}
                style={{
                  fontSize: 12,
                  height: 36,
                  padding: "0 16px",
                  borderRadius: 8,
                  border: isSelected ? `1px solid ${config.color}` : "1px solid #d9d9d9",
                  backgroundColor: isSelected ? "#ffffff" : "#fafafa",
                  color: isSelected ? config.color : "#595959",
                  fontWeight: 500,
                  transition: "all 0.3s ease",
                }}
                className="view-example-btn"
              >
                View Example
              </Button>
            </div>
          );
        })}
      </div>

      {/* Example Modal */}
      <Modal
        title={null}
        open={exampleModalVisible}
        onCancel={() => setExampleModalVisible(false)}
        footer={null}
        width={800}
        centered
        styles={{
          body: { padding: 0 },
        }}
      >
        {getCurrentExample() && (
          <div style={{ padding: 24 }}>
            {/* Header */}
            <div style={{ marginBottom: 24, textAlign: "center" }}>
              <Space direction="vertical" size={8}>
                <iconify-icon
                  icon={STYLE_CONFIG[currentStyle]?.icon || "mdi:comment-text-outline"}
                  style={{
                    fontSize: 48,
                    color: STYLE_CONFIG[currentStyle]?.color || "#1890ff",
                  }}
                />
                <Text
                  strong
                  style={{
                    fontSize: 20,
                    textTransform: "capitalize",
                    color: "#262626",
                  }}
                >
                  {currentStyle} Response
                </Text>
                <Text style={{ color: "#8c8c8c", fontSize: 14 }}>
                  {STYLE_DESCRIPTIONS[currentStyle]}
                </Text>
              </Space>
            </div>

            {/* Navigation for multiple examples */}
            {hasMultipleExamples() && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 16,
                  padding: "8px 12px",
                  backgroundColor: "#f8f9fa",
                  borderRadius: 6,
                }}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<LeftOutlined />}
                  onClick={goToPrevExample}
                  style={{ color: "#1890ff" }}
                >
                  Previous
                </Button>
                <Text style={{ fontSize: 12, color: "#8c8c8c" }}>
                  {currentExampleIndex + 1} of{" "}
                  {groupedByStyle[currentStyle].length}
                </Text>
                <Button
                  type="text"
                  size="small"
                  icon={<RightOutlined />}
                  onClick={goToNextExample}
                  style={{ color: "#1890ff" }}
                >
                  Next
                </Button>
              </div>
            )}

            {/* Sample Question */}
            <div style={{ marginBottom: 20 }}>
              <Text strong style={{ color: "#262626", fontSize: 14 }}>
                Sample Question:
              </Text>
              <div
                style={{
                  marginTop: 8,
                  padding: 12,
                  backgroundColor: "#f6f9ff",
                  borderLeft: "3px solid #1890ff",
                  borderRadius: "0 6px 6px 0",
                }}
              >
                <Text style={{ color: "#262626", fontSize: 14 }}>
                  {getCurrentExample().user_query}
                </Text>
              </div>
            </div>

            {/* Sample Answer */}
            <div>
              <Text strong style={{ color: "#262626", fontSize: 14 }}>
                Sample Answer:
              </Text>
              <div
                style={{
                  marginTop: 8,
                  padding: 16,
                  backgroundColor: "#fafafa",
                  borderRadius: 6,
                  border: "1px solid #f0f0f0",
                }}
              >
                {isError(getCurrentExample().response_example) ? (
                  <Text
                    style={{
                      color: "#ff4d4f",
                      fontSize: 14,
                      fontStyle: "italic",
                    }}
                  >
                    There was some error generating this example
                  </Text>
                ) : (
                  <Text
                    style={{ color: "#262626", fontSize: 14, lineHeight: 1.6 }}
                  >
                    {getCurrentExample().response_example || "No response to show"}
                  </Text>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </Card>
  );
};

// export default ResponseStyleSelector;
