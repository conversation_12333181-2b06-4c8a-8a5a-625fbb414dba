import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Select,
  Row,
  Col,
  message,
} from "antd";
import { PlusOutlined, CopyOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { inviteUser } from "../../services/inviteUser.service";

const { Text } = Typography;
const { Option } = Select;

const InviteAgentsForm = ({ onClose }) => {
  const [form] = Form.useForm();
  const [username, setUsername] = useState("");
  const [userInviteRole, setUserInviteRole] = useState("agent");
  const [registrationToken, setRegistrationToken] = useState("");
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  const userRole = localStorage.getItem("role");

  const roleOptions =
    userRole === "supervisor"
      ? ["supervisor", "agent"]
      : ["admin", "supervisor", "agent"];

  const onFinish = (values) => {
    setLoading(true);
    setUsername(values.username);
    setUserInviteRole(values.role);

    dispatch(
      inviteUser({
        data: values,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          message.success("User invited successfully!");
          setSuccess(true);
          setRegistrationToken(response?.registration_token);
          form.resetFields();
        },
        failureCallback: (error) => {
          message.error("Failed to invite user. Please try again.");
          console.error("Error inviting user:", error);
        },
      })
    );
  };
  const invitationURL = registrationToken
    ? `${window.location.origin}/invitation?token=${registrationToken}&username=${username}&role=${userInviteRole}`
    : "";

  const handleCopy = () => {
    navigator.clipboard.writeText(invitationURL);
    message.success("Invitation link copied to clipboard!");
  };

  return (
    <Card bordered style={{ padding: "20px" }}>
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Row gutter={16}>
          <Col xs={24} sm={16}>
            <Form.Item
              label="Username/Email"
              name="username"
              rules={[
                { required: true, message: "Please input the username!" },
                { min: 3, message: "Username must be at least 3 characters." },
              ]}
            >
              <Input placeholder="Enter new agent username/email" />
            </Form.Item>
          </Col>

          <Col xs={24} sm={8}>
            <Form.Item
              label="Role"
              name="role"
              rules={[{ required: true, message: "Please select a role!" }]}
            >
              <Select
                placeholder="Assign a role"
                style={{ border: "1px solid #d9d9d9", borderRadius: "4px" }}
              >
                {roleOptions.map((role) => (
                  <Option key={role} value={role}>
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item style={{ textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            icon={<PlusOutlined />}
            loading={loading}
            block
          >
            Invite
          </Button>
        </Form.Item>
      </Form>

      {success && registrationToken && (
        <Card
          type="inner"
          title="Invitation Link"
          style={{ marginTop: "16px" }}
        >
          <Space direction="vertical">
            <Text
              copyable={{
                text: invitationURL,
                tooltips: ["Copy URL", "Copied!"],
              }}
            >
              {invitationURL}
            </Text>
            <Button
              type="default"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              disabled={!invitationURL}
            >
              Copy URL
            </Button>
          </Space>
        </Card>
      )}
    </Card>
  );
};

export default InviteAgentsForm;
