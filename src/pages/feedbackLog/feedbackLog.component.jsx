import { useState, useEffect } from "react";

import "./feedback.css";
import {
  Typography,
  Card,
  Select,
  Input,
  Tag,
  Space,
  Button,
  Row,
  Col,
  DatePicker,
  Tooltip,
  Avatar,
  Table,
  Modal,
  Pagination,
  Popconfirm,
} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  EyeOutlined,
  CloseCircleOutlined,
  CalendarOutlined,
  TagOutlined,
  FlagFilled,
  ReloadOutlined,
  CheckCircleOutlined,
  CheckSquareOutlined,
  CloseSquareOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks";
import {
  fetchFeedbackAPI,
  flagTableRowApi,
  getAllFiltersApi,
} from "../../services/feedback.service";
import FeedbackDetailcomponent from "./FeedbackDetail.component";
import { useNavigate } from "react-router-dom";
import { logout } from "../../store/slices/profile.slice";
import { transformString } from "../../helpers/channelNameTransformation";

const { Title, Text } = Typography;
const { Option } = Select;

const FeedbackInsightsDashboard = () => {
  // State for feedback data and filters
  const [feedbackData, setFeedbackData] = useState([]);
  const [totalFeedbackCount, setTotalFeedbackCount] = useState(0);
  const [filteredData, setFilteredData] = useState([]);
  const [filters, setFilters] = useState();
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null); // Store the selected record

  const [searchFeedback, setSearchFeedback] = useState();
  const [status, setStatus] = useState("unresolved");

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { dateRangeValue } = useAppSelector((state) => state.profile);

  const { RangePicker } = DatePicker;

  const [showUserId, setShowUserId] = useState({});

  const [loading, setLoading] = useState(true);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  // const [dates, setDates] = useState([]);
  // Check if dates are passed as null from navigation state
  const [dates, setDates] = useState([
    dayjs(dateRangeValue.start_date).format("YYYY-MM-DD"),
    dayjs(dateRangeValue.end_date).format("YYYY-MM-DD"),
  ]);

  // For RangePicker UI (moment objects)
  // const [dateRange, setDateRange] = useState(null);
  // Initialize date picker values based on navigation state
  const [dateRange, setDateRange] = useState([
    dayjs(dateRangeValue.start_date),
    dayjs(dateRangeValue.end_date),
  ]);

  const [category, setCategory] = useState();

  // Get unique categories for filter dropdown
  const uniqueCategories = Array.from(
    new Set(feedbackData.flatMap((item) => item?.categories))
  );

  const seeFullDetail = (record) => {
    console.log("record -> ", record);
    setSelectedRecord(record);
    setDetailVisible(true);
  };

  // Clear all filters
  const handleClearFilter = () => {
    setDates([]);
    setDateRange(null); // Also clear the moment objects for RangePicker
    setCategory();
    setSearchFeedback();
    setStatus("unresolved");
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get sentiment color based on evaluation
  const getSentimentColor = (evaluation) => {
    switch (evaluation) {
      case "dislike":
        return " #ff4d4f";
      case "like":
        return " #52c41a";
      default:
        return " #faad14";
    }
  };

  const handlePageChange = (page) => {
    setPage(page);
  };

  const fetchAllFilters = () => {
    dispatch(
      getAllFiltersApi({
        finalCallback: () => {},
        successCallback: (response) => {
          setFilters(response);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const handleFlagTableRow = (record) => {
    const data = {
      evaluation_id: record?._id,
      resolved_id: record?.resolved_id ?? null,
      resolved: !record?.resolved,
    };

    dispatch(
      flagTableRowApi({
        data: data,
        finalCallback: () => {},
        successCallback: () => {
          fetchFeedback();
        },
        failureCallback: (errors) => {},
      })
    );
  };

  useEffect(() => {
    fetchAllFilters();
  }, []);

  useEffect(() => {
    fetchFeedback();
  }, [page, dates, category, searchFeedback, status]);

  const fetchFeedback = () => {
    setLoading(true);
    dispatch(
      fetchFeedbackAPI({
        params: {
          page: page,
          limit: limit,
          start_date: dates ? dates[0] : null,
          end_date: dates ? dates[1] : null,
          search_query: searchFeedback,
          category: category,
          resolved:
            status === "resolved"
              ? true
              : status === "unresolved"
              ? false
              : undefined,
        },
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (responses) => {
          console.log("API Response:", responses); // Debugging

          if (responses?.data) {
            setFeedbackData(responses.data);
            setTotalFeedbackCount(responses.meta.total_count);
          }
        },
        failureCallback: (errors) => {
          setLoading(false);
        },
      })
    );
  };

  // Define table columns
  const columns = [
    {
      title: "User",
      dataIndex: "username",
      key: "userName",

      width: 200,
      render: (text, record) => (
        <div style={{ display: "flex", alignItems: "center" }}>
          <Avatar
            icon={<UserOutlined />}
            style={{
              width: "30px",
              height: "30px",
              borderRadius: "50%",
              backgroundColor: getSentimentColor(record.evaluation),
              marginRight: "8px",
            }}
          />
          <Text
            strong
            style={{
              fontSize: "14px",
              display: "block",
              maxWidth: "100%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {text}
          </Text>
        </div>
      ),
    },

    {
      title: "Query",
      dataIndex: "message",
      width: 450,
      key: "message",
      render: (text) => (
        <Tooltip title={text} overlayStyle={{ maxWidth: "50vw" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
            }}
          >
            <div
              style={{
                maxWidth: "400px",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {text}
            </div>
          </div>
        </Tooltip>
      ),
    },
    {
      title: "AI response",
      dataIndex: "reply",
      width: 450,
      key: "reply",
      render: (text) => (
        <Tooltip title={text} overlayStyle={{ maxWidth: "50vw" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
            }}
          >
            <div
              style={{
                maxWidth: "400px",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {text}
            </div>
          </div>
        </Tooltip>
      ),
    },

    {
      title: "Categories",
      dataIndex: "categories",
      width: 250,
      key: "categories",
      render: (categories) => {
        const displayedCategories = (categories || []).slice(0, 2);
        const hiddenCount = (categories || []).length - 2;
        const hasMore = hiddenCount > 0;

        return (
          <Space wrap>
            {displayedCategories.map((category) => (
              <Tag
                key={category}
                style={{
                  borderRadius: "4px",
                  backgroundColor: "#f0f0f0",
                  border: "none",
                  color: "#595959",
                }}
              >
                {category}
              </Tag>
            ))}
            {hasMore && (
              <Tag
                style={{
                  borderRadius: "4px",
                  backgroundColor: "#f0f0f0",
                  border: "none",
                  color: "#595959",
                }}
              >
                +{hiddenCount} more
              </Tag>
            )}
          </Space>
        );
      },
    },

    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",

      width: 300,
      render: (text) => (
        <Text type="secondary" style={{ fontSize: "13px" }}>
          {formatDate(text)}
        </Text>
      ),
    },
    {
      title: "Actions",
      key: "actions",

      width: 100,
      render: (_, record) => (
        <Space
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            style={{ color: "#1890ff" }}
            onClick={() => seeFullDetail(record)}
          />

          <Popconfirm
            placement="topLeft"
            title={
              record?.resolved
                ? "Are you sure you want to flag it?"
                : "Are you sure you want to unflag it?"
            }
            onConfirm={() => handleFlagTableRow(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={
                <FlagFilled
                  style={{ color: record?.resolved ? "green" : "red" }}
                />
              }
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleSearchFeedback = (e) => {
    setPage(1);
    const value = e.target.value;
    setSearchFeedback(value === "" ? undefined : value);
  };

  // Updated date change handler to maintain both states
  const handleDateChange = (values) => {
    setPage(1);
    setDateRange(values); // Store moment objects for RangePicker

    if (values) {
      // Convert to strings for API calls and filtering
      const stringDates = values.map((date) => date.format("YYYY-MM-DD"));
      setDates(stringDates);
      console.log("Selected Dates:", stringDates);
    } else {
      setDates([]);
    }
  };

  const handleCategorySelect = (selected) => {
    setPage(1);
    setCategory(selected && selected.length > 0 ? selected : undefined);
  };

  return (
    <div
      className="feedback-dashboard"
      style={{
        // padding: "24px 14px",
        backgroundColor: "#f5f5f5",
        height: "90vh",
      }}
      id="feedback-dashboard"
    >
      {/* Search and Filter Header */}
      <Card
        className="dashboard-header"
        style={{
          marginBottom: "24px",
        }}
      >
        <div
          style={{
            borderBottom: "1px solid #f0f0f0",
            marginBottom: "16px",
          }}
        >
          <Title
            level={5}
            style={{
              fontSize: "18px",
              color: "#333",
            }}
          >
            FeedBack Report
          </Title>
        </div>

        <Row gutter={[16, 16]} align="middle">
          {/* Search Input - Takes full width on mobile, half on larger screens */}
          <Col xs={24} md={8} lg={6}>
            <Input
              size="large"
              placeholder="Search feedback..."
              prefix={
                <SearchOutlined
                  style={{
                    fontSize: "16px",
                    color: "#1890ff",
                  }}
                />
              }
              value={searchFeedback}
              onChange={handleSearchFeedback}
              style={{
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                border: "1px solid #e8e8e8",
                height: "40px",
                boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
              }}
              allowClear
            />
          </Col>

          {/* Date Range Picker */}
          <Col xs={24} md={7} lg={6}>
            <RangePicker
              value={dateRange}
              disabledDate={(d) => d && d.valueOf() > Date.now()}
              onChange={handleDateChange}
              style={{
                width: "100%",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                border: "1px solid #e8e8e8",
                height: "40px",
              }}
              placeholder={["Start date", "End date"]}
              allowClear
              format="YYYY-MM-DD"
              suffixIcon={<CalendarOutlined style={{ color: "#1890ff" }} />}
            />
          </Col>

          {/* Category Multi-select */}
          <Col xs={12} md={5} lg={6}>
            <Select
              mode="multiple"
              style={{
                width: "100%",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                height: "40px",
                overflow: "hidden",
              }}
              placeholder="Categories"
              size="large"
              suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
              onChange={handleCategorySelect}
              value={category}
              allowClear
              maxTagCount={1}
              showArrow={true}
              maxTagPlaceholder={(omittedValues) =>
                `+${omittedValues.length} more`
              }
            >
              {filters?.categories?.map((category) => (
                <Option key={category} value={category}>
                  {category}
                </Option>
              ))}
            </Select>
          </Col>

          {/* Status Select */}
          <Col xs={12} md={4} lg={3}>
            <Select
              allowClear
              placeholder="Status"
              value={status}
              onChange={(value) => {
                setPage(1);
                setStatus(value);
              }}
              style={{
                width: "100%",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                height: "40px",
              }}
              options={[
                {
                  value: "resolved",
                  label: "Resolved",
                },
                {
                  value: "unresolved",
                  label: "Unresolved",
                },
              ]}
              optionRender={(option) => <Space>{option.data.label}</Space>}
            />
          </Col>

          {/* Clear Filters Button */}
          <Col xs={24} md={24} lg={3}>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                type="default"
                icon={<ReloadOutlined />}
                size="large"
                style={{
                  borderRadius: "8px",
                  background: "#fff",
                  height: "40px",
                }}
                onClick={handleClearFilter}
              />
            </div>
          </Col>
        </Row>

        {/* <Row gutter={[20, 16]}>
          <Col xs={24} md={12} lg={8}>
            <div
              style={{
                position: "relative",
              }}
            >
              <Input
                size="large"
                placeholder="Search..."
                prefix={
                  <SearchOutlined
                    style={{
                      fontSize: "16px",
                      marginRight: "6px",
                    }}
                  />
                }
                value={searchFeedback}
                onChange={handleSearchFeedback}
                style={{
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  border: "1px solid #e8e8e8",
                  transition: "all 0.3s ease",
                }}
                allowClear
              />
            </div>
          </Col>

          <Col xs={12} sm={6} md={6} lg={6}>
            <RangePicker
              value={dateRange}
              onChange={handleDateChange}
              style={{
                width: "100%",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                border: "1px solid #e8e8e8",
                height: "39.6px",
                boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
              }}
              placeholder={["Start date", "End date"]}
              allowClear
              format="YYYY-MM-DD"
            />
          </Col>

          <Col xs={12} sm={6} md={6} lg={5}>
            <Select
              mode="multiple"
              style={{
                width: "100%",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                minHeight: "39.6px",
              }}
              placeholder="Select categories"
              size="large"
              suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
              dropdownMatchSelectWidth={true}
              onChange={handleCategorySelect}
              value={category || []}
              allowClear
              maxTagCount={1} // This hides all tags
              showArrow={true}
              dropdownStyle={{ borderRadius: "8px", padding: "8px 0" }}
              // Optional: Show count of selected items
              maxTagPlaceholder={(omittedValues) =>
                `+${omittedValues.length} more`
              }
            >
              {filters?.categories?.map((category) => {
                return (
                  <Option key={category} value={category}>
                    {category}
                  </Option>
                );
              })}
            </Select>
          </Col>
          <Col>
            <Select
              defaultValue="resolved"
              style={{
                width: "120px",
                borderRadius: "8px",
                backgroundColor: "#f9f9f9",
                minHeight: "39.6px",
              }}
              // onChange={handleChange}
              options={[
                { value: "resolved", label: "Resolved" },
                { value: "unresolved", label: "Unresolved" },
              ]}
            />
          </Col>

          <Col xs={24} sm={6} md={6} lg={4} style={{ textAlign: "right" }}>
            <Button
              type="primary"
              icon={<CloseCircleOutlined />}
              size="large"
              style={{
                borderRadius: "8px",
                border: "none",
                background: "#f5f5f5",
                color: "#ff4d4f",
                fontWeight: 500,
                boxShadow: "none",
                height: "42px",
                transition: "all 0.3s ease",
              }}
              onClick={handleClearFilter}
              danger
              ghost
            />
          </Col>
        </Row> */}

        {/* Update this part of your Active Filters section */}
        {(category?.length > 0 || status || (dates && dates.length > 0)) && (
          <div
            style={{
              marginTop: "20px",
              padding: "12px 16px",
              borderRadius: "8px",
              border: "1px solid #e6f0ff",
              // backgroundColor: "#f6f8ff",
            }}
          >
            <Row align="middle">
              <Col>
                <Text
                  style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    // color: "#1890ff",
                    marginRight: "8px",
                  }}
                >
                  Active Filters:
                </Text>
                <Space size={10} wrap>
                  {dates && dates.length > 0 && (
                    <Tag
                      // color="#1890ff"
                      style={{
                        borderRadius: "6px",
                        padding: "4px 8px",
                        fontSize: "13px",
                        fontWeight: 500,
                        border: "none",
                        display: "flex",
                        alignItems: "center",
                        gap: "4px",
                      }}
                      closable
                      onClose={() => {
                        setDates([]);
                        setDateRange(null);
                      }}
                    >
                      <CalendarOutlined /> Date: {dates.join(" — ")}
                    </Tag>
                  )}

                  {/* Map through each category to create individual tags */}
                  {category &&
                    category.length > 0 &&
                    category.map((cat) => (
                      <Tag
                        key={cat}
                        // color="#52c41a"
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => {
                          // Remove just this category from the array
                          const updatedCategories = category.filter(
                            (c) => c !== cat
                          );
                          setCategory(
                            updatedCategories.length > 0
                              ? updatedCategories
                              : undefined
                          );
                        }}
                      >
                        <TagOutlined /> {cat}
                      </Tag>
                    ))}

                  {status && (
                    <Tag
                      style={{
                        borderRadius: "6px",
                        padding: "4px 8px",
                        fontSize: "13px",
                        fontWeight: 500,
                        border: "none",
                        display: "flex",
                        alignItems: "center",
                        gap: "4px",
                      }}
                      closable
                      onClose={() => {
                        setStatus();
                      }}
                    >
                      {status === "resolved" ? (
                        <CheckSquareOutlined />
                      ) : status === "unresolved" ? (
                        <CloseSquareOutlined />
                      ) : null}
                      {transformString(status)}
                    </Tag>
                  )}
                </Space>
              </Col>
            </Row>
          </div>
        )}
      </Card>
      {/* {loading && filteredData.length === 0 ? (
        <Card
          style={{
            textAlign: "center",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
          }}
        >
          <Spin size="large" />
          <div
            style={{ marginTop: "16px", maxHeight: "80vh", minHeight: "80vh" }}
          >
            <Text type="secondary">Loading feedback data...</Text>
          </div>
        </Card>
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
            width: "100%",
          }}
        >
          <Table
            className="feedback-table"
            dataSource={feedbackData}
            columns={columns}
            rowKey={(item) => item._id}
            pagination={false}
            bordered
            style={{
              backgroundColor: "white",
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
            }}
            rowClassName={(record) =>
              record.evaluation === "like"
                ? "table-row-positive"
                : record.evaluation === "dislike"
                ? "table-row-negative"
                : ""
            }
            footer={false}
          />

          {detailVisible && selectedRecord && (
            <Modal
              placement="right"
              visible={detailVisible}
              onCancel={() => setDetailVisible(false)} // Close the modal
              footer={null}
              width={"75vw"}
              closeIcon={
                <span style={{ color: "red", fontSize: "30px" }}>×</span>
              }
            >
              <FeedbackDetailcomponent
                getSentimentColor={getSentimentColor}
                record={selectedRecord}
              />
            </Modal>
          )}
          <div>
            <Pagination
              current={page}
              pageSize={limit}
              total={totalFeedbackCount}
              onChange={handlePageChange}
              showSizeChanger={false}
              style={{ marginTop: "24px", marginBottom: "32px" }}
            />
          </div>
        </div>
      )} */}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
          width: "100%",
        }}
      >
        <Table
          className="feedback-table"
          dataSource={feedbackData}
          columns={columns}
          rowKey={(item) => item._id}
          pagination={false}
          bordered
          style={{
            backgroundColor: "white",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
          }}
          rowClassName={(record) =>
            record.evaluation === "like"
              ? "table-row-positive"
              : record.evaluation === "dislike"
              ? "table-row-negative"
              : ""
          }
          footer={false}
        />

        {detailVisible && selectedRecord && (
          <Modal
            placement="right"
            visible={detailVisible}
            onCancel={() => setDetailVisible(false)} // Close the modal
            footer={null}
            width={"75vw"}
            closeIcon={
              <span style={{ color: "red", fontSize: "30px" }}>×</span>
            }
          >
            <FeedbackDetailcomponent
              getSentimentColor={getSentimentColor}
              record={selectedRecord}
            />
          </Modal>
        )}
        <div>
          <Pagination
            current={page}
            pageSize={limit}
            total={totalFeedbackCount}
            onChange={handlePageChange}
            showSizeChanger={false}
            style={{ marginTop: "24px", marginBottom: "32px" }}
          />
        </div>
      </div>
    </div>
  );
};

export default FeedbackInsightsDashboard;
