import { Ava<PERSON>, Card, <PERSON>, Typography, Tag } from "antd";
import React from "react";
import {
  UserOutlined,
  QuestionCircleOutlined,
  MessageOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import "./feedback.css";

const { Text, Paragraph } = Typography;

const FeedbackDetailComponent = ({ record, getSentimentColor }) => {
  return (
    <Card
      bordered={false}
      title={
        <Space
          style={{
            display: "flex",
            alignItems: "center",
            width: "100%",
          }}
        >
          <Avatar
            size={40}
            icon={<UserOutlined />}
            src={record.avatarUrl}
            style={{
              backgroundColor: getSentimentColor(record.evaluation),
              boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            }}
          />
          <div className="user-info">
            <Text strong style={{ fontSize: 18 }}>
              {record.username}'s Feedback
            </Text>
          </div>
        </Space>
      }
      style={{
        width: "70vw",
        borderRadius: "8px",
        height: "75vh",
        overflowY: "auto",
        // borderLeft: `7px solid ${getSentimentColor(record.evaluation)}`,
      }}
    >
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {record.categories && record.categories.length > 0 ? (
          <Space size={[8, 12]} wrap>
            {record.categories.map((category) => (
              <Tag
                key={category}
                style={{
                  padding: "4px 10px",
                  borderRadius: "16px",
                  fontSize: "14px",
                }}
              >
                {category}
              </Tag>
            ))}
          </Space>
        ) : (
          <Text type="secondary">No categories</Text>
        )}

        {/* Customer Query Section without List */}
        <div
          style={{
            backgroundColor: "#f5f5f5",
            padding: "10px 20px",
            borderRadius: "6px",
          }}
        >
          <Space>
            <QuestionCircleOutlined
              style={{
                color: "#1677ff",
                fontSize: "18px",
              }}
            />
            <Text strong style={{ fontSize: "16px" }}>
              Customer Query
            </Text>
          </Space>
          <Paragraph>{record.message || "No user Message available"}</Paragraph>
        </div>

        {/* AI Reply Section */}
        <div
          style={{
            backgroundColor: "#f5f5f5",
            padding: "10px 20px",
            borderRadius: "6px",
          }}
        >
          <Space>
            <RobotOutlined
              style={{
                color: "#1677ff",
                fontSize: "18px",
              }}
            />
            <Text strong style={{ fontSize: "16px" }}>
              AI Reply
            </Text>
          </Space>
          <Paragraph style={{ marginTop: "0px" }}>
            {record.reply || "No AI reply available"}
          </Paragraph>
        </div>

        {/* User Feedback Section */}
        <div
          style={{
            backgroundColor: "#f5f5f5",
            padding: "10px 20px",
            borderRadius: "6px",
          }}
        >
          <Space>
            <MessageOutlined
              style={{
                color: "#f5222d",
                fontSize: "18px",
              }}
            />
            <Text strong style={{ fontSize: "16px" }}>
              Customer Feedback
            </Text>
          </Space>
          <Paragraph italic>
            {record.remark || "No feedback provided"}
          </Paragraph>
        </div>
      </Space>
    </Card>
  );
};

export default FeedbackDetailComponent;
