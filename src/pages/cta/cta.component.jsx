import React, { useEffect, useState } from "react";
import {
  Typography,
  Card,
  Select,
  Input,
  Tag,
  Space,
  Button,
  Row,
  Col,
  DatePicker,
  Tooltip,
  Avatar,
  Table,
  Modal,
  Pagination,
  Popconfirm,
  Menu,
  Dropdown,
  Collapse,
  Grid,
} from "antd";

import {
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  EyeOutlined,
  CloseCircleOutlined,
  CalendarOutlined,
  TagOutlined,
  FlagFilled,
  ReloadOutlined,
  CheckOutlined,
  MoreOutlined,
  MessageOutlined,
  CloseOutlined,
} from "@ant-design/icons";

import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks";

import {
  getAllCtaDataApi,
  getCtaFiltersApi,
  getCtaTopicsApi,
  getAgentsApi,
  assignAgentApi,
} from "../../services/cta.service";

import { getFiltersApi } from "../../services/channels.service.js";

import dayjs from "dayjs";
import DataTablePagination from "../../components/common/dataTablePagination";
import CtaDetailsDialog from "./CtaDetailsDialog.Component";
import ResolveCtaDialog from "./ResolveCtaDialog.Component";
import { useNavigate, useLocation } from "react-router-dom";
import { logout } from "../../store/slices/profile.slice";

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { useBreakpoint } = Grid;

const CtaComponent = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;
  const [searchCta, setSearchCta] = useState(location.state?.search);
  const { dateRangeValue } = useAppSelector((state) => state.profile);
  const [channelsList, setChannelsList] = useState();
  const [allFilters, setAllFilters] = useState();

  // Check if dates are passed as null from navigation state
  const [dates, setDates] = useState(
    location.state?.dates === null
      ? []
      : [
          dayjs(dateRangeValue.start_date).format("YYYY-MM-DD"),
          dayjs(dateRangeValue.end_date).format("YYYY-MM-DD"),
        ]
  );

  // Initialize date picker values based on navigation state
  const [dateRangeAntdValue, setDateRangeAntdValue] = useState(
    location.state?.dates === null
      ? null
      : [dayjs(dateRangeValue.start_date), dayjs(dateRangeValue.end_date)]
  );

  const [ctaType, setCtaType] = useState(
    location.state?.type
      ? location.state?.type
      : location.state?.type === null
      ? null
      : undefined
  );

  const [channel, setChannel] = useState();

  // CTA FILTERS
  const [ctaFilters, setCtaFilters] = useState();
  const [ctaTopics, setCtaTopics] = useState([]);
  const [users, setUsers] = useState([]);

  // CTA DATA
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const [ctaData, setCtaData] = useState();
  const [ctaStatusDialogOpen, setCtaStatusDialogOpen] = useState(false);

  // Filters
  const [createdBy, setCreatedBy] = useState([]);
  const [resource, setResource] = useState([]);
  const [priority, setPriority] = useState([]);
  const [status, setStatus] = useState(
    location.state?.status
      ? location.state?.status
      : location.state?.status === null
      ? null
      : "open"
  );
  const [topic, setTopic] = useState([]);
  const [assignedTo, setAssignedTo] = useState(
    localStorage.getItem("role") === "agent"
      ? localStorage.getItem("userId")
      : undefined
  );

  // Pagination
  const [pageSize, setPageSize] = useState("10");
  const [pageNumber, setPageNumber] = useState("1");
  const [totalDocuments, setTotalDocuments] = useState();

  // Store previous state for empty page detection
  const [previousPageState, setPreviousPageState] = useState({
    pageNumber: 1,
    totalDocuments: 0,
    hasData: false,
  });

  // Dialog state
  const [isDetailsDialogVisible, setIsDetailsDialogVisible] = useState(false);
  const [selectedCta, setSelectedCta] = useState(null);

  // Handle Search filter
  const handleSearchFilter = (e) => {
    setPageNumber(1);
    setSearchCta(e.target.value);
  };

  // Handle Date range filter
  const handleDateChange = (values) => {
    setPageNumber(1);
    if (values) {
      const stringDates = values.map((date) => date.format("YYYY-MM-DD"));
      const newDateRangeAntdValue = [
        dayjs(stringDates[0]),
        dayjs(stringDates[1]),
      ];
      setDateRangeAntdValue(newDateRangeAntdValue);
      setDates(stringDates);
    } else {
      setDates([]);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle CTA select
  const handleCtaSelect = (record) => {
    setSelectedCta(record);
    setIsDetailsDialogVisible(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsDetailsDialogVisible(false);
    // setSelectedCta(null);
  };

  // handle jump to chat
  const handleJumpToChat = (record) => {
    navigate("/channels", {
      state: {
        phoneID: record?.customer_email || record?.customer_id,
        navigatedFromCta: true,
      },
    });
  };

  const screens = useBreakpoint();
  const isMobile = screens.xs && !screens.md;

  const changeAgentAssignment = (ctaId, agentId) => {
    dispatch(
      assignAgentApi({
        params: {
          cta_id: ctaId,
          assign_to: agentId,
          mode: "assign",
        },
        finalCallback: () => {
          setRefreshTrigger((prev) => !prev);
        },
        successCallback: (response) => {},
        failureCallback: (errors) => {},
      })
    );
  };

  // Define table columns
  const columns = [
    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",
      render: (text) => <>{formatDate(text)}</>,
    },
    {
      title: "Customer Name",
      dataIndex: "customer_name",
      key: "customer_name",
      render: (text) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {text ? text : "N/A"}
        </div>
      ),
    },
    {
      title: "Channel",
      dataIndex: "channel",
      key: "type",
      render: (text) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {text[0].toUpperCase() + text.slice(1)}
        </div>
      ),
    },
    {
      title: "Details",
      key: "details",
      render: (text, record) => (
        <Button
          color="primary"
          variant="filled"
          icon={<EyeOutlined />}
          size="small"
          style={{ borderRadius: "6px" }}
          onClick={() => handleCtaSelect(record)}
        >
          View Details
        </Button>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (text) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {text[0].toUpperCase() + text.slice(1)}
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (text) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          <Tag
            color={
              text === "open"
                ? "blue"
                : text === "resolved"
                ? "green"
                : text === "closed"
                ? "red"
                : "default"
            }
            style={{
              width: "65px",
              textAlign: "center",
            }}
          >
            {text[0].toUpperCase() + text.slice(1)}
          </Tag>
        </div>
      ),
    },
    {
      title: "Assigned To",
      dataIndex: "assigned_to_name",
      key: "assigned_to_name",
      render: (text, record) => {
        const handleChange = (value) => {
          const selectedUser = users.find((u) => u.key === value);

          Modal.confirm({
            title: "Change Assignment",
            content: (
              <>
                Are you sure you want to assign this task to{" "}
                <b>{selectedUser.name}</b>?
              </>
            ),
            okText: "Yes",
            cancelText: "No",
            transitionName: "",
            maskTransitionName: "",
            onOk: () => {
              const userId = value;
              const ctaId = record.id;

              changeAgentAssignment(ctaId, userId);
              fetchAllCtaData();
            },
          });
        };

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            {record.status === "open" ? (
              <Select
                style={{ width: 120 }}
                value={record.assigned_to}
                onChange={handleChange}
              >
                <Select.Option
                  key="placeholder"
                  value={null}
                  disabled
                  hidden
                  style={{ display: "none" }}
                >
                  -
                </Select.Option>
                {users.map((user) => (
                  <Select.Option key={user.key} value={user.key}>
                    {user.name}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <>{record.assigned_to_name}</>
            )}
          </div>
        );
      },
    },
    {
      title: "Action",
      key: "action",
      width: 150,
      render: (text, record) => (
        <div
          style={{
            display: "flex",
            gap: "8px",
          }}
        >
          <Tooltip title="View In Chat" placement="top">
            <Button
              icon={<MessageOutlined />}
              size="small"
              variant="filled"
              color="default"
              onClick={() => handleJumpToChat(record)}
            >
              View In Chat
            </Button>
          </Tooltip>
          {record.status === "open" && (
            <Tooltip title="Mark As Resolved" placement="top">
              <Button
                icon={<CheckOutlined />}
                size="small"
                variant="filled"
                color="danger"
                onClick={() => {
                  setSelectedCta(record);
                  setCtaStatusDialogOpen(true);
                }}
              >
                Resolve
              </Button>
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  const filterContent = (
    <Row gutter={[16, 16]} align="top" style={{ width: "100%" }}>
      {/* Search Input */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          Search
        </Text>
        <Input
          placeholder="Search..."
          prefix={<SearchOutlined style={{ color: "#1890ff" }} />}
          value={searchCta}
          onChange={handleSearchFilter}
          style={{
            borderRadius: "8px",
            backgroundColor: "#f9f9f9",
            border: "1px solid #e8e8e8",
            boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
          }}
          allowClear
        />
      </Col>

      {/* Date Range Picker */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          Date Range
        </Text>
        <RangePicker
          value={dateRangeAntdValue}
          disabledDate={(d) => d && d.valueOf() > Date.now()}
          onChange={handleDateChange}
          style={{
            width: "100%",
            borderRadius: "8px",
            backgroundColor: "#f9f9f9",
            border: "1px solid #e8e8e8",
          }}
          placeholder={["Start date", "End date"]}
          allowClear
          format="YYYY-MM-DD"
          suffixIcon={<CalendarOutlined style={{ color: "#1890ff" }} />}
        />
      </Col>

      {/* Channel */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          Channel
        </Text>
        <Select
          style={{
            width: "100%",
            backgroundColor: "#f9f9f9",
          }}
          placeholder="Channel"
          size="medium"
          suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
          onChange={(value) => {
            setPageNumber(1);
            setChannel(value);
            // Don't reset pageSize when changing CTA type
          }}
          value={channel}
          allowClear
          showArrow
        >
          {allFilters?.channel?.map((item) => {
            return (
              <Option key={item} value={item}>
                {item}
              </Option>
            );
          })}
        </Select>
      </Col>

      {/* CTA Type */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          CTA Type
        </Text>
        <Select
          style={{
            width: "100%",
            backgroundColor: "#f9f9f9",
          }}
          placeholder="CTA Type"
          size="medium"
          suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
          onChange={(value) => {
            setPageNumber(1);
            setCtaType(value);
            // Don't reset pageSize when changing CTA type
          }}
          value={ctaType}
          allowClear
          showArrow
        >
          {allFilters?.type?.map((item) => {
            return (
              <Option key={item} value={item}>
                {item}
              </Option>
            );
          })}
        </Select>
      </Col>

      {/* Status */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          Status
        </Text>
        <Select
          style={{
            width: "100%",
            backgroundColor: "#f9f9f9",
          }}
          placeholder="Status"
          size="medium"
          suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
          onChange={(value) => {
            setPageNumber(1);
            setStatus(value);
          }}
          value={status}
          allowClear
          showArrow
        >
          {allFilters?.status?.map((item) => {
            return (
              <Option key={item} value={item}>
                {item}
              </Option>
            );
          })}
        </Select>
      </Col>

      {/* Assigned To */}
      <Col xs={24} sm={12} md={8} lg={4} xl={4}>
        <Text strong style={{ display: "block", marginBottom: 8 }}>
          Assigned To
        </Text>
        <Select
          style={{
            width: "100%",
            backgroundColor: "#f9f9f9",
          }}
          placeholder="Assigned To"
          size="medium"
          suffixIcon={<FilterOutlined style={{ color: "#1890ff" }} />}
          onChange={(value) => {
            setPageNumber(1);
            setAssignedTo(value);
          }}
          value={assignedTo}
          showArrow
          allowClear
        >
          {allFilters?.assigned_to?.map((item) => {
            return (
              <Option key={item?.id} value={item?.id}>
                {item?.name}
              </Option>
            );
          })}
        </Select>
      </Col>
    </Row>
  );

  const fetchCtaFilters = () => {
    dispatch(
      getCtaFiltersApi({
        params: {
          cta_type: ctaType,
        },
        finalCallback: () => {},
        successCallback: (response) => {
          setCtaFilters(response);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchAllUsers = () => {
    dispatch(
      getAgentsApi({
        params: {},
        finalCallback: () => {},
        successCallback: (response) => {
          if (response && response.length > 0) {
            setUsers(response);
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchAllCtaData = (checkEmptyPage = false) => {
    // Store current state before making API call
    const currentState = {
      pageNumber: parseInt(pageNumber),
      totalDocuments: totalDocuments || 0,
      hasData: (ctaData && ctaData.length > 0) || false,
      currentDataLength: ctaData ? ctaData.length : 0,
    };

    dispatch(
      getAllCtaDataApi({
        params: {
          search: searchCta,
          type: ctaType,
          channels: channel,
          status: status,
          priority: priority,
          created_by: createdBy,
          resource: resource,
          start_date: dates?.length > 0 ? dates[0] : null,
          end_date: dates?.length > 0 ? dates[1] : null,
          per_page: pageSize,
          page: pageNumber,
          topic: topic,
          assigned_to: assignedTo,
        },
        finalCallback: () => {},
        successCallback: (response) => {
          const responseData = response?.data || [];
          const totalDocs = response?.meta?.total || 0;
          const currentPageFromResponse = response?.meta?.page || 1;

          // Enhanced empty page detection
          if (checkEmptyPage && responseData.length === 0) {
            // Calculate if we should navigate to previous page
            const shouldNavigateToPrevious =
              currentPageFromResponse > 1 && // Not on first page
              (currentState.hasData || // Had data before
                (currentState.totalDocuments > 0 &&
                  totalDocs < currentState.totalDocuments)); // Total count decreased

            if (shouldNavigateToPrevious) {
              // Calculate target page
              const maxPossiblePages = Math.ceil(
                totalDocs / parseInt(pageSize)
              );
              const targetPage = Math.min(
                Math.max(1, maxPossiblePages),
                currentPageFromResponse - 1
              );

              console.log("Navigating to previous page:", targetPage);

              // Set the target page
              setPageNumber(targetPage);

              // Fetch data for the target page with a small delay to ensure state is updated
              setTimeout(() => {
                dispatch(
                  getAllCtaDataApi({
                    params: {
                      search: searchCta,
                      type: ctaType,
                      channels: channel,
                      status: status,
                      priority: priority,
                      created_by: createdBy,
                      resource: resource,
                      start_date: dates?.length > 0 ? dates[0] : null,
                      end_date: dates?.length > 0 ? dates[1] : null,
                      per_page: pageSize,
                      page: targetPage,
                      topic: topic,
                      assigned_to: assignedTo,
                    },
                    finalCallback: () => {},
                    successCallback: (newResponse) => {
                      const newResponseData = newResponse?.data || [];
                      const newTotalDocs = newResponse?.meta?.total || 0;
                      const newCurrentPage = newResponse?.meta?.page || 1;

                      setCtaData(newResponseData);
                      setTotalDocuments(newTotalDocs);
                      setPageNumber(newCurrentPage);
                    },
                    failureCallback: (errors) => {
                      console.error("Error fetching target page data:", errors);
                      // Fallback: set data anyway to avoid infinite loading
                      setCtaData(responseData);
                      setTotalDocuments(totalDocs);
                      setPageNumber(currentPageFromResponse);
                    },
                  })
                );
              }, 100);
              return;
            }
          }

          // Normal case: just set the data
          setCtaData(responseData);
          setTotalDocuments(totalDocs);
          setPageNumber(currentPageFromResponse);
        },
        failureCallback: (errors) => {
          console.error("Error fetching CTA data:", errors);
        },
      })
    );
  };

  const fetchCtaTopics = async () => {
    dispatch(
      getCtaTopicsApi({
        params: {
          type: ctaType,
        },
        finalCallback: () => {},
        successCallback: (response) => {
          if (response && response.length > 0) {
            setCtaTopics(response);
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchFilters = () => {
    dispatch(
      getFiltersApi({
        params: { filter_route: "CTA" },
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("RESPONSE -> ", response);
          setAllFilters(response);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  useEffect(() => {
    fetchCtaTopics();
    fetchAllUsers();
    fetchFilters();
  }, []);

  useEffect(() => {
    fetchAllCtaData();
  }, [
    searchCta,
    ctaType,
    channel,
    dates,
    createdBy,
    resource,
    priority,
    status,
    pageNumber,
    pageSize,
    topic,
    refreshTrigger,
    assignedTo,
  ]);

  return (
    <>
      {/* Search and Filter Header */}
      <Card
        className="dashboard-header"
        style={{
          marginBottom: "24px",
        }}
      >
        <div
          style={{
            borderBottom: "1px solid #f0f0f0",
            marginBottom: "16px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            paddingBottom: "8px",
          }}
        >
          <Title
            level={4}
            style={{
              color: "#333",
              fontWeight: 700,
            }}
          >
            Call To Action
          </Title>

          <Tooltip title="Refresh Data" placement="top">
            <Button
              type="text"
              color="primary"
              variant="outlined"
              icon={<ReloadOutlined />}
              size="medium"
              onClick={() => setRefreshTrigger((prev) => !prev)}
            >
              Refresh
            </Button>
          </Tooltip>
        </div>

        {isMobile ? (
          <Collapse
            defaultActiveKey={["1"]}
            style={{
              marginBottom: 16,
              backgroundColor: "#fff",
              borderRadius: "8px",
              border: "1px solid #e8e8e8",
            }}
            expandIconPosition="end"
          >
            <Panel
              header={
                <Text strong style={{ fontSize: 16 }}>
                  Filters
                </Text>
              }
              key="1"
            >
              {filterContent}
            </Panel>
          </Collapse>
        ) : (
          <div style={{ marginBottom: 16 }}>{filterContent}</div>
        )}
      </Card>

      <div
        style={{
          background: "#fff",
          borderRadius: "8px",
          paddingBottom: "16px",
        }}
      >
        <div
          style={{
            overflowX: "auto",
            width: "100%",
            whiteSpace: "nowrap",
          }}
        >
          <Table
            dataSource={ctaData}
            columns={columns}
            rowKey={(item) => item._id}
            pagination={false}
            bordered
            rowClassName={(record) =>
              record?.id === selectedCta?.id ? "table-row-selected" : ""
            }
            footer={false}
          />
        </div>
        <DataTablePagination
          totalDocuments={totalDocuments}
          pageSize={pageSize}
          setPageSize={setPageSize}
          setPageNumber={setPageNumber}
          currentPage={pageNumber}
        />
      </div>

      <CtaDetailsDialog
        visible={isDetailsDialogVisible}
        ctaData={selectedCta}
        onClose={handleDialogClose}
        setCtaStatusDialogOpen={setCtaStatusDialogOpen}
      />
      <ResolveCtaDialog
        visible={ctaStatusDialogOpen}
        ctaData={selectedCta}
        onClose={() => setCtaStatusDialogOpen(false)}
        onSuccess={() => {
          setCtaStatusDialogOpen(false);
          setIsDetailsDialogVisible(false);
          fetchAllCtaData(true); // Pass true to check for empty page
        }}
      />
    </>
  );
};

export default CtaComponent;
