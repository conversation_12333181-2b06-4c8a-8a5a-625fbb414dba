import { Collapse, Layout, Menu, Typography } from "antd";
import Sider from "antd/es/layout/Sider";
import React from "react";
import { EditOutlined, InboxOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { documentsByTocApi } from "../../../../services/knowledgebase.service";

const { Text, Title } = Typography;
const { Panel } = Collapse;

const TocComponent = ({ tocData, setEndpointResponse }) => {
  const dispatch = useAppDispatch();

  return (
    <div
      style={{
        height: "100%",
        padding: "24px",
        background: "#fff",
        borderRadius: "8px",
      }}
    >
      <Title level={4}>
        Table of content
        {/* <EditOutlined /> */}
      </Title>
      {tocData.length > 0 ? (
        <Collapse accordion>
          {tocData.map((section) => (
            <Panel header={section.section_name} key={section.id}>
              {section.doc_info?.length > 0 ? (
                <Menu>
                  {section.doc_info.map((doc) => (
                    <Menu.Item
                      key={doc.id}
                      onClick={() => {
                        dispatch(
                          documentsByTocApi({
                            data: {
                              collection_name: "test_page_info",
                              limit: 10,
                              filter: [{ title: doc.name }],
                            },
                            finalCallback: () => {},
                            successCallback: (response) => {
                              setEndpointResponse(response?.response);
                            },
                            failureCallback: () => {},
                          })
                        );
                      }}
                    >
                      {doc.name}
                    </Menu.Item>
                  ))}
                </Menu>
              ) : (
                <Text type="secondary">No documents available</Text>
              )}
            </Panel>
          ))}
        </Collapse>
      ) : (
        <div
          style={{
            marginTop: "15vh",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
            padding: "20px",
          }}
        >
          <InboxOutlined
            style={{
              fontSize: "64px",
              color: "#bfbfbf",
              marginBottom: "16px",
            }}
          />
          <Text style={{ fontSize: "16px", color: "#595959" }}>
            Table of content will appear here
          </Text>
          <br />
          <Text
            type="secondary"
            style={{ fontSize: "14px", maxWidth: "300px" }}
          >
            After you upload documents, the table of contents will appear here.
          </Text>
        </div>
      )}
    </div>
  );
};

export default TocComponent;
