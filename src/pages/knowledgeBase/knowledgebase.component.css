.file-upload-container {
    margin: 0 auto;
    padding: 20px;
  }
  
  .upload-card,
  .knowledge-base-card {
    margin-bottom: 20px;
  }
  
  .file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    margin-top: 20px;
  }
  
  .file-item {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s;
  }
  
  .file-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .file-icon {
    font-size: 40px;
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  .file-name {
    font-size: 14px;
    margin-bottom: 8px;
    word-break: break-word;
  }
  
  .file-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
  
  .section-title {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  
  .upload-button {
    margin-top: 20px;
  }
  
  .ant-upload-drag {
    border: 2px dashed #1890ff;
  }
  
  .ant-upload-drag:hover {
    border-color: #40a9ff;
  }
  
  .ant-card-head-title {
    font-size: 18px;
    font-weight: bold;
  }
  
  /* New styles for the modal */
  .ant-modal-content {
    height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .ant-modal-body {
    flex-grow: 1;
    padding: 0;
  }
  
  .status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .status-text {
    flex-grow: 1;
  }
  
  .status {
    margin-right: 10px;
    font-weight: 500;
  }
  
  .status-icon {
    margin-left: 10px;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-header .ant-tooltip-inner {
    top: 0;
  }
  
  .card-header span {
    font-size: 18px;
    font-weight: bold;
  }
  
  .card-header .anticon-delete {
    cursor: pointer;
  }
  