import React, { useState, useEffect } from "react";
import { Form, Spin, message } from "antd";
import DocumentEditor from "./EditDoc.jsx";
import httpBase from "../../utils/http.utils.js";
import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks.js";
import { addDocAPI, handleUpdateAPI } from "../../services/dramit.service.js";

const EditDoccontainer = () => {
  const [documents, setDocuments] = useState([]);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDocument, setEditingDocument] = useState(null);
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const [docsResponse, productsResponse, categoriesResponse] =
        await Promise.all([
          httpBase().get("/get_doc"),
          httpBase().get("/get_products"),
          httpBase().get("/get_categories"),
        ]);
      setDocuments(docsResponse.data);
      setProducts(productsResponse.data);
      setCategories(categoriesResponse.data);
    } catch (err) {
      message.error("Failed to load initial data");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    dispatch(
      addDocAPI({
        data: values,
        finalCallback: () => setLoading(false),
        successCallback: (res) => {
          fetchInitialData();
          message.success("Document saved successfully");
          handleCancel();
        },
        failureCallback: (error) => {
          message.error("Error saving document");
        },
      })
    );
  };

  const handleUpdate = async (values) => {
    try {
      if (!editingDocument?._id) {
        throw new Error("No document ID found");
      }

      const updatedDocument = {
        id: editingDocument._id,
        title: values.title,
        text: values.text,
        products: editingDocument.products,
        categories: editingDocument.categories,
      };
      dispatch(
        handleUpdateAPI({
          data: updatedDocument,
          finalCallback: () => {
            console.log("update doc final callback");
          },
          successCallback: (res) => {
            message.success("Document updated successfully");
            handleReload(editingDocument._id);
            setModalVisible(false);
          },
          failureCallback: (error) => {
            message.error("Error saving document");
          },
        })
      );
    } catch (err) {
      message.error(err.message);
    }
  };

  // const handleDelete = async (id) => {
  //   try {
  //     const response = await httpBase().delete(`/delete_doc/${id}`);
  //     if (response.ok) {
  //       // await fetchInitialData();
  //       message.success("Document deleted successfully");
  //     } else {
  //       throw new Error("Failed to delete document");
  //     }
  //   } catch (err) {
  //     message.error("Error deleting document");
  //   }
  // };

  const handleDelete = async (id) => {
    dispatch({
      data: id,
      successCallback: (res) => {
        message.success("Document deleted successfully");
        handleReload(id);
      },
      failureCallback: (error) => {
        message.error("Error deleting document");
      },
    });
  };

  const handleReload = async (documentId) => {
    try {
      await httpBase().post(
        "/process_documents",
        {},
        {
          params: { document_id: documentId },
        }
      );
      message.success("Data reloaded successfully");
    } catch (err) {
      message.error("Error reloading data");
    }
  };

  const handleEdit = (doc) => {
    setEditingDocument(doc);
    form.setFieldsValue(doc);
    setModalVisible(true);
  };

  const handleAddNew = () => {
    setEditingDocument(null);
    form.resetFields();
    setModalVisible(true);
  };

  const getProductName = (id) => {
    const product = products.find((p) => p._id === id);
    return product ? product.name : "Unknown Product";
  };

  const getCategoryName = (id) => {
    const category = categories.find((c) => c._id === id);
    return category ? category.name : "Unknown Category";
  };

  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  if (loading)
    return (
      <div
        style={{
          width: "90vw",
          height: "80vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large" style={{ transform: "scale(1.3)" }} />
      </div>
    );

  return (
    <div>
      <DocumentEditor
        handleSave={handleSave}
        handleUpdate={handleUpdate}
        handleDelete={handleDelete}
        handleReload={handleReload}
        handleEdit={handleEdit}
        handleAddNew={handleAddNew}
        getProductName={getProductName}
        getCategoryName={getCategoryName}
        handleCancel={handleCancel}
        editingDocument={editingDocument}
        modalVisible={modalVisible}
        documents={documents}
        form={form}
      />
    </div>
  );
};

export default EditDoccontainer;
