import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

export default function MessageAndUserTrendChart({ messageData, userData }) {
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current || !messageData || !userData) return;

    const chart = echarts.init(chartRef.current);

    const dates = messageData.map((item) => item._id);
    const messageCounts = messageData.map((item) => item.message_count);
    const userCounts = userData.map((item) => item.user_count);

    const option = {
      title: {
        text: "Daily Messages and New Users Trend",
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "bold",
          color: "#0D47A1",
        },
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["Messages", "New Users"],
        bottom: 0,
      },
      grid: {
        left: "3%",
        right: "3%",
        top: "15%",
        bottom: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: dates,
        boundaryGap: false,
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: "value",
        name: "Count",
      },
      series: [
        {
          name: "Messages",
          type: "line",
          data: messageCounts,
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#4a90e2",
          },
          lineStyle: {
            width: 3,
            color: "#4a90e2",
          },
          areaStyle: {
            color: "rgba(74, 144, 226, 0)",
          },
        },
        {
          name: "New Users",
          type: "line",
          data: userCounts,
          smooth: true,
          symbol: "triangle",
          symbolSize: 8,
          itemStyle: {
            color: "#e94e77",
          },
          lineStyle: {
            width: 3,
            color: "#e94e77",
          },
          areaStyle: {
            color: "rgba(233, 78, 119, 0)",
          },
        },
      ],
    };

    chart.setOption(option);

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener("resize", handleResize);
    };
  }, [messageData, userData]);

  return (
    <div
      style={{
        width: "100%",
        background: "#fff",
        borderRadius: "16px",
        boxShadow: "0 2px 10px rgba(0, 0, 0, 0.1)",
        padding: "1rem 0 0 1rem",
        marginTop: "25px",
      }}
    >
      <div ref={chartRef} style={{ width: "100%", height: "350px" }} />
    </div>
  );
}