import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

export default function LanguageCountPieChart({ data }) {
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;
    
    const chart = echarts.init(chartRef.current);
    
    if (!data || data.length === 0) {
      const emptyOption = {
        title: {
          text: "Messages by Language",
          left: "center",
          textStyle: { fontSize: 16, fontWeight: "bold", color: "#0D47A1" },
          subtext: "No data available",
          subtextStyle: { color: "#999", fontSize: 14 }
        },
        series: [
          {
            type: "pie",
            radius: "70%",
            data: [{ value: 1, name: "No data", itemStyle: { color: "#f2f2f2" } }],
            silent: true,
            label: { show: false }
          }
        ]
      };
      
      chart.setOption(emptyOption);
    }
    else {
      const sortedData = [...data].sort((a, b) => b.count - a.count).slice(0, 4);
      const top5Languages = new Set(sortedData.map(item => item._id.trim()));

      const chartData = data.map((item) => ({
        name: item._id.trim(),
        value: item.count,
        label: {
          show: top5Languages.has(item._id.trim()),
        },
      }));

      const option = {
        title: {
          text: "Messages by Language",
          left: "center",
          textStyle: { fontSize: 16, fontWeight: "bold", color: "#0D47A1" },
        },
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)",
        },
        series: [
          {
            name: "Messages",
            type: "pie",
            radius: "70%",
            data: chartData,
            color: ["#4a90e2", "#1565c0", "#0D47A1", "#1976D2", "#2196F3", "#64B5F6"],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      
      chart.setOption(option);
    }

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener("resize", handleResize);
    };
  }, [data]);

  return (
    <div
      style={{
        width: "50%",
        background: "#fff",
        borderRadius: "16px",
        boxShadow: "0 2px 10px rgba(0, 0, 0, 0.1)",
        padding: "1rem 0 0 0",
        marginTop: "25px",
      }}>
      <div ref={chartRef} style={{ width: "100%", height: "350px" }} />
    </div>
  );
}