import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Dropdown,
  Input,
  List,
  Menu,
  message,
  Modal,
  Row,
  Space,
  Tag,
  Typography,
} from "antd";
import {
  ExclamationCircleFilled,
  SlidersOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  UserAddOutlined,
  DeleteOutlined,
} from "@ant-design/icons";

import SelectOption from "../../../components/common/selectOption";
import SearchInput from "../../../components/common/searchInput";
import DataTablePagination from "../../../components/common/dataTablePagination";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  changeUserRoleApi,
  getAllUsersApi,
  userDeletionApi,
  userPasswordResetApi,
} from "../../../services/users.service";
import InviteUserModal from "../../../components/inviteUserModal";
import { useNavigate } from "react-router-dom";
import { logout } from "../../../store/slices/profile.slice";

const ManageAgentsComponent = () => {
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [selectedUser, setSelectedUser] = useState();
  const [userRole, setUserRole] = useState();
  const [searchKeywords, setSearchKeywords] = useState();

  const { confirm } = Modal;
  const { Text, Title } = Typography;

  // Pagination
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [currentPage, setCurrentPage] = useState();
  const [totalDocuments, setTotalDocuments] = useState();

  // Loading states
  const [loadingUserDelete, setLoadingUserDelete] = useState(false);

  const [usersList, setUsersList] = useState();

  const [isUserInviteModalVisible, setIsUserInviteModalVisible] =
    useState(false);

  const options = [
    {
      label: "Admin",
      value: "admin",
    },
    {
      label: "Agent",
      value: "agent",
    },
  ];

  const handleRoleOnChange = (value) => {
    setPageNumber(1);
    setUserRole(value);
  };

  const handlePasswordReset = (subordinate_id, username) => {
    setLoading(true);
    dispatch(
      userPasswordResetApi({
        reqData: {
          subordinate_id: subordinate_id,
        },
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response) {
            const regex = /reset to ([a-zA-Z0-9]+) successfully/;
            const match = response.message.match(regex);
            const resetPassword = match ? match[1] : "unknown";

            Modal.success({
              title: (
                <div
                  style={{
                    display: "flex",
                    // justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <CheckCircleTwoTone
                    twoToneColor="#52c41a"
                    style={{ fontSize: "24px", marginRight: "10px" }}
                  />
                  <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                    Password Reset Successful
                  </span>
                </div>
              ),
              transitionName: "",
              maskTransitionName: "",
              icon: null,
              width: 600,
              content: (
                <div>
                  <p>{username}'s password has been reset successfully to:</p>
                  <div style={{ textAlign: "center" }}>
                    <Text
                      copyable
                      style={{
                        fontSize: "16px",
                        display: "inline-block",
                        padding: "10px",
                        background: "#f5f5f5",
                        borderRadius: "5px",
                        marginBottom: "10px",
                        textAlign: "center",
                      }}
                    >
                      {resetPassword}
                    </Text>
                  </div>
                  <p>
                    Please relay this password to <strong>{username}</strong>{" "}
                    and ask them to change their password.
                  </p>
                </div>
              ),
            });
          }
        },
        failureCallback: (error) => {
          Modal.error({
            title: "Password Reset Failed",
            transitionName: "",
            maskTransitionName: "",
            content: (
              <div style={{ textAlign: "center" }}>
                <p>
                  Failed to reset the password for {username}. Please try again
                  later.
                </p>
              </div>
            ),
          });
        },
      })
    );
  };

  const showDeleteConfirm = (subordinate_id, username) => {
    confirm({
      title: `Reset password for ${username}?`,
      icon: <ExclamationCircleFilled />,
      content: "This will reset the user's password to a default value.",
      okText: "Yes",
      okType: "danger",
      cancelText: "No",
      transitionName: "",
      maskTransitionName: "",
      onOk() {
        handlePasswordReset(subordinate_id, username);
      },
      onCancel() {
        console.log("Password reset cancelled.");
      },
    });
  };

  const handleRoleChange = (subordinate_id, new_role) => {
    setLoading(true);
    dispatch(
      changeUserRoleApi({
        params: {
          user_id: subordinate_id,
          role_update_request: new_role,
        },
        finalCallback: () => {
          setLoading(false);
          fetchUsers();
        },
        successCallback: (response) => {
          if (response) {
            Modal.success({
              title: "Role Change Successful",
              transitionName: "",
              maskTransitionName: "",
              icon: <CheckCircleTwoTone twoToneColor="#52c41a" />,
              content: <p>{response.message}</p>,
            });
          }
        },
        failureCallback: (error) => {
          Modal.error({
            title: "Role Change Failed",
            transitionName: "",
            maskTransitionName: "",
            icon: <CloseCircleTwoTone twoToneColor="#ff4d4f" />,
            content: (
              <div style={{ textAlign: "center" }}>
                <p>{error.message}</p>
                {error.detail && (
                  <p>
                    <strong>Details:</strong> {error.detail}
                  </p>
                )}
              </div>
            ),
          });
        },
      })
    );
  };

  const roleMenu = (subordinate_id, currentRole) => {
    const allRoles = ["admin", "agent"];
    const availableRoles = allRoles.filter((role) => role !== currentRole);

    return (
      <Menu>
        {availableRoles.map((role) => (
          <Menu.Item
            key={role}
            onClick={() => handleRoleChange(subordinate_id, role)}
          >
            {role.charAt(0).toUpperCase() + role.slice(1)}
          </Menu.Item>
        ))}
      </Menu>
    );
  };

  const fetchUsers = () => {
    setLoading(true);
    dispatch(
      getAllUsersApi({
        params: {
          page_number: pageNumber,
          documents_per_page: pageSize,
          agent_search_query: searchKeywords,
          role_filter: userRole,
        },
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response) {
            setSelectedUser(response?.documents?.[0]);
            setCurrentPage(response?.current_page);
            setTotalDocuments(response?.total_documents);
            setUsersList(response?.documents);
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const handleUserDelete = (id) => {
    setLoadingUserDelete(true);
    dispatch(
      userDeletionApi({
        params: { user_id: id },
        finalCallback: () => {
          setLoadingUserDelete(false);
        },
        successCallback: () => {
          message.success("User deleted successfully!");
          // Inorder to refresh user list
          fetchUsers();
        },
        failureCallback: (errors) => {},
      })
    );
  };

  // Function to check if an item is selected
  const isItemSelected = (item) => {
    return selectedUser && selectedUser._id === item._id;
  };

  const handlePageSizeChange = (current, size) => {
    setPageSize(size);
    setPageNumber(1); // Reset to page 1 when page size changes
  };

  useEffect(() => {
    fetchUsers();
  }, [pageNumber, pageSize, userRole, searchKeywords]);

  return (
    <div>
      <Row gutter={16}>
        {" "}
        {/* Reduced gutter from 36 to 16 */}
        <Col span={14}>
          <div
            style={{
              overflowY: "auto",
              minHeight: "500px",
            }}
          >
            <Card>
              <Title
                level={4}
                style={{ textAlign: "left", marginBottom: "8px" }}
              >
                Manage Agents
              </Title>

              <Divider style={{ margin: "8px 0 16px 0" }} />

              <div
                style={{
                  display: "flex",
                  marginBottom: "20px",
                  gap: "12px",
                }}
              >
                <SearchInput
                  placeholder="Search Agent"
                  setSearchKeywords={setSearchKeywords}
                />
                <SelectOption
                  onChange={handleRoleOnChange}
                  placeholder="Choose Role"
                  options={options}
                  style={{ width: "150px" }}
                />
                <Button
                  style={{
                    borderColor: "#d9d9d9",
                    color: "#595959",
                    fontWeight: "500",
                    marginLeft: "18px",
                  }}
                  icon={<UserAddOutlined />}
                  onClick={() => setIsUserInviteModalVisible(true)}
                >
                  Invite User
                </Button>

                {/* Invite User Modal */}
                <InviteUserModal
                  isUserInviteModalVisible={isUserInviteModalVisible}
                  setIsUserInviteModalVisible={setIsUserInviteModalVisible}
                />
              </div>
              <List
                itemLayout="horizontal"
                dataSource={usersList}
                renderItem={(item) => (
                  <List.Item
                    style={{
                      cursor: "pointer",
                      padding: "16px",
                      backgroundColor: isItemSelected(item)
                        ? "#e6f7ff"
                        : "transparent",
                      transition: "all 0.3s",
                    }}
                    onClick={() => setSelectedUser(item)}
                  >
                    <Space align="center">
                      <Avatar
                        style={{
                          backgroundColor: "grey",
                          marginLeft: "5px",
                          marginRight: "5px",
                        }}
                      >
                        {item?.username.charAt(0).toUpperCase()}
                      </Avatar>
                      <span
                        style={{
                          whiteSpace: "normal",
                          wordBreak: "break-word",
                          fontWeight: isItemSelected(item) ? "500" : "normal",
                        }}
                      >
                        {item?.username}
                      </span>
                      <Tag color="blue">
                        {item?.role.charAt(0).toUpperCase() +
                          item.role.slice(1)}
                      </Tag>
                    </Space>
                    {localStorage.getItem("role") === "admin" && (
                      <Button
                        loading={loadingUserDelete}
                        disabled={loadingUserDelete}
                        icon={<DeleteOutlined />}
                        style={{
                          border: "none",
                          color: "rgba(208, 7, 24, 0.71)",
                          background: "none",
                          boxShadow: "none",
                        }}
                        onClick={(e) => {
                          // Prevent the click event from propagating to the List.Item
                          e.stopPropagation();
                          confirm({
                            title: `Do you want to delete this user?`,
                            icon: <ExclamationCircleFilled />,
                            content: (
                              <p style={{ color: "#444444" }}>
                                This will delete {item?.username}'s (
                                {item?.role.charAt(0).toUpperCase() +
                                  item?.role?.slice(1)}
                                ) account from the system.
                              </p>
                            ),
                            okText: "Yes",
                            okType: "danger",
                            cancelText: "No",
                            transitionName: "",
                            maskTransitionName: "",
                            onOk() {
                              handleUserDelete(item?._id);
                            },
                            onCancel() {
                              console.log("Password delete cancelled.");
                            },
                          });
                        }}
                      />
                    )}
                  </List.Item>
                )}
              />

              <DataTablePagination
                pageSize={pageSize}
                currentPage={pageNumber}
                totalDocuments={totalDocuments}
                setPageSize={setPageSize}
                setPageNumber={setPageNumber}
              />
            </Card>
          </div>
        </Col>
        <Col span={10}>
          <div
            style={{
              overflowY: "auto",
              minHeight: "220px",
            }}
          >
            <Card>
              {selectedUser ? (
                <>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "20px",
                    }}
                  >
                    <Avatar
                      size={64}
                      style={{ backgroundColor: "grey", marginRight: "16px" }}
                    >
                      {selectedUser.username.charAt(0).toUpperCase()}
                    </Avatar>
                    <div>
                      <Title level={4} style={{ margin: 0 }}>
                        {selectedUser.username}
                      </Title>
                      <Tag color="blue">
                        {selectedUser.role.charAt(0).toUpperCase() +
                          selectedUser.role.slice(1)}
                      </Tag>
                    </div>
                  </div>
                  <p>
                    <strong>Created At:</strong>{" "}
                    {new Date(selectedUser.created_at).toLocaleString()}
                  </p>
                  <Space style={{ marginTop: "20px" }}>
                    <Button
                      type="primary"
                      onClick={() =>
                        showDeleteConfirm(
                          selectedUser._id,
                          selectedUser.username
                        )
                      }
                    >
                      <ExclamationCircleOutlined /> Reset Password
                    </Button>
                    <Dropdown
                      overlay={roleMenu(selectedUser._id, selectedUser.role)}
                    >
                      <Button type="primary">
                        <SlidersOutlined /> Change Role
                      </Button>
                    </Dropdown>
                  </Space>
                </>
              ) : (
                <Text type="secondary">Select a user to view details</Text>
              )}
            </Card>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default ManageAgentsComponent;
