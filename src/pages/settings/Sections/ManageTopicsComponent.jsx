import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Button,
  Collapse,
  Modal,
  message,
  Typography,
  Space,
  List,
  Tooltip,
  Divider,
  Skeleton,
  Empty,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  getTopicsApi,
  updateTopic<PERSON>pi,
  deleteTopic<PERSON><PERSON>,
  createTopic<PERSON><PERSON>,
} from "../../../services/topics.service";
import TopicDialog from "./TopicDialog";
import { useNavigate } from "react-router-dom";
import { logout } from "../../../store/slices/profile.slice";

const { Title } = Typography;
const { Panel } = Collapse;

const ManageTopicsComponent = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState("");
  const [selectedTopicId, setSelectedTopicId] = useState(null);
  const [selectedSubtopic, setSelectedSubtopic] = useState(null);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getTopicsApi({
        params: {},
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (data) => {
          setTopics(data || []);
        },
        failureCallback: (error) => {
          message.error(
            "Failed to fetch topics: " + (error.message || "Unknown error")
          );
        },
      })
    );
  }, [dispatch]);

  const showModal = (type, topicId = null, subtopic = null) => {
    setModalType(type);
    setSelectedTopicId(topicId);
    setSelectedSubtopic(subtopic);
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setModalType("");
    setSelectedTopicId(null);
    setSelectedSubtopic(null);
  };

  const handleCreate = (values) => {
    const payload = {
      name: values.name,
      type: values.type,
      ...(values.type === "subtopic" && { parent_id: values.parent_id }),
    };
    dispatch(
      createTopicApi({
        params: payload,
        finalCallback: () => {},
        successCallback: () => {
          dispatch(
            getTopicsApi({
              params: {},
              finalCallback: () => {},
              successCallback: (data) => {
                setTopics(data || []);
                message.success(`New ${values.type} added`);
              },
              failureCallback: (error) => {
                message.error(
                  "Failed to refresh topics: " +
                    (error.message || "Unknown error")
                );
              },
            })
          );
          handleModalCancel();
        },
        failureCallback: (error) => {
          message.error(
            "Failed to add item: " + (error.message || "Unknown error")
          );
        },
      })
    );
  };

  const handleUpdate = (values) => {
    const payload = {
      id: values.type === "topic" ? selectedTopicId : selectedSubtopic.id,
      name: values.name,
      type: values.type,
      ...(values.type === "subtopic" && { parent_id: values.parent_id }),
    };
    dispatch(
      updateTopicApi({
        params: payload,
        finalCallback: () => {},
        successCallback: () => {
          dispatch(
            getTopicsApi({
              params: {},
              finalCallback: () => {},
              successCallback: (data) => {
                setTopics(data || []);
                message.success(
                  `${
                    values.type.charAt(0).toUpperCase() + values.type.slice(1)
                  } updated`
                );
              },
              failureCallback: (error) => {
                message.error(
                  "Failed to refresh topics: " +
                    (error.message || "Unknown error")
                );
              },
            })
          );
          handleModalCancel();
        },
        failureCallback: (error) => {
          message.error(
            "Failed to update item: " + (error.message || "Unknown error")
          );
        },
      })
    );
  };

  const handleDeleteTopic = (topicId) => {
    const topic = topics.find((t) => t.id === topicId);
    Modal.confirm({
      title: "Delete Topic",
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to delete "${topic.name}"?`,
      transitionName: "",
      maskTransitionName: "",
      onOk: () => {
        dispatch(
          deleteTopicApi({
            params: {
              topic_id: topicId,
              topic_type: topic.type,
            },
            finalCallback: () => {},
            successCallback: () => {
              setTopics(topics.filter((t) => t.id !== topicId));
              message.success("Topic deleted successfully");
            },
            failureCallback: (error) => {
              message.error(
                "Failed to delete topic: " + (error.message || "Unknown error")
              );
            },
          })
        );
      },
    });
  };

  const handleDeleteSubtopic = (topicId, subtopicId) => {
    const topic = topics.find((t) => t.id === topicId);
    const subtopic = topic.sub_topics.find((s) => s.id === subtopicId);
    Modal.confirm({
      title: "Delete Subtopic",
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to delete "${subtopic.name}"?`,
      transitionName: "",
      maskTransitionName: "",
      onOk: () => {
        dispatch(
          deleteTopicApi({
            params: {
              topic_id: subtopicId,
              topic_type: "subtopic",
            },
            finalCallback: () => {},
            successCallback: () => {
              setTopics(
                topics.map((t) =>
                  t.id === topicId
                    ? {
                        ...t,
                        sub_topics: t.sub_topics.filter(
                          (s) => s.id !== subtopicId
                        ),
                      }
                    : t
                )
              );
              message.success("Subtopic deleted successfully");
            },
            failureCallback: (error) => {
              message.error(
                "Failed to delete subtopic: " +
                  (error.message || "Unknown error")
              );
            },
          })
        );
      },
    });
  };

  return (
    <Card>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 0",
          paddingTop: "0",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", height: "100%" }}>
          <Title level={4} style={{ marginBottom: 0, marginTop: 0 }}>
            Manage Topics
          </Title>
        </div>
        <div style={{ display: "flex", alignItems: "center", height: "100%" }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => showModal("add-topic")}
          >
            Add Topic
          </Button>
        </div>
      </div>
      {loading ? (
        <Skeleton
          active
          paragraph={{ rows: 6 }}
          title={false}
          style={{ marginTop: 16 }}
        />
      ) : (
        <>
          {topics.length > 0 ? (
            <Collapse accordion>
              {topics.map((topic) => (
                <Panel
                  header={
                    <Space>
                      <span>{topic.name}</span>
                      <Tooltip title="Edit Topic">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            showModal("edit-topic", topic.id);
                          }}
                        />
                      </Tooltip>
                      <Divider type="vertical" />
                      <Tooltip title="Delete Topic">
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          danger
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTopic(topic.id);
                          }}
                        />
                      </Tooltip>
                    </Space>
                  }
                  key={topic.id}
                >
                  <List
                    dataSource={topic.sub_topics || []}
                    renderItem={(subtopic) => (
                      <List.Item
                        style={{
                          marginLeft: "48px",
                        }}
                        actions={[
                          <Tooltip title="Edit Subtopic">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              onClick={() =>
                                showModal("edit-subtopic", topic.id, subtopic)
                              }
                            />
                          </Tooltip>,
                          <Tooltip title="Delete Subtopic">
                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              danger
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteSubtopic(topic.id, subtopic.id);
                              }}
                            />
                          </Tooltip>,
                        ]}
                      >
                        <span>{subtopic.name}</span>
                      </List.Item>
                    )}
                    footer={
                      <Button
                        type="dashed"
                        icon={<PlusOutlined />}
                        onClick={() => showModal("add-subtopic", topic.id)}
                        block
                      >
                        Add Subtopic
                      </Button>
                    }
                  />
                </Panel>
              ))}
            </Collapse>
          ) : (
            <Empty
              description={
                <Space direction="vertical" size="small">
                  <Typography.Text type="secondary">
                    No topics found.
                  </Typography.Text>
                </Space>
              }
            />
          )}
        </>
      )}
      <TopicDialog
        visible={isModalVisible}
        modalType={modalType}
        topics={topics}
        selectedTopicId={selectedTopicId}
        selectedSubtopic={selectedSubtopic}
        onCreate={handleCreate}
        onUpdate={handleUpdate}
        onCancel={handleModalCancel}
      />
    </Card>
  );
};

export default ManageTopicsComponent;
