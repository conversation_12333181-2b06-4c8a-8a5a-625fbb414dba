import React, { useEffect, useState } from 'react';
import { Card, message, Table, Typography, Spin, Pagination, Row, Col, Progress, Select, DatePicker, Tooltip, Statistic, Skeleton, Button } from 'antd';
import { useAppDispatch } from '../../../hooks/reduxHooks';
import { getCreditHistoryApi, getCreditBalanceApi } from '../../../services/credit.service';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const UsageDashboard = () => {
  const [creditHistoryTransactions, setCreditHistoryTransactions] = useState([]);
  const [meta, setMeta] = useState({
    total: 0,
    page: 1,
    limit: 10,
  });
  const [creditBalance, setCreditBalance] = useState({
    total_credits: 0,
    remaining: 0,
  });
  const [transactionType, setTransactionType] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [transactionCounts, setTransactionCounts] = useState({
    addition: 0,
    usage: 0,
  });
  // Updated loading state to an object
  const [loading, setLoading] = useState({
    balance: false,
    transactions: false,
  });

  const dispatch = useAppDispatch();

  const usedCredits = creditBalance.total_credits - creditBalance.remaining;
  const remainingPercentage = creditBalance.total_credits ? ((creditBalance.remaining / creditBalance.total_credits) * 100).toFixed(2) : 0;

  const fetchTransactions = (page = meta.page, limit = meta.limit) => {
    setLoading((prev) => ({ ...prev, transactions: true }));

    const params = {
      transaction_type: transactionType,
      limit: limit,
      page: page,
    };

    if (Array.isArray(dateRange) && dateRange[0] && dateRange[1]) {
      params.start_date = dayjs(dateRange[0]).format('YYYY-MM-DD');
      params.end_date = dayjs(dateRange[1]).format('YYYY-MM-DD');
    }

    dispatch(getCreditHistoryApi({
      params,
      successCallback: (response) => {
        setCreditHistoryTransactions(response?.transactions || []);
        setMeta({
          total: response?.total || 0,
          page: response?.page || page,
          limit: response?.limit || limit,
        });

        const counts = {
          addition: response?.total_cost?.addition || 0,
          usage: response?.total_cost?.usage || 0,  
        };

        setTransactionCounts(counts);
      },
      failureCallback: (errors) => {
        console.error('API Error: ', errors);
        message.error('Failed to fetch credit history.');
      },
      finalCallback: () => {
        setLoading((prev) => ({ ...prev, transactions: false }));
      },
    }));
  };

  const handleTransactionTypeChange = (value) => {
    setTransactionType(value);
    setMeta((prev) => ({ ...prev, page: 1 }));
  };

  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    setMeta((prev) => ({ ...prev, page: 1 }));
  };

  const handleResetFilters = () => {
    setTransactionType(null);
    setDateRange(null);
    setMeta((prev) => ({ ...prev, page: 1 }));
  };

  useEffect(() => {
    fetchTransactions(meta.page, meta.limit);
  }, [dispatch, transactionType, dateRange]);

  useEffect(() => {
    setLoading((prev) => ({ ...prev, balance: true }));
    dispatch(getCreditBalanceApi({
      successCallback: (response) => {
        setCreditBalance({
          total_credits: response?.balance?.total_credits || 0,
          remaining: response?.balance?.remaining || 0,
        });
      },
      failureCallback: (errors) => {
        console.error('API Error: ', errors);
        message.error('Failed to fetch credit balance.');
      },
      finalCallback: () => {
        setLoading((prev) => ({ ...prev, balance: false }));
      },
    }));
  }, [dispatch]);

  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text) => <>{formatDate(text)}</>,
    },
    {
      title: 'Transaction Type',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      render: (text) => (
        <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text[0].toUpperCase() + text.slice(1)}
        </div>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (text, record) => {
        const isAddition = record.transaction_type === 'addition';
        const color = isAddition ? 'green' : 'red';
        const tooltipText = isAddition ? 'Credit Added' : 'Credit Used';

        return (
          <Tooltip title={tooltipText} placement="topLeft">
            <div style={{ color, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {text} credits
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text) => (
        <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{text}</div>
      ),
    },
  ];

  const handlePaginationChange = (page, pageSize) => {
    setMeta((prev) => ({ ...prev, page, limit: pageSize }));
    fetchTransactions(page, pageSize);
  };

  return (
    <>
      <style>
        {`
          .shadow-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }
          .info-card {
            background-color: #f9f9fa;
            border: 1px solid #e2e8f0;
            height: 100%;
          }
          .list-disc {
            list-style-type: disc;
            padding-left: 20px;
          }
          .list-circle {
            list-style-type: circle;
            padding-left: 20px;
            margin-top: 4px;
          }
          .card-title {
            color: #4a5568;
            margin-bottom: 16px;
          }
          .card-text {
            color: #718096;
          }
          .equal-height-row {
            display: flex;
            flex-wrap: wrap;
          }
          .equal-height-col {
            display: flex;
            flex-direction: column;
          }
          .equal-height-card {
            flex: 1;
            display: flex;
            flex-direction: column;
          }
          .stats-card {
            background-color: #f9f9fa;
            border: 1px solid #e2e8f0;
            margin-bottom: 24px;
            padding: 16px;
            border-radius: 4px;
          }
        `}
      </style>

      <Card style={{ marginBottom: '24px' }} className="shadow-card">
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          <Title level={4} style={{ textAlign: 'left', marginBottom: '24px', color: '#2d3748' }}>
            Credit Balance Overview
          </Title>
          <Row gutter={[16, 16]} className="equal-height-row">
            <Col xs={24} md={12} className="equal-height-col">
              <Card className="info-card equal-height-card">
                <Title level={5} className="card-title">Understanding Credits</Title>
                <Text className="card-text">
                  <ul className="list-disc">
                    <li>
                      <strong>₹ 1 = 2 credits</strong>: Credits are purchased at this rate.
                    </li>
                    <li>
                      <strong>Sending a message costs 3 credits</strong>:
                      <ul className="list-circle">
                        <li>1 credit for WhatsApp API</li>
                        <li>2 credits for AI response</li>
                      </ul>
                    </li>
                    <li>
                      <strong>Usage Breakdown</strong>: Of the total credits used, <strong>66%</strong> is for AI
                      processing, and <strong>34%</strong> is for WhatsApp API.
                    </li>
                  </ul>
                </Text>
              </Card>
            </Col>
            <Col xs={24} md={12} className="equal-height-col">
              <Card className="info-card equal-height-card">
                <Title level={5} className="card-title">Credit Summary</Title>
                {loading.balance ? (
                  <Skeleton active paragraph={{ rows: 4 }} />
                ) : (
                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={8} style={{ textAlign: 'center' }}>
                      <Statistic
                        title="Total Credits"
                        value={creditBalance.total_credits}
                        prefix={
                          <span style={{ marginRight: '5px', verticalAlign: 'baseline' }}>
                            <iconify-icon icon="bxs:coin-stack" />
                          </span>
                        }
                      />
                    </Col>
                    <Col xs={24} sm={8} style={{ textAlign: 'center' }}>
                      <Statistic
                        title="Used Credits"
                        value={usedCredits}
                        prefix={
                          <span style={{ marginRight: '5px', verticalAlign: 'baseline' }}>
                            <iconify-icon icon="mingcute:check-2-fill" />
                          </span>
                        }
                      />
                    </Col>
                    <Col xs={24} sm={8} style={{ textAlign: 'center' }}>
                      <Statistic
                        title="Remaining Credits"
                        value={creditBalance.remaining}
                        prefix={
                          <span style={{ marginRight: '5px', verticalAlign: 'baseline' }}>
                            <iconify-icon icon="bxs:coin-stack" />
                          </span>
                        }
                      />
                    </Col>
                    <Col xs={24} style={{ marginTop: '10px' }}>
                      <Text style={{ color: '#718096' }}>Remaining Credits</Text>
                      <Progress
                        percent={remainingPercentage}
                        status="active"
                        strokeColor={{
                          '0%': '#108ee9',
                          '100%': '#87d068',
                        }}
                      />
                    </Col>
                  </Row>
                )}
              </Card>
            </Col>
          </Row>
        </div>
      </Card>

      <Card className="shadow-card">
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          <Title level={4} style={{ textAlign: 'left', marginBottom: '16px', color: '#2d3748' }}>
            Billing & Usage
          </Title>

          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={6}>
              <Select
                placeholder="Transaction Type"
                style={{ width: '100%' }}
                value={transactionType}
                onChange={handleTransactionTypeChange}
                allowClear
              >
                <Select.Option value="addition">Addition</Select.Option>
                <Select.Option value="usage">Usage</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6}>
              <RangePicker
                style={{ width: '100%' }}
                onChange={handleDateRangeChange}
                value={dateRange}
              />
            </Col>
            <Col xs={24} sm={6}>
              <Button
                onClick={handleResetFilters}
                variant='outlined'
                type='primary'
                color='danger'
              >
                Reset Filters
              </Button>
            </Col>
          </Row>

          <Card
            className="stats-card"
            bordered={false}
            style={{ padding: '0', marginBottom: 16, backgroundColor: '#f9f9fa' }}
          >
            <Row justify="space-between" align="middle" style={{ fontSize: '12px', color: '#4A5568', fontWeight: '500' }}>
              <Col>
                <span style={{ color: '#38a169' }}>
                  <iconify-icon icon="ph:plus-circle-fill" style={{ marginRight: 4, verticalAlign: 'middle' }} />
                  Credits Addition: {loading.transactions ? <Skeleton.Input active size="small" style={{ width: "10px", marginLeft: "8px" }} /> : transactionCounts.addition}
                </span>
              </Col>
              <Col>
                <span style={{ color: '#e53e3e' }}>
                  <iconify-icon icon="ph:minus-circle-fill" style={{ marginRight: 4, verticalAlign: 'middle' }} />
                  Credits Usage: {loading.transactions ? <Skeleton.Input active size="small" style={{ width: "10px", marginLeft: "8px" }} /> : transactionCounts.usage}
                </span>
              </Col>
              <Col>
                {/* <span style={{ color: '#718096' }}>
                  {dateRange?.[0] && dateRange?.[1]
                    ? `${dayjs(dateRange[0]).format('MMM D')} - ${dayjs(dateRange[1]).format('MMM D')}`
                    : 'All'}
                </span> */}
                  &nbsp;
              </Col>
            </Row>
          </Card>

          {loading.transactions && creditHistoryTransactions.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Spin size="large" tip="Loading transaction history..." />
            </div>
          ) : creditHistoryTransactions.length > 0 ? (
            <>
              <div style={{ overflowX: 'auto', width: '100%', whiteSpace: 'nowrap' }}>
                <Table
                  dataSource={creditHistoryTransactions}
                  columns={columns}
                  rowKey="transaction_id"
                  pagination={false}
                  bordered
                  loading={loading.transactions}
                />
              </div>
              <div style={{ marginTop: '24px', textAlign: 'right' }}>
                <Pagination
                  current={meta.page}
                  pageSize={meta.limit}
                  total={meta.total}
                  onChange={handlePaginationChange}
                  showSizeChanger
                  showTotal={(total) => `Total ${total} items`}
                  pageSizeOptions={['10', '20', '50']}
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
              </div>
            </>
          ) : (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Typography.Text>No transaction history available.</Typography.Text>
            </div>
          )}
        </div>
      </Card>
    </>
  );
};

export default UsageDashboard;