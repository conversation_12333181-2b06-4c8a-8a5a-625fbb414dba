import React, { useEffect } from 'react';
import { Form, Button } from 'antd';

const TestFormValues = () => {
  const [form] = Form.useForm();

  useEffect(() => {
    // Simulate the API response data
    const mockApiData = {
      used_tools: ['ticket', 'technical_support'],
      booking_types: ['booking'],
      booking_required_info: ['Full Name', 'Preferred Date'],
      ticket_required_info: ['Full Name', 'Email Address']
    };

    console.log('Mock API Data:', mockApiData);

    // Map to form data
    const formData = {
      used_tools: mockApiData.used_tools || [],
      booking: mockApiData.booking_types || mockApiData.booking || [],
      booking_required_info: mockApiData.booking_required_info || [],
      ticket_required_info: mockApiData.ticket_required_info || []
    };

    console.log('Form Data to set:', formData);
    form.setFieldsValue(formData);

    // Check what was actually set
    setTimeout(() => {
      console.log('Form values after setting:', form.getFieldsValue());
      console.log('used_tools value:', form.getFieldValue('used_tools'));
      console.log('booking value:', form.getFieldValue('booking'));
    }, 100);
  }, [form]);

  const handleCheck = () => {
    const values = form.getFieldsValue();
    console.log('Current form values:', values);
    alert(JSON.stringify(values, null, 2));
  };

  return (
    <div style={{ padding: 20 }}>
      <h3>Form Values Test</h3>
      <Form form={form}>
        <Form.Item name="used_tools" label="Used Tools">
          <div>Value: {JSON.stringify(form.getFieldValue('used_tools'))}</div>
        </Form.Item>
        <Form.Item name="booking" label="Booking">
          <div>Value: {JSON.stringify(form.getFieldValue('booking'))}</div>
        </Form.Item>
        <Form.Item name="booking_required_info" label="Booking Required Info">
          <div>Value: {JSON.stringify(form.getFieldValue('booking_required_info'))}</div>
        </Form.Item>
        <Form.Item name="ticket_required_info" label="Ticket Required Info">
          <div>Value: {JSON.stringify(form.getFieldValue('ticket_required_info'))}</div>
        </Form.Item>
      </Form>
      <Button onClick={handleCheck}>Check Values</Button>
    </div>
  );
};

export default TestFormValues;
