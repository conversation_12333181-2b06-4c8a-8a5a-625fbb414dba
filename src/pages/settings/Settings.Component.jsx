import React, { useEffect, useState } from "react";
import { <PERSON>, Col, Menu, Row, Typography } from "antd";
import {
  MailOutlined,
  DollarOutlined,
  LockOutlined,
  TagsOutlined,
  UsergroupAddOutlined,
  WhatsAppOutlined,
  RobotOutlined,
  SettingOutlined,
  EditOutlined,
} from "@ant-design/icons";
import ChangePassword from "./Sections/ChangePassword";
import ManageTopicsComponent from "./Sections/ManageTopicsComponent";
import ManageAgentsComponent from "./Sections/ManageAgentsComponent";
import WhatsappSetting from "./Sections/WhatsappSetting";
import { useSearchParams } from "react-router-dom";
import EmailSettings from "./Sections/EmailSettings";
import BillingUsagePage from "./Sections/UsageComponent";
import AiSettingsComponents from "./Sections/AISettingsComponent";
import AISetupComponent from "./Sections/AISetupComponent";
import { getAllSettingsApi } from "../../services/settings.service";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { transformStringForDash } from "../../helpers/channelNameTransformation";

const { Title } = Typography;

const SettingsComponent = ({ setLoading }) => {
  const dispatch = useAppDispatch();
  const role = localStorage.getItem("role");
  const [searchParams, setSearchParams] = useSearchParams();
  const [settingFirstItem, setSettingFirstItem] = useState();
  const activeTab = searchParams.get("tab") || settingFirstItem;

  const [menuItems, setMenuItems] = useState([]);

  const handleMenuClick = ({ key }) => {
    setSearchParams({ tab: key });
  };

  const renderContent = () => {
    const isTabInMenu = menuItems.some((item) => item.key === activeTab);
    if (!isTabInMenu) return null;

    switch (activeTab) {
      case "ai-settings":
        return <AiSettingsComponents />;
      case "ai-setup":
        return <AISetupComponent />;
      case "change-password":
        return <ChangePassword />;
      case "manage-topics":
        return <ManageTopicsComponent />;
      case "manage-agents":
        return <ManageAgentsComponent />;
      case "whatsapp-settings":
        return <WhatsappSetting />;
      case "billing-usage":
        return <BillingUsagePage setLoading={setLoading} />;
      case "email-settings":
        return <EmailSettings />;
      default:
        return null;
    }
  };

  const fetchAllSettings = () => {
    setLoading(true);
    dispatch(
      getAllSettingsApi({
        params: {
          filter_route: "settings",
        },
        successCallback: (response) => {
          // Add ai-setup to the menu items if not already present
          const menuItems = response?.setting_menu || [];
          if (!menuItems.includes("ai-setup")) {
            menuItems.push("ai-setup");
          }

          setSettingFirstItem(menuItems[0]);
          const menuItemsList = menuItems?.map((item) => {
            return {
              key: item,
              icon:
                item === "ai-settings" ? (
                  <RobotOutlined />
                ) : item === "ai-setup" ? (
                  <EditOutlined />
                ) : item === "change-password" ? (
                  <LockOutlined />
                ) : item === "manage-topics" ? (
                  <TagsOutlined />
                ) : item === "manage-agents" ? (
                  <UsergroupAddOutlined />
                ) : item === "whatsapp-settings" ? (
                  <WhatsAppOutlined />
                ) : item === "billing-usage" ? (
                  <DollarOutlined />
                ) : item === "email-settings" ? (
                  <MailOutlined />
                ) : (
                  <SettingOutlined />
                ),
              label: transformStringForDash(item),
            };
          });

          setMenuItems(menuItemsList);
        },
        failureCallback: (error) => {},
        finalCallback: () => {
          setLoading(false);
        },
      })
    );
  };

  useEffect(() => {
    fetchAllSettings();
  }, []);

  return (
    <div>
      <style>
        {`
          .settings-menu .ant-menu-item-selected {
            background-color: #e6f7ff !important;
            color: #1d39c4 !important;
          }
          .settings-menu .ant-menu-item-selected .anticon {
            color: #1d39c4 !important;
          }
          .settings-menu .ant-menu-item:hover {
            background-color: #d6eaff !important;
            color: #1d39c4 !important;
          }
          .settings-menu .ant-menu-item-selected:hover {
            background-color: #e6f7ff !important;
            color: #1d39c4 !important;
          }
          .settings-menu .ant-menu-item-selected:hover .anticon {
            color: #1d39c4 !important;
          }
          .sidebar-sticky {
            position: sticky;
            top: 0px;
            max-height: calc(100vh - 32px);
            overflow-y: auto;
          }
          .settings-container {
            min-height: calc(100vh - 32px);
            padding-bottom: 50px;
          }
        `}
      </style>
      <Row gutter={24} className="settings-container">
        <Col span={6}>
          <div className="sidebar-sticky">
            <Card>
              <Title
                level={4}
                style={{
                  marginBottom: "8px",
                  paddingBottom: "8px",
                  borderBottom: "1px solid #e6f7ff",
                }}
              >
                Settings
              </Title>
              <Menu
                className="settings-menu"
                mode="vertical"
                selectedKeys={[activeTab]}
                onClick={handleMenuClick}
                items={menuItems}
                style={{ border: "none" }}
              />
            </Card>
          </div>
        </Col>
        <Col span={18}>{renderContent()}</Col>
      </Row>
    </div>
  );
};

export default SettingsComponent;
