import React, { useState } from "react";
import {
  Card,
  Typography,
  Avatar,
  Space,
  Input,
  Button,
  Upload,
  Image,
  Modal,
  Form,
  Divider,
  Select,
  message,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  SendOutlined,
  PaperClipOutlined,
  SyncOutlined,
  LikeOutlined,
  DislikeOutlined,
  LikeFilled,
  DislikeFilled,
} from "@ant-design/icons";
import { styles } from "../../styles";
import RenderImageCollage from "./ChatImage";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import {
  handleFeedbackApi,
  handleFeedbackDeleteApi,
} from "../../../../services/feedbackLog.service";
import FeedbackModal from "./FeedbackModal";
import "./index.css";

const { Text, Title, Paragraph } = Typography;
const { TextArea } = Input;

// Format the timestamp
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
};

// Parse reply text with line breaks
const parseReplyText = (text) => {
  return text.split("\n").map((line, index) => (
    <span key={index}>
      {line}
      {index < text.split("\n").length - 1 && <br />}
    </span>
  ));
};

const SingleChatItem = ({
  message: msng,
  convIndex,
  msgIndex,
  setVisible,
  setResponseData,
  setDrawerData,
}) => {
  const dispatch = useAppDispatch();

  // State for like/dislike buttons - initialize based on evaluation_status
  const [liked, setLiked] = useState(msng?.evaluation_status === "like");
  const [disliked, setDisliked] = useState(
    msng?.evaluation_status === "dislike"
  );
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);

  // Extract images from message if they exist
  const images = msng.images || [];
  const hasMedia = msng?.media_ids && msng?.media_ids?.length > 0;

  // State for feedback modal
  const [form] = Form.useForm();

  // Split message content by double newlines for assistant messages
  const messageParts =
    msng?.role === "assistant" && msng?.content
      ? msng?.content
          .replace(/\\n/g, "") // Unescape newlines
          .replace(/\\n{2}/g, " \n") // Exactly 2 newlines → space
          .split(/\n{3,}/) // Split on 3+ newlines
          .filter((part) => part.trim())
      : [];

  // Handle like button click
  const handleLike = (e) => {
    e.stopPropagation(); // Prevent bubble click event

    // If already liked, undo the like
    if (liked) {
      dispatch(
        handleFeedbackDeleteApi({
          data: {
            message_id: msng?.message_id,
          },
          finalCallback: () => {},
          successCallback: (response) => {
            setLiked(false);
            console.log("Like removed -> ", msng);
            message.warning("Unliked!");
          },
          failureCallback: () => {},
        })
      );
      return;
    }

    dispatch(
      handleFeedbackApi({
        data: {
          message_id: msng?.message_id,
          evaluation: "like",
          remark: null,
          categories: null,
          reviewer_id: localStorage.getItem("userId"),
          created_at: null,
        },
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("Message liked -> ", msng);
          setLiked(true);
          if (disliked) setDisliked(false);
          message.success("Liked!");
        },
        failureCallback: () => {},
      })
    );
  };

  // Handle dislike button click
  const handleDislike = (e) => {
    e.stopPropagation(); // Prevent bubble click event

    // If already disliked, just undo it without showing modal
    if (disliked) {
      dispatch(
        handleFeedbackDeleteApi({
          data: {
            message_id: msng?.message_id,
          },
          finalCallback: () => {},
          successCallback: (response) => {
            setDisliked(false);
            console.log("Dislike removed:", msng);
            message.warning("Dislike removed!");
          },
          failureCallback: () => {},
        })
      );
      return;
    }

    setFeedbackModalVisible(true);
    console.log("Message disliked:", msng);
  };

  // Function to check if a URL is a YouTube link and extract video ID
  const getYouTubeVideoId = (url) => {
    const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(youtubeRegex);
    return match ? match[1] : null;
  };

  // Function to render media from reply_urls
  const renderReplyUrls = (urls) => {
    if (!urls || urls.length === 0) return null;

    return (
      <div style={{ display: "flex", flexWrap: "wrap", gap: 8, marginTop: 8 }}>
        {urls.map((url, index) => {
          const videoId = getYouTubeVideoId(url);
          if (videoId) {
            // Render YouTube thumbnail
            return (
              <a
                key={index}
                href={url}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src={`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`}
                  alt="YouTube video thumbnail"
                  style={{
                    width: 120,
                    height: 90,
                    objectFit: "cover",
                    borderRadius: 4,
                  }}
                  preview={false}
                />
              </a>
            );
          } else {
            // Render image
            return (
              <Image
                key={index}
                src={url}
                alt="Attached image"
                style={{
                  maxWidth: 200,
                  maxHeight: 200,
                  objectFit: "contain",
                  borderRadius: 4,
                }}
                fallback="https://via.placeholder.com/200?text=Image+Not+Found"
                onError={(e) => {
                  e.target.src = "https://via.placeholder.com/200?text=Image+Not+Found";
                }}
              />
            );
          }
        })}
      </div>
    );
  };

  return (
    <div
      key={`conv-${convIndex}-msg-${msgIndex}`}
      style={{
        marginBottom: "16px",
      }}
    >
      {msng?.role === "assistant" ? (
        <Space align="start" direction="vertical" style={{ width: "100%" }}>
          <Space align="start">
            <Avatar
              icon={<RobotOutlined />}
              style={{ backgroundColor: "#1890ff" }}
            />
            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              {/* Map through message parts to create multiple bubbles */}
              {messageParts?.length > 0 ? (
                messageParts?.map((part, partIndex) => (
                  <div
                    key={partIndex}
                    style={{
                      backgroundColor: "#e6f7ff",
                      borderRadius: "8px",
                      padding: "12px",
                      maxWidth: "80%",
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      setDrawerData(msng);
                      setVisible(true);
                      setResponseData({ ...msng, content: part });
                    }}
                  >
                    <Paragraph style={{ marginBottom: 0 }}>
                      {parseReplyText(part)}
                    </Paragraph>
                  </div>
                ))
              ) : (
                // Fallback for empty content or no double newlines
                <div
                  style={{
                    backgroundColor: "#e6f7ff",
                    borderRadius: "8px",
                    padding: "12px",
                    maxWidth: "80%",
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    setDrawerData(msng);
                    setVisible(true);
                    setResponseData(msng);
                  }}
                >
                  <Paragraph style={{ marginBottom: 0 }}>
                    {parseReplyText(msng.content || "")}
                  </Paragraph>
                </div>
              )}
            </div>
          </Space>

          {/* Render reply_urls in a separate container below the message */}
          {msng?.reply_urls?.length > 0 && (
            <div style={{ marginLeft: "45px", marginTop: "8px" }}>
              {renderReplyUrls(msng?.reply_urls)}
            </div>
          )}

          {/* Like/Dislike buttons - moved below reply_urls */}
          <Space style={{ marginLeft: "50px", marginTop: "8px" }}>
            <Button
              type={liked ? "primary" : "text"}
              size="small"
              icon={liked ? <LikeFilled /> : <LikeOutlined />}
              onClick={handleLike}
            >
              {liked ? "Like" : "Like"}
            </Button>

            <Button
              type={disliked ? "primary" : "text"}
              size="small"
              danger={disliked}
              icon={disliked ? <DislikeFilled /> : <DislikeOutlined />}
              onClick={handleDislike}
            >
              {disliked ? "Dislike" : "Dislike"}
            </Button>
          </Space>

          {/* Render metadata images if they exist */}
          {msng?.metadata?.length > 0 &&
            msng?.metadata?.some((item) => item?.image_urls?.length > 0) && (
              <div style={{ marginLeft: "45px", marginTop: "8px" }}>
                {msng?.metadata?.map(
                  (item, index) =>
                    item?.image_urls?.length > 0 && (
                      <RenderImageCollage
                        key={index}
                        images={item?.image_urls?.map((img) => img)}
                      />
                    )
                )}
              </div>
            )}

          {/* Timestamp and processing time */}
          <div style={{ marginLeft: "45px" }}>
            <Text
              type="secondary"
              style={{
                textAlign: "right",
                marginRight: "30px",
                fontSize: "12px",
              }}
            >
              {formatTime(msng?.created_at || new Date().toISOString())}
              {msng?.processing_time && (
                <>
                  {` · ${msng?.processing_time.toFixed(2)}s`}
                </>
              )}
            </Text>
          </div>
        </Space>
      ) : (
        <div>
          <div
            style={{
              ...styles.userMessageContainer,
              flexDirection: "column",
              alignItems: "flex-end",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              {msng?.content ? (
                <>
                  <div style={styles.userMessageContent}>
                    <Paragraph style={{ marginBottom: 0 }}>
                      {msng?.content}
                    </Paragraph>
                  </div>
                </>
              ) : (
                <>
                  <div style={{ marginBottom: "16px" }}>
                    <div
                      className="message-loading"
                      style={{
                        backgroundColor: "rgb(220, 245, 200)",
                      }}
                    >
                      <div className="bouncing-dots">
                        <span
                          style={{ backgroundColor: "rgb(46, 139, 87)" }}
                        ></span>
                        <span
                          style={{ backgroundColor: "rgb(46, 139, 87)" }}
                        ></span>
                        <span
                          style={{ backgroundColor: "rgb(46, 139, 87)" }}
                        ></span>
                      </div>
                    </div>
                  </div>
                </>
              )}

              <Avatar
                icon={<UserOutlined />}
                style={{
                  backgroundColor: "#87d068",
                  marginLeft: "8px",
                }}
              />
            </div>

            {/* Render media_ids if they exist */}
            {hasMedia && (
              <div style={{ marginTop: "8px", maxWidth: "80%" }}>
                {typeof msng.media_ids === "string" ? (
                  <Card style={{ backgroundColor: "#f0f0f0" }}>
                    {msng?.media_ids}
                  </Card>
                ) : (
                  <RenderImageCollage images={msng?.media_ids} />
                )}
              </div>
            )}
          </div>

          {/* Render reply_urls in a separate container below the message */}
          {msng?.reply_urls?.length > 0 && (
            <div style={{ marginTop: "8px", marginRight: "30px", textAlign: "right" }}>
              {renderReplyUrls(msng?.reply_urls)}
            </div>
          )}

          {/* Timestamp */}
          <div
            style={{
              marginTop: "4px",
              textAlign: "right",
              marginRight: "30px",
            }}
          >
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {formatTime(msng?.created_at || new Date().toISOString())}
            </Text>
          </div>
        </div>
      )}

      <FeedbackModal
        message={msng}
        feedbackModalVisible={feedbackModalVisible}
        setFeedbackModalVisible={setFeedbackModalVisible}
        liked={liked}
        setLiked={setLiked}
        setDisliked={setDisliked}
      />
    </div>
  );
};

export default SingleChatItem;