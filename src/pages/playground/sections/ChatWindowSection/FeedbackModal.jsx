import {
  Button,
  Divider,
  Form,
  Input,
  Modal,
  Select,
  Typography,
  message,
} from "antd";
import React, { useState } from "react";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { handleFeedbackApi } from "../../../../services/feedbackLog.service";

const { Text, Title, Paragraph } = Typography;
const { TextArea } = Input;

const FeedbackModal = ({
  message: msng,
  feedbackModalVisible,
  setFeedbackModalVisible,
  liked,
  setLiked,
  setDisliked,
}) => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();

  // States
  const [loading, setLoading] = useState(false);

  // Handle feedback submission
  const handleFeedbackSubmit = (values) => {
    setLoading(true);
    console.log("msng -> ", msng);
    console.log("Feedback submitted:", values);

    dispatch(
      handleFeedbackApi({
        data: {
          message_id: msng?.message_id,
          evaluation: "dislike",
          remark: values?.remark,
          categories: values?.categories,
          reviewer_id: localStorage.getItem("userId"),
          created_at: null,
        },
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          // Otherwise, set disliked and show modal
          setDisliked(true);
          if (liked) setLiked(false);

          setFeedbackModalVisible(false);
          message.success("Disliked!");
        },
        failureCallback: () => {},
      })
    );
  };

  return (
    <div>
      {/* Feedback Modal */}
      <Modal
        title="Please provide feedback"
        open={feedbackModalVisible}
        onCancel={() => setFeedbackModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setFeedbackModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => form.submit()}
            loading={loading}
          >
            Submit
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFeedbackSubmit}
          initialValues={{ categories: ["Factually incorrect"] }}
        >
          <Form.Item
            name="categories"
            label="What went wrong with this response?"
          >
            <Select
              placeholder="Select a category"
              style={{ width: "100%" }}
              mode="multiple"
              options={[
                { value: "Factually incorrect", label: "Factually incorrect" },
                { value: "Unhelpful", label: "Unhelpful" },
                { value: "Off-topic", label: "Off-topic" },
                { value: "Others", label: "Others" },
              ]}
            />
          </Form.Item>
          <Divider />
          <Form.Item name="remark" label="Additional details (optional)">
            <TextArea
              rows={4}
              placeholder="Please provide any specific details about the issue"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FeedbackModal;
