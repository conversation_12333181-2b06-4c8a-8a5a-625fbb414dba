import { Image } from "antd";
import { useState, useEffect, useRef } from "react";
import { PictureOutlined } from "@ant-design/icons";

const RenderImageCollage = ({ images = [] }) => {
  const [loadErrors, setLoadErrors] = useState({});
  const [validImages, setValidImages] = useState([]);
  const [visible, setVisible] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const stylesInjected = useRef(false);

  // Filter images that have errors
  useEffect(() => {
    const filteredImages = images.filter((_, index) => !loadErrors[index]);
    setValidImages(filteredImages);
  }, [loadErrors, images]);

  // Inject styles only once when component mounts
  useEffect(() => {
    if (
      !stylesInjected.current &&
      !document.getElementById("preview-arrow-styles")
    ) {
      const styleEl = document.createElement("style");
      styleEl.id = "preview-arrow-styles";
      styleEl.innerHTML = `
      .ant-image-preview-switch-left {
        left: 40px !important;
        color: black;
        background-color: rgba(255, 255, 255, 0.69);
      }
      .ant-image-preview-switch-right {
        right: 40px !important;
        color: black;
        background-color: rgba(255, 255, 255, 0.69);
      }
      .ant-image-preview-switch-left:hover,
      .ant-image-preview-switch-right:hover {
        background-color: rgba(255, 255, 255, 0.69) !important;
        color: black !important;
        cursor: pointer;
      }
    `;
      document.head.appendChild(styleEl);
      stylesInjected.current = true;
    }

    // Clean up on unmount
    return () => {
      const styleEl = document.getElementById("preview-arrow-styles");
      if (styleEl) {
        styleEl.remove();
      }
    };
  }, []);

  if (validImages.length === 0) return null;

  const handleImageError = (index) => {
    setLoadErrors((prev) => ({
      ...prev,
      [index]: true,
    }));
  };

  // If 3 or fewer images, show all
  const showAllImages =
    validImages.length <= 3 ? validImages : validImages.slice(0, 2);
  const remainingCount = validImages.length > 3 ? validImages.length - 2 : 0;

  // Show all images in preview mode
  const handleViewAll = () => {
    setCurrentIndex(2);
    setVisible(true);
  };

  // Handle clicking on a preview image
  const handleImageClick = (index) => {
    setCurrentIndex(index);
    setVisible(true);
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          gap: "8px",
          // maxWidth: "500px",
        }}
      >
        {/* Show images */}
        {showAllImages.map((image, index) => (
          <div
            key={index}
            onClick={() => handleImageClick(index)}
            style={{
              cursor: "pointer",
              width: "100px",
              height: "100px",
              borderRadius: "8px",
              overflow: "hidden",
              backgroundColor: "#4a4a4a",
              border: "1px solid grey",
            }}
          >
            <Image
              preview={false}
              src={image.url || image}
              alt={`Image ${index + 1}`}
              style={{
                objectFit: "cover",
                width: "100px",
                height: "100px",
              }}
              onError={() => handleImageError(index)}
            />
          </div>
        ))}

        {/* Show "+X more" if more than 3 images */}
        {remainingCount > 0 && (
          <div
            onClick={handleViewAll}
            style={{
              cursor: "pointer",
              width: "100px",
              height: "100px",
              borderRadius: "8px",
              backgroundColor: "#4a4a4a",
              border: "1px solid grey",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              color: "white",
            }}
          >
            <PictureOutlined style={{ fontSize: "24px" }} />
            <div style={{ marginTop: "8px" }}>+{remainingCount} more</div>
          </div>
        )}
      </div>

      {/* Image preview group for the slideshow */}
      <Image.PreviewGroup
        preview={{
          visible,
          onVisibleChange: setVisible,
          current: currentIndex,
          onChange: (current) => setCurrentIndex(current),
        }}
      >
        {validImages.map((image, index) => (
          <Image
            key={index}
            src={image.url || image}
            alt={`Image ${index + 1}`}
            style={{ display: "none" }}
            onError={() => handleImageError(index)}
          />
        ))}
      </Image.PreviewGroup>
    </div>
  );
};

export default RenderImageCollage;
