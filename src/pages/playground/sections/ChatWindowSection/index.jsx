import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Typography,
  Avatar,
  Space,
  Input,
  Button,
  Upload,
  Menu,
  Dropdown,
  message,
  Divider,
  Row,
  Col,
  Spin,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  SendOutlined,
  PaperClipOutlined,
  DeleteOutlined,
  MoreOutlined,
  CloseOutlined,
  DeleteTwoTone,
  PlusCircleOutlined,
  SmileFilled,
  FileImageFilled,
  PictureOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { styles } from "../../styles";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import {
  generateReplyApi,
  fetchChatHistory,
  chatDeleteAPI,
} from "../../../../services/playground.service";
import SelectOption from "../../../../components/common/selectOption";
import SingleChatItem from "./SingleChatItem";
import { imageUploadApi } from "../../../../services/knowledgebase.service";
import "./index.css";
import dayjs from "dayjs"; // Import dayjs for date handling
import { useNavigate } from "react-router-dom";
import { logout } from "../../../../store/slices/profile.slice";
import EmptyChat from "./EmptyChat";

const { Text, Title, Paragraph } = Typography;
const { TextArea } = Input;

// Date Separator Component
const DateSeparator = ({ date }) => {
  // Format the date appropriately
  const formatDate = (dateString) => {
    const today = dayjs().startOf("day");
    const yesterday = today.subtract(1, "day");
    const messageDate = dayjs(dateString).startOf("day");

    if (messageDate.isSame(today)) {
      return "Today";
    } else if (messageDate.isSame(yesterday)) {
      return "Yesterday";
    } else {
      // For older dates, format as "Jan 1, 2025"
      return messageDate.format("MMM D, YYYY");
    }
  };

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "16px 0",
        width: "100%",
      }}
    >
      <div
        style={{
          backgroundColor: "#f0f0f0",
          borderRadius: "16px",
          padding: "4px 12px",
          display: "inline-block",
        }}
      >
        <Text style={{ fontSize: "12px", color: "#666" }}>
          {formatDate(date)}
        </Text>
      </div>
    </div>
  );
};

const ChatWindowSection = ({
  responseData,
  setResponseData,
  setVisible,
  setSpinning,
  setDrawerData,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  // Loading state
  const [loading, setLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [userInput, setUserInput] = useState("");
  const [fileList, setFileList] = useState([]);
  const [fileURL, setFileURL] = useState([]);
  const [imagePreview, setImagePreview] = useState([]);

  // Response page number metaData from response
  const [conversationsMeta, setConversationsMeta] = useState();

  // Reference to chat container for scrolling
  const chatContainerRef = useRef(null);

  // New states for infinite scrolling
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const lastScrollPositionRef = useRef(0);

  // New state to track if user has intentionally scrolled
  const [hasScrolled, setHasScrolled] = useState(false);

  useEffect(() => {
    setSpinning(true);
    fetchChatHis();
  }, []);

  // Scroll to bottom whenever conversations change, but only if we're not loading more at the top
  useEffect(() => {
    if (shouldScrollToBottom) {
      scrollToBottom();
    }
  }, [conversations, shouldScrollToBottom]);

  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      const container = chatContainerRef.current;
      if (!container) return;

      // Store the current scroll position
      const currentScrollTop = container.scrollTop;

      // Check if user is scrolling up (away from the bottom)
      if (
        currentScrollTop <
        container.scrollHeight - container.clientHeight - 50
      ) {
        setHasScrolled(true);
      } else {
        // If user is at the bottom of the chat, they're not trying to view older messages
        setHasScrolled(false);
      }

      lastScrollPositionRef.current = currentScrollTop;

      // Check if user has scrolled to the top (with a small threshold)
      if (currentScrollTop <= 30 && !isLoadingMore && hasMore) {
        loadMoreConversations();
      }
    };

    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, [isLoadingMore, hasMore, page]);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  const loadMoreConversations = () => {
    if (isLoadingMore || !hasMore) return;

    // Disable auto-scroll when loading more
    setShouldScrollToBottom(false);
    setIsLoadingMore(true);

    // Save the current scroll height to restore position later
    const scrollHeight = chatContainerRef.current?.scrollHeight || 0;

    dispatch(
      fetchChatHistory({
        params: {
          user_id: localStorage.getItem("userId"),
          page_number: page + 1,
        },
        finalCallback: () => {
          setIsLoadingMore(false);

          // After new content is loaded, maintain the scroll position
          setTimeout(() => {
            if (chatContainerRef.current) {
              const newScrollHeight = chatContainerRef.current.scrollHeight;
              const heightDifference = newScrollHeight - scrollHeight;
              chatContainerRef.current.scrollTop =
                heightDifference > 0 ? heightDifference : 0;
            }
          }, 50);
        },
        successCallback: (response) => {
          if (!response.responseData || response.responseData.length === 0) {
            setHasMore(false);
            return;
          }

          setConversations((prevConversations) => {
            return [...response.responseData, ...prevConversations];
          });

          setPage((prevPage) => prevPage + 1);
        },
        failureCallback: (error) => {
          console.error("Error loading more conversations:", error);
          setHasMore(false);
        },
      })
    );
  };

  const fetchChatHis = () => {
    // Reset pagination data when fetching initial history
    setPage(1);
    setHasMore(true);
    setShouldScrollToBottom(true);
    setHasScrolled(false); // Reset scrolled state when fetching chat history

    dispatch(
      fetchChatHistory({
        params: {
          user_id: localStorage.getItem("userId"),
          page_number: 1,
        },
        finalCallback: () => {
          setSpinning(false);
          setLoading(false);
          // Ensure scroll to bottom after loading conversations
          setTimeout(scrollToBottom, 100);
        },
        successCallback: (response) => {
          setConversations(response?.responseData || []);
          setConversationsMeta(response?.responseMeta);

          // Check if there might be more data to load
          if (!response?.responseData || response.responseData.length === 0) {
            setHasMore(false);
          }
        },
        failureCallback: (error) => {
          console.log("Error:", error);
          setHasMore(false);
        },
      })
    );
  };

  const sendMessage = (urls) => {
    // When sending a message, we always want to scroll to bottom
    setShouldScrollToBottom(true);
    setHasScrolled(false); // Reset scroll state when sending a new message

    const data = {
      chat_data: [
        {
          data: {
            content: userInput?.trim(),
            media_ids: urls?.length > 0 ? urls : [],
          },
          role: "user",
        },
      ],
      chat_data_format: "role_data",
      media_ids: ["string"],
      media_values: "string",
      message: "string",
      primary_product: "string",
      primary_product_code: "string",
      tags: ["Facebook"],
      user_id: localStorage.getItem("userId"),
      user_name: localStorage.getItem("username"),
      mode: "elaborated",
    };

    // Create new user message
    const newUserMessage = {
      role: "user",
      content: userInput,
      created_at: new Date().toISOString(),
      media_ids: urls,
    };

    // Update conversations
    setConversations((prev) => {
      const updatedConversations = [...prev];
      if (updatedConversations?.length > 0) {
        updatedConversations[updatedConversations?.length - 1].push(
          newUserMessage
        );
      } else {
        updatedConversations.push([newUserMessage]);
      }
      return updatedConversations;
    });

    // Reset input and image preview
    setUserInput("");
    setIsTyping(true);
    setImagePreview([]);

    // Dispatch AI response
    dispatch(
      generateReplyApi({
        data: data,
        finalCallback: () => {
          setLoading(false);
          setIsTyping(false);
          // Consider removing this to prevent double-fetching
          fetchChatHis();
        },
        successCallback: (response) => {
          if (response?.response) {
            setResponseData(response?.response);
            console.log("AI Response:", response?.response);

            // Create AI response
            const aiResponse = {
              role: "assistant",
              content: response?.response?.reply,
              created_at: new Date().toISOString(),
              processingTime: response?.response?.processing_time,
            };

            setConversations((prev) => {
              const updated = [...prev];
              if (updated?.length > 0) {
                updated[updated?.length - 1].push(aiResponse);
              } else {
                updated.push([aiResponse]);
              }
              return updated;
            });
          }
        },
        failureCallback: (error) => {
          setIsTyping(false);
          setLoading(false);
        },
      })
    );
  };

  const handleSendMessage = async () => {
    if (!userInput?.trim() && (!fileList || fileList?.length === 0)) return;

    console.log("imagePreview", fileList);
    setLoading(true);
    console.log("User Input -> ", userInput?.trim());

    if (fileList && fileList?.length > 0) {
      const formData = new FormData();
      fileList.forEach((file) => {
        formData.append("files", file.originFileObj);
      });

      dispatch(
        imageUploadApi({
          data: formData,
          params: { source: "test" },
          // finalCallback: () => sendMessage([]),
          finalCallback: () => {},
          successCallback: (response) => {
            const urls = response?.data?.map((item) => item?.url ?? []);
            setFileURL(urls);
            sendMessage(urls); // Now pass the uploaded image URLs
            setFileList([]);
          },
          failureCallback: (error) => {
            console.error("Image Upload Error", error);
            setLoading(false);
          },
        })
      );
    } else {
      sendMessage([]);
    }
  };

  // Handle file change
  const handleFileChange = ({ fileList }) => {
    const file = fileList[fileList?.length - 1]; // Get last selected file

    if (file && file.originFileObj) {
      const validExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      const fileExtension = file.name.split(".").pop().toLowerCase();

      if (validExtensions.includes(fileExtension)) {
        console.log("fileList", fileList);
        setFileList(fileList);
        const newPreviews = [];

        fileList.forEach((file) => {
          if (file.originFileObj) {
            const reader = new FileReader();
            reader.onload = (event) => {
              newPreviews.push(event.target.result);

              // Only update state once after all files are processed
              if (newPreviews?.length === fileList?.length) {
                setImagePreview(newPreviews);
              }
            };
            reader.readAsDataURL(file.originFileObj);
          }
        });
      } else {
        message.error(
          "Only image files (jpg, jpeg, png, gif, bmp, webp) are allowed."
        );
        setFileList(
          fileList.filter((file) =>
            validExtensions.includes(file.name.split(".").pop().toLowerCase())
          )
        );
      }
    } else {
      message.error("File upload failed. Please try again.");
    }
  };

  // Remove an image preview
  const removeImagePreview = (index) => {
    setFileList((prevFileList) =>
      prevFileList.filter((_, idx) => idx !== index)
    );
    setImagePreview((prevState) => prevState.filter((_, idx) => idx !== index));
  };

  const handleDeleteChatHistory = () => {
    setHasScrolled(false); // Reset scrolled state when clearing chat history

    dispatch(
      chatDeleteAPI({
        params: {
          user_id: localStorage.getItem("userId"),
        },
        finalCallback: () => {},
        successCallback: (response) => {
          message.success("Chat deleted successfully!");
          fetchChatHis();
        },
        failureCallback: (error) => {
          message.error(error.message);
        },
      })
    );
  };

  // AI Typing indicator component
  const TypingIndicator = () => (
    <div style={{ marginBottom: "16px" }}>
      <Space align="start">
        <Avatar
          icon={<RobotOutlined />}
          style={{ backgroundColor: "#1890ff" }}
        />

        <div className="message-loading">
          <div className="bouncing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </Space>
    </div>
  );

  // Render messages with date separators
  const renderMessagesWithDateSeparators = (conversations) => {
    if (!conversations || conversations.length === 0) return [];

    const result = [];
    let currentDate = null;

    conversations.flatMap((conversation, convIndex) => {
      const sortedMessages = [...conversation].sort((a, b) => {
        const timeA = new Date(a.created_at || 0).getTime();
        const timeB = new Date(b.created_at || 0).getTime();
        return timeA - timeB;
      });

      sortedMessages.forEach((message, msgIndex) => {
        // Skip messages without created_at timestamp
        if (!message.created_at) {
          result.push(
            <SingleChatItem
              key={`msg-no-date-${msgIndex}-${convIndex}`}
              message={message}
              convIndex={convIndex}
              msgIndex={msgIndex}
              setVisible={setVisible}
              setResponseData={setResponseData}
              setDrawerData={setDrawerData}
            />
          );
          return;
        }

        // Get message date without time (start of day)
        const messageDate = dayjs(message.created_at).startOf("day");

        // If this is a new date, add a separator
        if (!currentDate || !messageDate.isSame(currentDate)) {
          currentDate = messageDate;
          result.push(
            <DateSeparator
              key={`date-${message.created_at}-${convIndex}-${msgIndex}`}
              date={message.created_at}
            />
          );
        }

        // Add the message
        result.push(
          <SingleChatItem
            key={`msg-${message.created_at}-${msgIndex}-${convIndex}`}
            message={message}
            convIndex={convIndex}
            msgIndex={msgIndex}
            setVisible={setVisible}
            setResponseData={setResponseData}
            setDrawerData={setDrawerData}
          />
        );
      });
    });

    return result;
  };

  return (
    <div style={{ height: "100%", width: "100%" }}>
      <Card
        bodyStyle={{ padding: 0, height: "100%" }}
        style={{
          minHeight: "91vh",
          display: "flex",
          flexDirection: "column",
          width: "100%",
        }}
        title={
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <div>
              <Title level={4} style={{ margin: 0, fontWeight: "600" }}>
                Chats
              </Title>
            </div>
            <div>
              <Button
                icon={<DeleteOutlined style={{ fontSize: "18px" }} />}
                onClick={handleDeleteChatHistory}
                color="danger"
                variant="text"
                disabled={!conversations?.length > 0}
              >
                Clear Chats
              </Button>
            </div>
          </div>
        }
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            height: "100%",
          }}
        >
          <div
            ref={chatContainerRef}
            style={{
              height: "calc(100vh - 250px)",
              maxHeight: "100vh",
              overflowY: "auto",
              width: "95%",
              padding: "16px",
              display: "flex",
              flexDirection: "column",
              paddingBottom: "20px",
              position: "relative",
            }}
          >
            {/* Scroll-to-bottom button overlay */}
            {hasScrolled && (
              <Button
                type="primary"
                shape="circle"
                icon={<span style={{ fontSize: 18 }}>↓</span>}
                onClick={scrollToBottom}
                aria-label="Scroll to bottom"
                style={{
                  position: "fixed",
                  left: "53%",
                  transform: "translateX(-50%)",
                  bottom: "120px", // Adjust this value as needed

                  zIndex: 1000,
                  paddingBottom: 10,
                }}
                className="back-to-top-button"
              />
            )}
            {/* Show empty state when there are no conversations */}
            {!loading && conversations?.length === 0 ? (
              <EmptyChat />
            ) : (
              <>
                {/* Loading indicator at the top when fetching more conversations */}
                {hasMore && (
                  <div
                    style={{
                      textAlign: "center",
                      height: "30px",
                      marginBottom: "10px",
                    }}
                  >
                    <Spin
                      indicator={<LoadingOutlined spin />}
                      spinning={isLoadingMore}
                    />
                  </div>
                )}

                {/* "Conversation ends here" only shows if:
                    1. There are no more messages to load (hasMore is false)
                    2. There are actual conversations to display
                    3. User has scrolled up (hasScrolled is true)
                    4. User is near the top (lastScrollPositionRef.current <= 100) */}
                {!hasMore &&
                  conversations.length > 0 &&
                  hasScrolled &&
                  lastScrollPositionRef.current <= 100 && (
                    <div
                      style={{
                        textAlign: "center",
                        padding: "12px",
                        color: "#888",
                        fontSize: "13px",
                        borderRadius: "8px",
                        margin: "10px 0 20px 0",
                        boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: "8px",
                      }}
                    >
                      <div
                        style={{
                          height: "1px",
                          backgroundColor: "#ddd",
                          width: "40px",
                        }}
                      />
                      <span>Conversation ends here</span>
                      <div
                        style={{
                          height: "1px",
                          backgroundColor: "#ddd",
                          width: "40px",
                        }}
                      />
                    </div>
                  )}

                {/* Render messages with date separators */}
                {renderMessagesWithDateSeparators(conversations)}

                {/* Show typing indicator when AI is responding */}
                {isTyping && <TypingIndicator />}
              </>
            )}
          </div>

          <div
            style={{
              position: "fixed",
              bottom: "30px",
              zIndex: 999,
              width: "100%",
              display: "flex",
              justifyContent: "center",
              padding: "10px",
            }}
          >
            <div style={{ width: "100%", maxWidth: "80vw" }}>
              {/* Image Preview Section */}
              {imagePreview?.length > 0 && (
                <div
                  style={{
                    width: "100%",
                    display: "flex",
                    flexWrap: "wrap",
                    justifyContent: "left",
                    backgroundColor: "#f9f9f9",
                  }}
                >
                  {imagePreview?.map((image, index) => (
                    <div key={index} style={{ position: "relative" }}>
                      <img
                        src={image}
                        alt={`Preview ${index}`}
                        style={{
                          width: "80px",
                          height: "80px",
                          borderRadius: "8px",
                          border: "solid 1px #E5E7EB",
                        }}
                      />
                      <Button
                        type="text"
                        icon={<DeleteOutlined style={{ color: "red" }} />}
                        onClick={() => removeImagePreview(index)}
                        style={{
                          position: "absolute",
                          top: "-10px",
                          right: "-10px",
                          zIndex: 1,
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Chat Input Section (Full Width) */}
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  padding: "12px",
                  backgroundColor: "white",
                  borderRadius: "12px",
                  border: "1px solid rgba(102, 101, 101, 0.24)",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                }}
              >
                {/* Upload Button */}
                <Upload
                  fileList={fileList}
                  onChange={handleFileChange}
                  beforeUpload={() => false}
                  showUploadList={false}
                  multiple
                >
                  <Button icon={<PlusCircleOutlined />} type="text" />
                </Upload>

                {/* Message Input (Expands Fully) */}
                <TextArea
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  placeholder="Type your message..."
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  onPressEnter={(e) => {
                    if (!e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  disabled={loading}
                  style={{
                    flex: 1,
                    border: "none",
                    outline: "none",
                    background: "transparent",
                    width: "100%", // Ensures full width usage
                  }}
                />

                {/* Send Button */}
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSendMessage}
                  loading={loading}
                  disabled={!(userInput?.trim() || imagePreview?.length > 0)}
                >
                  Send
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChatWindowSection;
