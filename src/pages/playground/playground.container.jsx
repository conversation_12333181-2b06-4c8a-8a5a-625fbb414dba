import { Spin } from "antd";
import React, { useState } from "react";
import PlaygoundComponent from "./playgound.component";

const PlaygroundContainer = () => {
  const [spinning, setSpinning] = useState(false);
  const [responseData, setResponseData] = useState();
  const [drawerData, setDrawerData] = useState();
  return (
    <div>
      <Spin spinning={spinning}>
        <PlaygoundComponent
          responseData={responseData}
          setResponseData={setResponseData}
          spinning={spinning}
          setSpinning={setSpinning}
          drawerData={drawerData}
          setDrawerData={setDrawerData}
        />
      </Spin>
    </div>
  );
};

export default PlaygroundContainer;
