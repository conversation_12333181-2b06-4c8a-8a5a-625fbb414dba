import { <PERSON><PERSON>, <PERSON>, Spin, Typography } from "antd";
import React, { useEffect, useState } from "react";
import {
  ArrowLeftOutlined,
  FolderOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import { getAnswerByIdApi } from "../../../services/resolveConflict.service";
import SourceNodesSection from "./SourceNodesSection";

const QuestionAnswerSection = ({
  questionId,
  question,
  sources,
  setSources,
  answer,
  setAnswer,
}) => {
  console.log("Sources -> ", sources);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { Title, Paragraph } = Typography;

  const [expanded, setExpanded] = useState(false);
  const [isEllipsis, setIsEllipsis] = useState(false);

  const fetchAnswerById = () => {
    dispatch(
      getAnswerByIdApi({
        id: questionId,
        finalCallback: () => {},
        successCallback: (response) => {
          setAnswer(response?.answer);
          setSources(response?.source_nodes);
        },
        failureCallback: () => {},
      })
    );
  };

  useEffect(() => {
    if (questionId) {
      fetchAnswerById();
    } else {
      setAnswer();
      setSources([]);
    }
  }, [questionId]);

  const handleToggleExpand = () => {
    setExpanded(!expanded); // Toggle the expanded state
  };
  const handleEllipsisChange = (ellipsis) => {
    setIsEllipsis(ellipsis); // Set whether the text is truncated or not
  };

  return (
    <Card
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div>Question's Answer</div>
          <div>
            <Button
              type="default"
              onClick={() => navigate("/knowledge-base")}
              icon={<ArrowLeftOutlined />}
            >
              Knowlege base
            </Button>
          </div>
        </div>
      }
      style={{
        height: "90vh",
        overflow: "auto",
      }}
      styles={{
        body: {
          paddingTop: "10px",
          paddingLeft: "18px",
          paddingBottom: "18px",
          paddingRight: "18px",
        },
      }}
    >
      {/* Question Title */}
      <Title
        level={4}
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        {question}
      </Title>

      <div>
        <Paragraph
          ellipsis={{
            rows: 3,
            expanded,
            onEllipsis: handleEllipsisChange,
          }}
          style={{ textAlign: "justify" }}
        >
          {answer}
        </Paragraph>
        {isEllipsis && (
          <Button type="dashed" onClick={handleToggleExpand} size="small">
            {expanded ? "Collapse" : "Show More"}
          </Button>
        )}
      </div>
      <div>
        <SourceNodesSection
          nodes={sources}
          questionId={questionId}
          fetchAnswerById={fetchAnswerById}
        />
      </div>
    </Card>
  );
};

export default QuestionAnswerSection;
