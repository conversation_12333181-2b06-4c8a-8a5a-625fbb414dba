export const connectWebSocket = (onStatusUpdate) => {
  const socket = new WebSocket(
    `${process.env.VITE_WS_BASE_URL}/setup_files?token=${localStorage.getItem(
      "authToken"
    )}`
  );

  socket.onopen = () => {
    console.log("WebSocket connected");
  };

  socket.onmessage = (event) => {
    try {
      const status = JSON.parse(event.data);
      onStatusUpdate(status);
      console.log("Received WebSocket message:", status);
    } catch (error) {
      console.error("Error parsing WebSocket message:", error);
    }
  };

  socket.onerror = (error) => {
    console.error("WebSocket connection error:", error);
  };

  socket.onclose = () => {
    console.log("WebSocket connection closed");
  };

  return socket;
};
