import React, { useState } from "react";
import { Card, Typography, Space, Modal, Form, Input, List } from "antd";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";

const { TextArea } = Input;

const QAList = ({ responseData, setResponseData }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [form] = Form.useForm();

  const showModal = (index) => {
    setEditingIndex(index);
    form.setFieldsValue(responseData[index]);
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const newData = [...responseData];
      newData[editingIndex] = values;
      setResponseData(newData);
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <>
      <List
        style={{
          height: "800px",
          overflowY: "auto",
          padding: "0 16px",
        }}
        itemLayout="vertical"
        dataSource={responseData}
        renderItem={(item, index) => (
          <List.Item
            key={index}
            onClick={() => showModal(index)}
            style={{ cursor: "pointer" }}
          >
            <Card
              style={{
                width: "100%",
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                transition: "all 0.3s ease",
                "&:hover": {
                  boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                },
                position: "relative",
              }}
            >
              <EditOutlined
                style={{
                  position: "absolute",
                  right: 16,
                  top: 16,
                  color: "#999",
                }}
              />
              <Space direction="vertical" style={{ width: "100%" }}>
                <div>
                  <Typography.Text strong style={{ marginRight: 8 }}>
                    Q{index + 1}.
                  </Typography.Text>
                  <Typography.Text style={{ fontSize: 16 }}>
                    {item.question}
                  </Typography.Text>
                </div>
                <div>
                  <Typography.Text strong style={{ marginRight: 8 }}>
                    A:
                  </Typography.Text>
                  <Typography.Text style={{ color: "#666", fontSize: 14 }}>
                    {item.answer}
                  </Typography.Text>
                </div>
              </Space>
            </Card>
          </List.Item>
        )}
      />

      <Modal
        title="Edit Q&A"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
        okText="Save"
        cancelText="Cancel"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ question: "", answer: "" }}
        >
          <Form.Item
            name="question"
            label="Question"
            rules={[{ required: true, message: "Please input the question!" }]}
          >
            <TextArea
              rows={3}
              placeholder="Enter question"
              style={{ borderRadius: 6 }}
            />
          </Form.Item>

          <Form.Item
            name="answer"
            label="Answer"
            rules={[{ required: true, message: "Please input the answer!" }]}
          >
            <TextArea
              rows={4}
              placeholder="Enter answer"
              style={{ borderRadius: 6 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default QAList;
