import { Button, Card, Form, Input, message } from "antd";
import React, { useEffect, useState } from "react";
import SelectOption from "../../../../components/common/selectOption";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import {
  businessSetupApi,
  getBusinessSetupApi,
} from "../../../../services/setup.service";
import { logout } from "../../../../store/slices/profile.slice";
import { useNavigate } from "react-router-dom";

const ProductSection = ({ businessTypeOptionList }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  //   useEffect(() => {
  //       It will set the values of input fields just like initial values
  //     const fetchedData = {
  //       business_type: "option_2",
  //       business_description: "Fetched business description",
  //     };
  //     form.setFieldsValue(fetchedData);
  //   }, []);

  const onFinish = (values) => {
    // Handle form submission here

    dispatch(
      businessSetupApi({
        data: values,
        finalCallback: () => {},
        successCallback: (response) => {
          message.success(response?.message);

          // Finally this will empty the input fields
          // form.resetFields();
        },
        failureCallback: () => {},
      })
    );
  };

  const fetchBusinessSetup = () => {
    dispatch(
      getBusinessSetupApi({
        finalCallback: () => {},
        successCallback: (response) => {
          form.setFieldsValue(response);
        },
        failureCallback: (errors) => {
          console.log("Error in getCustomersDataApi -> ", errors);
        },
      })
    );
  };

  useEffect(() => {
    fetchBusinessSetup();
  }, []);

  return (
    <div>
      <Card
        style={{
          marginBottom: "48px",
        }}
        title={
          <p style={{ fontSize: "18px", margin: 0 }}>Business Information</p>
        }
      >
        <Form layout="vertical" form={form} onFinish={onFinish} size="large">
          <Form.Item
            name="business_type"
            label="Business Type"
            rules={[
              { required: true, message: "Please enter your business type!" },
            ]}
          >
            <SelectOption
              options={businessTypeOptionList}
              placeholder="Select Business Type"
            />
          </Form.Item>

          <Form.Item
            name="business_desc"
            label="Business Description"
            rules={[
              {
                required: true,
                message: "Please enter business description!",
              },
              // {
              //   max: 500,
              //   message: "Description cannot exceed 500 characters!",
              // },
            ]}
          >
            <Input.TextArea
              size="large"
              placeholder="Enter your business description"
              autoSize={{ minRows: 3, maxRows: 5 }}
              // showCount
              // maxLength={500}
            />
          </Form.Item>

          <Form.Item style={{ marginTop: 40, textAlign: "center" }}>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              style={{
                minWidth: 180,
                height: 35,
                borderRadius: 6,
                fontSize: 16,
              }}
            >
              Save
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ProductSection;
