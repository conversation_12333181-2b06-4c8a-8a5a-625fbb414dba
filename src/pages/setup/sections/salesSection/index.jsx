import React, { useEffect, useState } from "react";
import FileUpload from "../../../../components/common/FileUpload";
import ProductFilterForm from "./productFilterForm";
import DataTable from "../../../../components/common/dataTable";
import { Typography } from "antd";

const SalesSection = () => {
  const { Title } = Typography;
  // File upload response
  const [fileUploadResponse, setFileUploadResponse] = useState();
  const [salesTableData, setSalesTableData] = useState();

  return (
    <div>
      {!fileUploadResponse && (
        <Title
          strong
          style={{ textAlign: "center", fontSize: "18px", color: "#BBBBBB" }}
        >
          Upload sales document
        </Title>
      )}

      <FileUpload
        url="/sales_pre_setup/"
        setResponseData={setFileUploadResponse}
      />
      {fileUploadResponse && (
        <ProductFilterForm
          key={JSON.stringify(fileUploadResponse?.content)}
          fileUploadResponse={fileUploadResponse}
          setSalesTableData={setSalesTableData}
        />
      )}
      {salesTableData && (
        <DataTable
          data={salesTableData?.rows}
          columns={salesTableData?.column_names}
        />
      )}
    </div>
  );
};

export default SalesSection;
