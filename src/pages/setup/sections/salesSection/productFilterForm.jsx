import React, { useEffect, useState } from "react";
import { Form, Select, But<PERSON>, Card } from "antd";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { salesSetupApi } from "../../../../services/setup.service";

const ProductFilterForm = ({ fileUploadResponse, setSalesTableData }) => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const categoryList = fileUploadResponse?.category_list.map((item) => {
    return { label: item, value: item };
  });

  const productList = fileUploadResponse?.product_list.map((item) => {
    return { label: item, value: item };
  });

  const onFinish = (values) => {
    setLoading(true);
    const finalObj = {
      ...values,
      text: fileUploadResponse?.content,
    };

    dispatch(
      salesSetupApi({
        data: finalObj,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          setSalesTableData(response);
        },
        failureCallback: () => {},
      })
    );
  };

  return (
    <Card
      style={{
        maxWidth: 800,
        margin: "0 auto",
        padding: 20,
        marginBottom: "48px",
      }}
    >
      <Form form={form} onFinish={onFinish} layout="vertical">
        <Form.Item
          name="category"
          label="Select Categories"
          rules={[{ required: true, message: "Please select an option!" }]}
          initialValue={categoryList.map((category) => category.value)}
        >
          <Select
            mode="tags"
            placeholder="Select categories"
            options={categoryList}
            style={{ width: "100%" }}
            tokenSeparators={[","]}
            allowClear
          />
        </Form.Item>

        <Form.Item
          name="products"
          label="Select products"
          rules={[{ required: true, message: "Please select an option!" }]}
          initialValue={productList.map((product) => product.value)}
        >
          <Select
            mode="tags"
            placeholder="Select products"
            options={productList}
            style={{ width: "100%" }}
            tokenSeparators={[","]}
            allowClear
          />
        </Form.Item>

        <Form.Item style={{ textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            style={{
              minWidth: 180,
              height: 35,
              borderRadius: 6,
              fontSize: 16,
            }}
          >
            {loading ? "Generating Table" : "Generate Table"}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ProductFilterForm;
