import httpBase from "../../../utils/http.utils";
export const fetch_docs = async (offset = null, filter = null, limit = 3) => {
  const payload = {
    collection_name: "test_page_info",
    limit: limit,
    filter: [{}], // Adjust filter as needed
  };

  // Conditionally add offset if it's not null
  if (offset !== null) {
    payload.offset = offset;
  }
  if (filter !== null) {
    payload.filter = [filter];
  }

  try {
    const response = await httpBase().post("/qdrant/search_all", payload, {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("authToken")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching source:", error);
    return null;
  }
};

export const fetch_toc = async () => {
  try {
    const response = await httpBase().get("/qdrant/get_toc", {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("authToken")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching source:", error);
    return null;
  }
};
export const RetreiveChat = async (query, level, metadata) => {
  try {
    const data = {
      query: query,
      level: level,
      metadata: null,
    };
    const response = await httpBase().post("/qdrant/search", data, {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("authToken")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching source:", error);
    return null;
  }
};
