import { useState, useMemo, useEffect } from "react";
import { Drawer, Input, Tree, Button } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import "./TOCDrawer.css";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { getAllSourcesApi } from "../../../../services/knowledgebase.service";

function TOCDrawer({
  visible,
  onClose,
  toc,
  setSourceFilter,
  setDefaultSources,
  setSubSources,
  setTocNode,
  fetchSources,
  setFiltersTOC,
}) {
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  // Reset selections and filter when search term or TOC data changes
  useEffect(() => {
    setSelectedKeys([]);
    setSourceFilter(null);
  }, [searchTerm, toc]);

  const treeData = useMemo(() => {
    if (!toc) return [];

    const filterToc = (text) =>
      text.toLowerCase().includes(searchTerm.toLowerCase());

    return toc
      .map((section) => {
        if (section.section_name === "Unidentified Documents") return null;

        const filteredDocs = section.doc_info.filter(
          (doc) => doc.name !== "Unknown Document" && filterToc(doc.name)
        );

        const hasMatch =
          searchTerm === "" ||
          filterToc(section.section_name) ||
          filteredDocs.length > 0;

        if (!hasMatch) return null;

        return {
          title: section.section_name,
          key: `section-${section.section_name}`,
          selectable: true,
          className: "tree-section",
          children: filteredDocs.map((doc) => ({
            title: doc.name,
            key: `doc-${section.section_name}-${doc.name}`,
            selectable: true,
            className: "tree-document",
          })),
        };
      })
      .filter(Boolean);
  }, [toc, searchTerm]);

  const handleSelect = (selectedKeys, { node, selected }) => {
    if (selected) {
      setSelectedKeys(selectedKeys);
      const filter = node.children
        ? { section_title: node.title }
        : { title: node.title };
      setFiltersTOC(filter);
      // setSourceFilter(filter);
      setTocNode(filter);
      fetchSources(filter);
    } else {
      // Clear selection and filter
      fetchSources(null);
      setSelectedKeys([]);
    }
  };

  return (
    <Drawer
      title="Table of Contents"
      placement="right"
      onClose={onClose}
      visible={visible}
      width={350}
      bodyStyle={{ padding: "20px" }}
    >
      <Input
        placeholder="Search TOC..."
        prefix={<SearchOutlined />}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        style={{ marginBottom: "10px" }}
      />
      <Tree
        treeData={treeData}
        onSelect={handleSelect}
        onExpand={(keys) => setExpandedKeys(keys)}
        expandedKeys={expandedKeys}
        selectedKeys={selectedKeys}
        className="custom-tree"
        defaultExpandAll={!searchTerm} // Auto-expand only when not searching
        style={{
          marginBottom: "20px",
          maxHeight: "calc(100vh - 200px)",
          overflowY: "auto",
          padding: "10px",
        }}
      />
      {searchTerm.trim() !== "" && (
        <Button
          onClick={() => {
            setSearchTerm("");
            setExpandedKeys([]);
          }}
          style={{ marginRight: "10px" }}
        >
          Clear Filter
        </Button>
      )}
    </Drawer>
  );
}

export default TOCDrawer;
