/* Proper spacing between items */
.custom-tree .ant-tree-treenode {
  margin-bottom: 5px;
  padding: 5px 8px;
}

/* Highlight selected items */
.custom-tree .ant-tree-node-selected {
  background-color: #4babd8 !important;
  color: white !important;
  border-radius: 4px;
}

/* Style for sections */
.tree-section .ant-tree-title {
  padding: 4px 8px;
}

/* Style for documents */
.tree-document .ant-tree-title {
  padding: 4px 0;
}

/* Hover effect */
.custom-tree .ant-tree-node-content-wrapper:hover {
  background-color: #4babd89d;
}
