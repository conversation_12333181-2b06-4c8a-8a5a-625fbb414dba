/* ChatBubble.css */
.chat-messages-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 10px;
    flex-grow: 1; /* Allow it to grow and fill the available space */
  }
  
  .message-wrapper {
    display: flex;
    width: 100%;
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .message-wrapper.user {
    justify-content: flex-end;
  }
  
  .message-wrapper.assistant {
    justify-content: flex-start;
  }
  
  .user-bubble {
    background: linear-gradient(135deg, #0072ff 0%, #2f9fbe 100%);
    color: white;
    border-radius: 15px 15px 0 15px;
    padding: 10px 18px;
    max-width: 70%;
    min-width: 90px;
    box-shadow: 0 4px 15px rgba(0, 114, 255, 0.2);
    position: relative;
    transform-origin: right bottom;
    animation: popIn 0.3s ease-out;
  }
  
  @keyframes popIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .assistant-bubble {
    background-color: white;
    border-radius: 18px 18px 18px 0;
    padding: 12px 18px;
    max-width: 70%;
    min-width: 60px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
  }
  
  .assistant-bubble:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  
  .assistant-bubble.selected {
    border: 2px solid #eff1f3;
    background-color: #f0f7ff;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
  }
  
  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 14px;
  }
  
  .detail-level {
    font-size: 13px;
    color: #4e4d4d;
    background-color: rgba(240, 240, 240, 0.9);
    padding: 3px 10px;
    border-radius: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
  }
  
  .message-content {
    line-height: 1.6;
    font-size: 14px;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  .user-bubble .message-header strong {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
  }
  
  .user-bubble .message-content {
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  }
  
  /* Time indicator */
  .message-time {
    font-size: 11px;
    margin-top: 6px;
    opacity: 0.7;
  }
  
  .user-bubble .message-time {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .assistant-bubble .message-time {
    color: #666;
  }
  
.assistant-bubble.typing {
  height: 40px; /* Fixed height for the typing bubble */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f0f7ff; /* Light background */
  border-radius: 18px 18px 18px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: typingAnimation 1.5s steps(3) infinite;
}

/* Modify the typing text animation */
.typing-animation {
  display: inline-block;
}

.typing-animation::after {
  content: ".";
  animation: typingDots 1.5s steps(3) infinite;
}

@keyframes typingDots {
  0% {
    content: ".";
  }
  25% {
    content: "..";
  }
  50% {
    content: "...";
  }
  75% {
    content: "....";
  }
  100% {
    content: ".";
  }
}
