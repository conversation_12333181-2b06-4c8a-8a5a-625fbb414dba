import { useEffect, useState } from "react";
import {
  Card,
  Empty,
  Button,
  message,
  Tooltip,
  Pagination,
  Spin,
  Popconfirm,
  Typography,
  List,
  Badge,
  Space,
  Tag,
  Input,
  Select,
  Dropdown,
  Menu,
  Divider,
  Skeleton,
  Segmented,
  Avatar,
  Col,
  Row,
  Checkbox,
  Progress,
  Modal,
} from "antd";
import {
  FilePdfOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
  InfoCircleOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileTextOutlined,
  FileImageOutlined,
  FileUnknownOutlined,
  SearchOutlined,
  FilterOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
  SortAscendingOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  CloudUploadOutlined,
  MoreOutlined,
  CalendarOutlined,
  UserOutlined,
  FileOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import httpBase from "../../../../utils/http.utils";
const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

const ExistingFiles = ({
  existingFiles,
  page,
  loading,
  limit,
  totalFiles,
  setPage,
  fetchExistingFiles,
  fetchSources,
  fetchTOC,
  setActiveTab,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [viewMode, setViewMode] = useState("list");
  const handlePreview = async (file) => {
    console.log("file", file);
    try {
      const response = await httpBase().get(`/get_doc/Files/${file}`, {
        responseType: "json",
      });
      if (response.data && response.data.presigned_url) {
        window.open(response?.data?.presigned_url, "_blank");
      } else {
        console.error("Presigned URL not found in the response.");
      }
    } catch (error) {
      console.error("Error fetching file:", error);
      message.error("Failed to open file.");
    }
  };

  const handleDelete = async (fileName) => {
    try {
      const response = await httpBase().delete(`/delete_file/${fileName}`);
      if (response.status === 200) {
        message.success("File deleted successfully");
        setSelectedFiles(selectedFiles.filter((name) => name !== fileName));
        fetchExistingFiles(page);
        fetchSources();
        fetchTOC();
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      message.error("Failed to delete file");
    }
  };

  const handleBulkDelete = async () => {
    Modal.confirm({
      title: `Delete ${selectedFiles.length} file${
        selectedFiles.length > 1 ? "s" : ""
      }?`,
      icon: <ExclamationCircleFilled />,
      content: "This action cannot be undone.",
      okText: "Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk: async () => {
        let successCount = 0;
        let failCount = 0;

        for (const fileName of selectedFiles) {
          try {
            const response = await httpBase().delete(
              `/delete_file/${fileName}`
            );
            if (response.status === 200) {
              successCount++;
            } else {
              failCount++;
            }
          } catch (error) {
            failCount++;
          }
        }

        if (successCount > 0) {
          message.success(
            `${successCount} file${
              successCount > 1 ? "s" : ""
            } deleted successfully`
          );
          setSelectedFiles([]);
          fetchExistingFiles(page);
        }

        if (failCount > 0) {
          message.error(
            `Failed to delete ${failCount} file${failCount > 1 ? "s" : ""}`
          );
        }
      },
    });
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    setSelectedFiles([]);
  };

  const toggleFileSelection = (fileName) => {
    if (selectedFiles.includes(fileName)) {
      setSelectedFiles(selectedFiles.filter((name) => name !== fileName));
    } else {
      setSelectedFiles([...selectedFiles, fileName]);
    }
  };

  const toggleSelectAll = (e) => {
    if (e.target.checked) {
      const allFileNames = existingFiles.map((file) => file.name || file);
      setSelectedFiles(allFileNames);
    } else {
      setSelectedFiles([]);
    }
  };

  // Get file extension
  const getFileExtension = (filename) => {
    return filename
      .slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2)
      .toUpperCase();
  };

  // Get appropriate icon based on file extension
  const getFileIcon = (extension) => {
    switch (extension) {
      case "PDF":
        return <FilePdfOutlined style={{ fontSize: 24, color: "#e74c3c" }} />;
      case "DOC":
      case "DOCX":
        return <FileWordOutlined style={{ fontSize: 24, color: "#3498db" }} />;
      case "XLS":
      case "XLSX":
        return <FileExcelOutlined style={{ fontSize: 24, color: "#2ecc71" }} />;
      case "TXT":
      case "CSV":
        return <FileTextOutlined style={{ fontSize: 24, color: "#f39c12" }} />;
      case "JPG":
      case "JPEG":
      case "PNG":
      case "GIF":
        return <FileImageOutlined style={{ fontSize: 24, color: "#9b59b6" }} />;
      default:
        return (
          <FileUnknownOutlined style={{ fontSize: 24, color: "#95a5a6" }} />
        );
    }
  };

  // Render different views
  const renderListView = () => (
    <List
      className="file-list"
      itemLayout="horizontal"
      dataSource={existingFiles}
      renderItem={(file) => {
        const extension = getFileExtension(file.name || file);
        const fileName = file.name || file;
        const isSelected = selectedFiles.includes(fileName);

        return (
          <List.Item
            key={fileName}
            className={isSelected ? "selected-file-item" : ""}
            style={{
              transition: "all 0.3s ease",
              backgroundColor: isSelected ? " #f0f5ff" : "transparent",
              borderLeft: isSelected
                ? "3px solid #1890ff"
                : "3px solid transparent",
              padding: "12px",
              borderRadius: "8px",
              marginBottom: "8px",
            }}
            actions={[
              <Tooltip title="Preview">
                <Button
                  icon={
                    <EyeOutlined
                      style={{ color: "rgb(93, 132, 221)", width: "20px" }}
                    />
                  }
                  onClick={() => handlePreview(fileName)}
                  type="text"
                  size="small"
                />
              </Tooltip>,
              <Popconfirm
                title="Delete the knowledge data of this related Document"
                onConfirm={() => handleDelete(fileName)}
                okText="Yes"
                cancelText="No"
                icon={<DeleteOutlined style={{ color: "red" }} />}
                placement="topRight"
              >
                <Tooltip title="Delete">
                  <Button
                    icon={<DeleteOutlined style={{ color: "red" }} />}
                    type="text"
                    size="small"
                  />
                </Tooltip>
              </Popconfirm>,
            ]}
          >
            <div
              style={{ display: "flex", alignItems: "center", width: "100%" }}
            >
              <Checkbox
                checked={isSelected}
                onClick={(e) => e.stopPropagation()}
                onChange={() => toggleFileSelection(fileName)}
                style={{ marginRight: 16 }}
              />
              <div
                style={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                <Avatar
                  shape="square"
                  size={40}
                  icon={getFileIcon(extension)}
                  style={{
                    background: "white",
                    boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
                    marginRight: 16,
                  }}
                />
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Text strong ellipsis style={{ maxWidth: "70%" }}>
                      {fileName}
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          </List.Item>
        );
      }}
    />
  );

  // Custom skeleton loaders
  const renderSkeletonLoaders = () =>
    Array(6)
      .fill()
      .map((_, index) => (
        <Skeleton
          key={index}
          active
          avatar={{ shape: "square", size: 40 }}
          paragraph={{ rows: 1, width: ["80%"] }}
          style={{ margin: "16px 0" }}
        />
      ));

  return (
    // <div className="existing-files-container">
    <div style={{ height: "100%" }}>
      <Card
        title={
          <Space>
            <Title level={4} style={{ margin: 0 }}>
              Your Files
            </Title>
            <Badge count={totalFiles} style={{ backgroundColor: "#108ee9" }} />
          </Space>
        }
        bordered={true}
        style={{
          width: "100%",
          height: "80%",
          overflow: "auto",
        }}
      >
        {selectedFiles.length > 0 && (
          <div
            style={{
              marginBottom: 10,
              padding: "8px 16px",
              background: "#f0f5ff",
              borderRadius: 8,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Space>
              <Checkbox
                onChange={toggleSelectAll}
                checked={selectedFiles.length === existingFiles.length}
                indeterminate={
                  selectedFiles.length > 0 &&
                  selectedFiles.length < existingFiles.length
                }
              />
              <Text>
                {selectedFiles.length} of {existingFiles.length} selected
              </Text>
            </Space>
            <Space>
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={handleBulkDelete}
              >
                Delete
              </Button>
            </Space>
          </div>
        )}

        {loading ? (
          renderSkeletonLoaders()
        ) : existingFiles && existingFiles.length > 0 ? (
          <>{renderListView()}</>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Text style={{ fontSize: 16, marginBottom: 8 }}>
                  No files found
                </Text>
              </div>
            }
          ></Empty>
        )}
      </Card>

      <style jsx global>{`
        .file-list .ant-list-item {
          cursor: pointer;
        }
        .file-list .ant-list-item:hover {
          background-color: #fafafa;
        }
        .selected-file-item {
          border-left: 3px solid #1890ff;
        }
        .file-list .ant-list-item .ant-space-item {
          display: flex;
          align-items: center;
        }
      `}</style>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "flex-end",
          padding: "15px 0",
          borderTop: "1px solid #f0f0f0",
          background: "white",
          position: "sticky",
          bottom: "0px",
          width: "100%",
        }}
      >
        <Pagination
          current={page}
          pageSize={limit}
          total={totalFiles}
          onChange={handlePageChange}
          showSizeChanger={false}
          showQuickJumper={false}
        />
      </div>
    </div>
  );
};

export default ExistingFiles;
