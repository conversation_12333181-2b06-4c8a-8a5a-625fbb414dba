import { useState, useEffect } from "react";
import {
  Card,
  Image,
  Button,
  Modal,
  Input,
  Upload,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Tag,
  Popconfirm,
  List,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileOutlined,
  UploadOutlined,
  GlobalOutlined,
  MinusCircleFilled,
  LinkOutlined,
  CloseCircleOutlined,
  UserOutlined,
  FileAddOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import httpBase from "../../../../../utils/http.utils";
import { useAppDispatch } from "../../../../../hooks/reduxHooks";
import {
  deleteDocumentApi,
  documentImageDeleteApi,
  imageUpdateApi,
  imageUploadApi,
  UrlApi,
} from "../../../../../services/knowledgebase.service";
import "./SourceCard.css";
import dayjs from "dayjs";
import { removeListener } from "@reduxjs/toolkit";
import { use } from "react";
import { checkBalance } from "../../../../../helpers/checkBalance";

const { Title } = Typography;

export default function SourceCard({
  highlight,
  setHighlight,
  source,
  // onUpdate,
  fetchDefaultSources,
  // updateSourceInList, // No longer used - we always do full refresh
  tocNode,
  fetchSources,
  fetchTOC,
  scrollToTop, // Function to scroll the list to top
}) {
  const dispatch = useAppDispatch();
  const [content, setContent] = useState(source?.text || "");
  const [title, setTitle] = useState(source?.extra_info?.title ?? "Unknown");
  const [isEditing, setIsEditing] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [urlList, setUrlList] = useState([]);
  const [inputValue, setInputValue] = useState("");

  const [previewTitle, setPreviewTitle] = useState("");
  const [fileUploadLoading, setFileUploadLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Keep original values for cancel operation
  const [originalContent, setOriginalContent] = useState(source?.text || "");
  const [originalTitle, setOriginalTitle] = useState(
    source?.extra_info?.section_title || ""
  );

  const [expanded, setExpanded] = useState(false);
  const [baseSource, setBaseSource] = useState(
    source?.extra_info?.source_url || ""
  );
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [newImageUrl, setNewImageUrl] = useState("");
  const [showHint, setShowHint] = useState(false);

  const [fileList, setFileList] = useState([]);
  const [Images, setImages] = useState([]);
  const [resources, setResources] = useState([]);
  const [ImageNames, setImageNames] = useState([]);
  const text = source?.text || "";
  const sentences = source?.sentence || [];

  useEffect(() => {
    setContent(source?.text || "");
    setTitle(source?.extra_info?.title || "");
    setOriginalContent(source?.text || "");
    setOriginalTitle(source?.extra_info?.section_title || "");
    setImages(source?.extra_info?.images_url || []);
    setResources(source?.extra_info?.resource_url || []);
    setImageNames(source?.extra_info?.images);
  }, [source]);
  const [buttonHovered, setButtonHovered] = useState(false);

  // Helper function to refresh data and scroll to top instantly
  const refreshDataAndScrollToTop = async () => {
    try {
      if (fetchSources && tocNode) {
        // If we have fetchSources and tocNode, use that for filtered data
        await fetchSources(tocNode, true);
      } else {
        // Otherwise use fetchDefaultSources for full refresh
        await fetchDefaultSources(null, null, true);
      }

      // Instantly scroll to top without animation after data is refreshed
      if (scrollToTop) {
        // Small delay to ensure data is rendered, then scroll instantly
        setTimeout(() => {
          // Scroll to top instantly without smooth animation
          if (typeof scrollToTop === 'function') {
            scrollToTop(false); // Pass false to disable smooth scrolling
          }
        }, 50); // Reduced delay for faster response
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  const { source_url, ...filteredSource } = source; // Exclude source_url from root
  const {
    source_url: extraSourceUrl,
    images_url,
    ...filteredExtraInfo
  } = filteredSource.extra_info || {}; // Exclude source_url and images_url



  const handleSave = async () => {
    setEditLoading(true);
    message.warning("Please wait... editing a document may take some time!");
    try {
      setIsEditing(false);

      if (!title) {
        message.error("Title is required.");
        setEditLoading(false);
        return;
      }
      const now = new Date();
      console.log("now id", source.id_ || source.id);

      // Format the date and time in the desired format without the time zone info
      const date = now.toLocaleDateString("en-GB"); // for "DD/MM/YYYY"
      const time = now.toLocaleTimeString("en-GB", { hour12: false }); // for "HH:mm:ss"

      // Combine date and time without the time zone part
      const formattedDateTime = `${date} ${time}`;

      // Prepare the updated data, making sure `id_` is included.
      const updatedSource = {
        ...filteredSource, // Spread source without source_url
        question_id: null,
        id_: source.id_ || source.id, // Ensure `id_` is included
        text: content,
        title,
        extra_info: {
          ...filteredExtraInfo, // Use extra_info without source_url
          title: title,
          images: ImageNames,
          hash_id: source?.id_,
          created_at:
            source?.extra_info?.created_at || new Date().toISOString(),
          updated_at: formattedDateTime,
          updated_by: localStorage.getItem("userId"),
        },
      };
      console.log("updatedSource", updatedSource);
      // API Call
      const response = await httpBase().post(
        "/qdrant/update_nodes",
        updatedSource,
        {
          params: { collection_name: "test_page_info" },
        }
      );

      if (response.status === 200) {
        // Refresh data and scroll to top after successful update
        await refreshDataAndScrollToTop();
        fetchTOC();
        message.success("Changes saved successfully!");
        setHighlight(false);
        // onUpdate(updatedSource);
      } else {
        message.error("Failed to update. Please try again.");
      }
    } catch (error) {
      console.error("Error updating source:", error);
      message.error("An error occurred while updating.");
    } finally {
      setEditLoading(false);
    }
  };

  // Completely redesigned sentence matching algorithm
  const findExactSentencePositions = (fullText, sentencesToHighlight) => {
    if (
      !fullText ||
      !sentencesToHighlight ||
      sentencesToHighlight.length === 0
    ) {
      return [];
    }

    const positions = [];

    for (const sentence of sentencesToHighlight) {
      if (!sentence || sentence.trim() === "") continue;

      // Step 1: Try exact matching first
      const exactIndex = fullText.indexOf(sentence);

      if (exactIndex !== -1) {
        positions.push({
          start: exactIndex,
          end: exactIndex + sentence.length,
          text: sentence,
          matched: "exact",
        });
        continue; // Skip to next sentence if exact match is found
      }

      // Step 2: Try with normalized whitespace
      const normalizedSentence = sentence.replace(/\s+/g, " ").trim();
      const normalizedText = fullText.replace(/\s+/g, " ").trim();
      const normalizedIndex = normalizedText.indexOf(normalizedSentence);

      if (normalizedIndex !== -1) {
        // Map back to original text position
        // This is approximate and may need refinement
        let originalStart = 0;
        let normalizedPos = 0;

        // Find the corresponding position in the original text
        while (
          normalizedPos < normalizedIndex &&
          originalStart < fullText.length
        ) {
          if (
            !/\s/.test(fullText[originalStart]) ||
            (fullText[originalStart] === " " &&
              normalizedText[normalizedPos] === " ")
          ) {
            normalizedPos++;
          }
          originalStart++;
        }

        // Find end position similarly
        let originalEnd = originalStart;
        let matchEnd = normalizedPos + normalizedSentence.length;

        while (normalizedPos < matchEnd && originalEnd < fullText.length) {
          if (
            !/\s/.test(fullText[originalEnd]) ||
            (fullText[originalEnd] === " " &&
              normalizedText[normalizedPos] === " ")
          ) {
            normalizedPos++;
          }
          originalEnd++;
        }

        positions.push({
          start: originalStart,
          end: originalEnd,
          text: fullText.substring(originalStart, originalEnd),
          matched: "normalized",
        });
        continue;
      }

      // Step 3: Try finding the most significant chunk
      // Use significant chunks (3+ consecutive words) to find approximate matches
      const sentenceWords = normalizedSentence.split(" ");
      let longestChunkIndex = -1;
      let longestChunkLength = 0;
      let longestChunkText = "";

      // Look for chunks of at least 3 consecutive words
      for (let i = 0; i < sentenceWords.length - 2; i++) {
        for (let j = i + 2; j < sentenceWords.length; j++) {
          const chunk = sentenceWords.slice(i, j + 1).join(" ");
          if (
            chunk.length > longestChunkLength &&
            normalizedText.includes(chunk)
          ) {
            longestChunkLength = chunk.length;
            longestChunkText = chunk;
            longestChunkIndex = normalizedText.indexOf(chunk);
          }
        }
      }

      if (longestChunkIndex !== -1) {
        // Map to original text as before
        let originalStart = 0;
        let normalizedPos = 0;

        while (
          normalizedPos < longestChunkIndex &&
          originalStart < fullText.length
        ) {
          if (
            !/\s/.test(fullText[originalStart]) ||
            (fullText[originalStart] === " " &&
              normalizedText[normalizedPos] === " ")
          ) {
            normalizedPos++;
          }
          originalStart++;
        }

        // Find a good sentence boundary before our chunk
        while (originalStart > 0) {
          if (
            /[.!?]\s/.test(fullText.substring(originalStart - 2, originalStart))
          ) {
            break;
          }
          originalStart--;
        }

        // Find end position
        let originalEnd = originalStart;
        let matchEnd = normalizedPos + longestChunkText.length;

        while (normalizedPos < matchEnd && originalEnd < fullText.length) {
          if (
            !/\s/.test(fullText[originalEnd]) ||
            (fullText[originalEnd] === " " &&
              normalizedText[normalizedPos] === " ")
          ) {
            normalizedPos++;
          }
          originalEnd++;
        }

        // Extend to end of sentence
        while (originalEnd < fullText.length) {
          if (
            /[.!?]\s/.test(fullText.substring(originalEnd, originalEnd + 2))
          ) {
            originalEnd += 2;
            break;
          }
          if (originalEnd + 1 === fullText.length) {
            originalEnd++;
            break;
          }
          originalEnd++;
        }

        positions.push({
          start: originalStart,
          end: originalEnd,
          text: fullText.substring(originalStart, originalEnd),
          matched: "chunk",
        });
        continue;
      }

      // Step 4: If all else fails, use keyword matching
      // Find sentences containing key keywords
      const keywords = sentenceWords
        .filter((word) => word.length > 4) // Only consider significant words
        .map((word) => word.replace(/[^\w]/g, "")); // Remove punctuation

      if (keywords.length > 0) {
        // Find regions in the text containing these keywords
        const textLower = fullText.toLowerCase();
        let matchFound = false;

        for (const keyword of keywords) {
          if (keyword.length < 4) continue;

          const keywordIndex = textLower.indexOf(keyword.toLowerCase());
          if (keywordIndex !== -1) {
            // Find sentence boundaries
            let sentenceStart = keywordIndex;
            while (sentenceStart > 0) {
              if (
                /[.!?]\s/.test(
                  fullText.substring(sentenceStart - 2, sentenceStart)
                )
              ) {
                break;
              }
              sentenceStart--;
            }

            let sentenceEnd = keywordIndex + keyword.length;
            while (sentenceEnd < fullText.length) {
              if (
                /[.!?]\s/.test(fullText.substring(sentenceEnd, sentenceEnd + 2))
              ) {
                sentenceEnd += 2;
                break;
              }
              if (sentenceEnd + 1 === fullText.length) {
                sentenceEnd++;
                break;
              }
              sentenceEnd++;
            }

            positions.push({
              start: sentenceStart,
              end: sentenceEnd,
              text: fullText.substring(sentenceStart, sentenceEnd),
              matched: "keyword",
            });
            matchFound = true;
            break;
          }
        }

        if (matchFound) continue;
      }

      // If we got here, no match was found for this sentence
      console.log("No match found for sentence:", sentence);
    }

    // Sort positions by start index and remove overlaps
    positions.sort((a, b) => a.start - b.start);

    // Remove overlapping positions
    const result = [];
    let lastEnd = -1;

    for (const pos of positions) {
      if (pos.start >= lastEnd) {
        result.push(pos);
        lastEnd = pos.end;
      }
    }

    return result;
  };

  // Improved text highlighting
  const renderTextWithHighlights = (text, highlightSentences) => {
    if (!text) return null;
    if (!highlightSentences || highlightSentences.length === 0) return text;

    const positions = findExactSentencePositions(text, highlightSentences);
    if (positions.length === 0) return text;

    const parts = [];
    let lastEnd = 0;

    positions.forEach((pos, index) => {
      // Add text before highlight
      if (pos.start > lastEnd) {
        parts.push(
          <span key={`text-${index}`}>
            {text.substring(lastEnd, pos.start)}
          </span>
        );
      }

      // Add highlighted text
      parts.push(
        <span
          key={`highlight-${index}`}
          style={{ backgroundColor: "#ffeb3b", padding: "2px 0" }}
        >
          {text.substring(pos.start, pos.end)}
        </span>
      );

      lastEnd = pos.end;
    });

    // Add remaining text after last highlight
    if (lastEnd < text.length) {
      parts.push(<span key="text-last">{text.substring(lastEnd)}</span>);
    }

    return parts;
  };

  // Improved text display logic with better context and truncation tracking
  const getDisplayText = () => {
    // Word limit for truncated view
    const WORD_LIMIT = 100;

    // Track if content is being truncated (will need "Show More" button)
    let isContentTruncated = false;

    // When expanded, show the full text with highlights
    if (expanded) {
      return [renderTextWithHighlights(text, highlight ? sentences : []), true];
    }

    // When not expanded, show a truncated version
    if (highlight && sentences.length > 0) {
      // Find positions of highlighted sentences
      const positions = findExactSentencePositions(text, sentences);

      if (positions.length === 0) {
        // No highlights found, fallback to regular truncation
        const words = text.split(/\s+/);
        const truncated = words.slice(0, WORD_LIMIT).join(" ");

        // Mark as truncated if the original text had more words
        isContentTruncated = words.length > WORD_LIMIT;

        return [
          <span key="text-content">
            {truncated}
            {isContentTruncated ? "..." : ""}
          </span>,
          isContentTruncated, // Return the truncation flag as the second element in array
        ];
      }

      // Get the text around the first highlight for context
      const firstPos = positions[0];

      // Find the beginning of the sentence or paragraph containing the highlight
      let contextStart = Math.max(0, firstPos.start - 200); // More context (200 chars)
      let contextEnd = Math.min(text.length, firstPos.end + 200);

      // Adjust to complete sentences or words where possible
      while (contextStart > 0 && !/[.!?\n]/.test(text[contextStart]))
        contextStart--;
      if (contextStart > 0) contextStart++; // Move past the punctuation

      while (contextEnd < text.length && !/[.!?\n]/.test(text[contextEnd]))
        contextEnd++;
      if (contextEnd < text.length) contextEnd++; // Include the punctuation

      // Create display parts
      const parts = [];

      // If we're not starting from the beginning, we're truncating content
      if (contextStart > 0) {
        isContentTruncated = true;
        parts.push(<span key="start-ellipsis">...</span>);
      }

      // Add the text before the highlight
      if (firstPos.start > contextStart) {
        parts.push(
          <span key="before-highlight">
            {text.substring(contextStart, firstPos.start)}
          </span>
        );
      }

      // Add the highlighted text
      parts.push(
        <span
          key="highlight"
          style={{ backgroundColor: "#ffeb3b", padding: "2px 0" }}
        >
          {text.substring(firstPos.start, firstPos.end)}
        </span>
      );

      // Add the text after the highlight
      if (firstPos.end < contextEnd) {
        parts.push(
          <span key="after-highlight">
            {text.substring(firstPos.end, contextEnd)}
          </span>
        );
      }

      // If we're not going to the end, we're truncating content
      if (contextEnd < text.length) {
        isContentTruncated = true;
        parts.push(<span key="end-ellipsis">...</span>);
      }

      return [parts, isContentTruncated]; // Return the content and truncation flag
    }

    // No highlights, just truncate the text
    const words = text.split(/\s+/);
    const truncated = words.slice(0, WORD_LIMIT).join(" ");

    // Mark as truncated if the original text had more words
    isContentTruncated = words.length > WORD_LIMIT;

    return [
      <span>
        {truncated}
        {isContentTruncated ? "..." : ""}
      </span>,
      isContentTruncated, // Return the truncation flag
    ];
  };

  // Get the text content to display and whether it's truncated
  const [displayedText, isContentTruncated] = getDisplayText();

  // Always show "Show More" button if content is truncated or expanded
  const shouldShowButton = isContentTruncated || expanded;

  const textContainerStyle = {
    border: "None",
    padding: "20px",
    backgroundColor: "#f9f9f9",
  };

  const textContentStyle = {
    textAlign: "justify",
    marginBottom: "15px",
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setContent(originalContent);
    setTitle(originalTitle);
  };

  const handleAddImageUrl = () => {
    if (!newImageUrl.trim()) {
      message.warning("Please enter a valid image URL");
      return;
    }
    setImages((prev) => [...prev, newImageUrl.trim()]);
    setNewImageUrl("");
  };

  const handleBeforeUpload = (file) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("data", JSON.stringify({ test: "a" })); // Stringify the object
    formData.append("source", source?.extra_info?.source);

    dispatch(
      imageUploadApi({
        data: formData,
        params: { source: source?.extra_info?.source },
        finalCallback: () => {},
        successCallback: (response) => {
          setImages((prev) => [
            ...prev,
            { name: response.name, url: response.url },
          ]);
          setImageNames((prev) => [...prev, response.name]);
        },
        failureCallback: () => {},
      })
    );
  };

  const handleRemoveImage = (name) => {
    const updatedImageNames = ImageNames?.filter((item) => {
      return item !== name;
    });

    const data = {
      source: source?.extra_info?.source,
      object_name: name,
    };

    dispatch(
      documentImageDeleteApi({
        params: data,
        finalCallback: () => {},
        successCallback: (response) => {
          dispatch(
            imageUpdateApi({
              data: {
                document_id: source?.id_,
                collection_name: "test_page_info",
                images: updatedImageNames,
              },
              finalCallback: () => {},
              successCallback: async () => {
                message.success("Image deleted successfully!");
                // Refresh data and scroll to top after image deletion
                await refreshDataAndScrollToTop();
              },
              failureCallback: () => {
                message.error("Image not deleted! Something went wrong.");
              },
            })
          );
        },
        failureCallback: () => {},
      })
    );
  };

  const handleSaveRes = () => {
    const hash_id = source?.extra_info?.hash_id;

    addURLApi(hash_id);
    handleSaveImages();
  };

  const handleSaveImages = () => {
    setFileUploadLoading(true);
    const formData = new FormData();
    fileList.forEach((file, index) => {
      formData.append("files", file.originFileObj);
    });
    formData.append("data", JSON.stringify({ test: "a" }));
    formData.append("source", source?.extra_info?.source);

    dispatch(
      imageUploadApi({
        data: formData,
        params: { source: source?.extra_info?.source },
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("image Upload", response);
          const imgNames = response?.data?.map((item) => {
            return item?.name ?? "";
          });

          dispatch(
            imageUpdateApi({
              data: {
                document_id: source?.id_,
                collection_name: "test_page_info",
                images: [
                  ...ImageNames,
                  ...imgNames,
                  // ...Images,
                ],
              },
              finalCallback: () => {
                setFileUploadLoading(false);
              },
              successCallback: async () => {
                message.success("Image saved successfully!");
                setFileList([]);
                setIsImageModalVisible(false);
                // Refresh data and scroll to top after image update
                await refreshDataAndScrollToTop();
                setHighlight(false);
              },
              failureCallback: () => {},
            })
          );
        },
        failureCallback: () => {},
      })
    );
  };

  const addURLApi = (hash_id, resourcesToUse = resources) => {
    // Breast Implant Recovery Timeline
    const data = {
      url: [...(resourcesToUse || []), ...(urlList || [])],
      id: hash_id,
    };
    dispatch(
      UrlApi({
        data,
        finalCallback: () => {
          setFileUploadLoading(false);
        },
        successCallback: async (response) => {
          setUrlList([]);
          message.success("Resource changed successfully!");
          // Refresh data and scroll to top after URL update
          await refreshDataAndScrollToTop();
          setIsImageModalVisible(false);
          setHighlight(false);
        },
        failureCallback: () => {
          message.error("Failed to update URL! Insert Valid public URL");
        },
      })
    );
  };

  async function openUrlAsBlob(url) {
    try {
      const response = await fetch(url); // Fetch the file from the URL
      const blob = await response.blob(); // Convert the response to a Blob
      const blobUrl = URL.createObjectURL(blob); // Create an Object URL from the Blob
      return blobUrl;
    } catch (error) {
      console.error("Error fetching the URL:", error);
      return null;
    }
  }

  const handleDeleteDocument = () => {
    setDeleteLoading(true);
    message.warning("Please wait... deleting a document may take some time!");
    dispatch(
      deleteDocumentApi({
        params: { id: source.id_ },
        finalCallback: () => {
          setDeleteLoading(false);
        },
        successCallback: async (response) => {
          // Refresh data and scroll to top after document deletion
          await refreshDataAndScrollToTop();
          setHighlight(false);
          message.success("Document deleted successfully!");
        },
        failureCallback: (error) => {
          message.error(error.message);
        },
      })
    );
  };

  const handleRemoveResource = (indexToRemove) => {
    const updatedResources = resources.filter(
      (_, index) => index !== indexToRemove
    );

    setResources(updatedResources);

    const hash_id = source?.extra_info?.hash_id;
    addURLApi(hash_id, updatedResources);
  };

  const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  const handlePreview = async (file) => {
    // If the file doesn't have a url (not uploaded yet), create one using FileReader
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }

    setPreviewImage(file.url || file.preview);

    setPreviewVisible(true);
    setPreviewTitle(
      file.name || file.url.substring(file.url.lastIndexOf("/") + 1)
    );
  };
  const addUrl = (url) => {
    let trimmedUrl = url.trim();

    // If it doesn't start with http/https, add https://
    if (!/^https?:\/\//i.test(trimmedUrl)) {
      trimmedUrl = "https://" + trimmedUrl;
    }

    try {
      new URL(trimmedUrl); // Validate
      setUrlList((prev) => [...prev, trimmedUrl]);
    } catch {
      message.error("Invalid URL.");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      setShowHint(false);
      addUrl(inputValue);
      setInputValue("");
    }
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);

    // Define options for formatting the date
    const options = {
      year: "numeric", // 2-digit year (e.g., "24" instead of "2024")
      month: "short", // e.g., "May"
      day: "numeric", // e.g., "12"
      hour: "numeric", // e.g., "5"
      minute: "numeric", // e.g., "00"
      hour12: true, // Display in 12-hour format with PM/AM
    };

    // Format the date using toLocaleString
    return date.toLocaleString("en-US", options);
  };
  return (
    <Card
      className="source-card"
      style={{
        marginBottom: 16,
        width: "100%",
        borderRadius: "12px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        border: "solid 1px #E5E4E2",
        marginRight: "8px",
      }}
      styles={{ body: { padding: 0, borderRadius: 0 } }}
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
            }}
          >
            {isEditing ? (
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                style={{
                  fontSize: "20px",
                  fontWeight: 500,
                  width: "400px",
                }}
              />
            ) : (
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <Title level={4} style={{ margin: 0 }}>
                  {title}
                </Title>
              </div>
            )}
          </div>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              type="default"
              size="small"
              icon={<EditOutlined />}
              onClick={async () => {
                const balance = await checkBalance();

                // If balance is 0, stop the function and show a message
                if (balance < 1) {
                  message.error(
                    "You don't have enough credits. Please recharge your account."
                  );
                  return;
                }

                setIsEditing(true);
              }}
              style={{ borderRadius: "6px" }}
              loading={editLoading}
              disabled={editLoading}
            >
              Edit
            </Button>
            <Button
              type="default"
              size="small"
              icon={<DeleteOutlined />}
              loading={deleteLoading}
              disabled={deleteLoading}
              style={{
                borderRadius: "6px",
                backgroundColor: "#ff4d4f",
                color: "#fff",
              }}
              onClick={async () => {
                Modal.confirm({
                  title: "Are you sure you want to delete this item?",
                  icon: <ExclamationCircleOutlined />,
                  content: "This action cannot be undone.",
                  okText: "Yes",
                  okButtonProps: {
                    style: {
                      backgroundColor: "#ff4d4f",
                      borderColor: "#ff4d4f",
                    },
                  },
                  cancelText: "No",
                  transitionName: "",
                  maskTransitionName: "",
                  onOk: handleDeleteDocument,
                });
              }}
            >
              Delete
            </Button>
          </div>
        </div>
      }
    >
      {/* CONTENTS */}
      <div
        style={{
          background: "#f8f9fa",
          borderRadius: "8px",
          marginBottom: "16px",
        }}
      >
        {isEditing ? (
          <div style={{ marginBottom: "16px" }}>
            <Input.TextArea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              autoSize={{ minRows: 3, maxRows: 10 }}
              style={{
                fontSize: "16px",
                marginBottom: "12px",
                borderRadius: "0",
              }}
            />
            <div
              style={{
                margin: "0 12px",
                display: "flex",
                gap: "8px",
                justifyContent: "flex-end",
              }}
            >
              <Button onClick={handleCancelEdit}>Cancel</Button>
              <Button type="primary" onClick={handleSave}>
                Save Changes
              </Button>
            </div>
          </div>
        ) : (
          <div style={textContainerStyle}>
            <div style={textContentStyle}>{displayedText}</div>

            <Row
              style={{
                display: "flex",
                width: "100%",
                justifyContent: "space-between",
                alignItems: "center",
              }}
              gutter={[12, 12]}
            >
              <Col>
                {shouldShowButton && (
                  <Tag
                    size="small"
                    onClick={() => setExpanded(!expanded)}
                    style={{
                      borderRadius: "6px",
                      cursor: "pointer",
                    }}
                  >
                    {expanded ? "Show Less" : "Show More"}
                  </Tag>
                )}
              </Col>

              <Col>
                {source?.updated_at && (
                  <span
                    style={{
                      fontSize: "12px",
                      fontStyle: "italic",
                      borderRight: "1px solid #ccc",
                      padding: "5px",
                    }}
                  >
                    Edited by: {source.updated_by} at{" "}
                    {formatDate(source.updated_at)}
                  </span>
                )}
                {source?.extra_info?.source_url?.url && (
                  <Button
                    type="link"
                    icon={<FileOutlined />}
                    style={{ border: "none" }}
                    onClick={() => {
                      window.open(
                        source?.extra_info?.source_url?.url,
                        "_blank",
                        "noopener,noreferrer"
                      );
                    }}
                  >
                    Original Doc
                  </Button>
                )}
              </Col>
            </Row>
          </div>
        )}
      </div>

      <Row
        gutter={[12, 12]}
        style={{
          display: "flex",
          alignItems: "center",
          margin: "15px",
        }}
      >
        {resources.map((resource, index) => {
          // Extract video ID from YouTube URL
          const extractVideoId = (youtubeUrl) => {
            const standardMatch = youtubeUrl.match(
              /youtube\.com\/watch\?v=([^&]+)/
            );
            const shortMatch = youtubeUrl.match(/youtu\.be\/([^?]+)/);
            const embedMatch = youtubeUrl.match(/youtube\.com\/embed\/([^?]+)/);

            if (standardMatch) return standardMatch[1];
            else if (shortMatch) return shortMatch[1];
            else if (embedMatch) return embedMatch[1];
            else return null;
          };

          const videoId = extractVideoId(resource);
          const thumbnail = `https://img.youtube.com/vi/${videoId}/sddefault.jpg`;

          return (
            <Col key={index}>
              <div
                style={{
                  width: "120px",
                  height: "90px",
                  borderRadius: "8px",
                  border: "1px solid #f0f0f0",
                  overflow: "hidden",
                  position: "relative",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                }}
              >
                <img
                  src={thumbnail}
                  alt="YouTube video"
                  style={{
                    cursor: "pointer",
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                  onClick={() => window.open(resource, "_blank")}
                />

                {/* Play Icon Overlay */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    pointerEvents: "none", // This allows clicks to pass through to the image
                  }}
                >
                  <div
                    style={{
                      width: "30px",
                      height: "30px",
                      borderRadius: "50%",
                      backgroundColor: "rgba(0, 0, 0, 0.6)",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {/* Triangle Play Icon */}
                    <div
                      style={{
                        width: 0,
                        height: 0,
                        borderTop: "7px solid transparent",
                        borderBottom: "7px solid transparent",
                        borderLeft: "12px solid white",
                        marginLeft: "3px", // Slight offset to center visually
                      }}
                    />
                  </div>
                </div>

                <div
                  style={{
                    position: "absolute",
                    top: "4px",
                    right: "4px",
                  }}
                >
                  <Button
                    type="text"
                    icon={<DeleteOutlined style={{ color: "#ff4d4f" }} />}
                    size="small"
                    onClick={() => handleRemoveResource(index)}
                  />
                </div>
              </div>
            </Col>
          );
        })}
        {Images.length > 0 ? (
          <div>
            <Row gutter={[12, 12]}>
              {Images.map((item) => (
                <Col key={item.name}>
                  <div
                    style={{
                      position: "relative",
                      border: "1px solid #f0f0f0",
                      width: "120px",
                      height: "90px",
                    }}
                  >
                    <Image
                      src={item.url || "/placeholder.svg"}
                      alt={`Source ${item.name}`}
                      width="120px"
                      height="90px"
                      style={{
                        objectFit: "cover",
                        borderRadius: "8px",
                      }}
                      preview={{
                        mask: (
                          <div style={{ borderRadius: "8px" }}>
                            <EyeOutlined /> Preview
                          </div>
                        ),
                      }}
                    />
                    <Button
                      type="text"
                      icon={<DeleteOutlined style={{ color: "#ff4d4f" }} />}
                      size="small"
                      style={{
                        position: "absolute",
                        top: 4,
                        right: 4,
                        background: "rgba(255,255,255,0.9)",
                        borderRadius: "50%",
                        minWidth: "auto",
                        width: "24px",
                        height: "24px",
                        padding: 0,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        zIndex: "99",
                      }}
                      onClick={() => handleRemoveImage(item?.name)}
                    />
                  </div>
                </Col>
              ))}

              <Col>
                <Button
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={() => setIsImageModalVisible(true)}
                  style={{
                    width: 120,
                    height: 100,
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "column",
                  }}
                >
                  Add Resource
                </Button>
              </Col>
            </Row>
          </div>
        ) : (
          <div
            style={{
              textAlign: "center",
              padding: "10px 15px ",
            }}
          >
            {/* Upload Image +  Button */}
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={() => {
                setIsImageModalVisible(true);
              }}
              style={{
                width: "130px",
                height: "80px",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
                background: "#f8f9fa",
                color: "#8c8c8c",
              }}
            >
              Add resources
            </Button>
          </div>
        )}
      </Row>

      <Modal
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <UploadOutlined style={{ marginRight: 8, fontSize: 20 }} />
            <span style={{ fontSize: 18, fontWeight: 500 }}>
              Upload from Device
            </span>
          </div>
        }
        visible={isImageModalVisible}
        onOk={handleSaveRes}
        onCancel={() => {
          setIsImageModalVisible(false);
          setFileList([]);
          setUrlList([]);
        }}
        width={700}
        okText={
          <Button
            type="text"
            loading={fileUploadLoading}
            style={{ color: "#fff" }}
            disabled={
              !fileList?.length > 0 &&
              !urlList?.length > 0 &&
              !resources?.length > 0
            }
          >
            {fileUploadLoading ? <>Uploading</> : <>Save</>}
          </Button>
        }
        cancelText="Cancel"
      >
        <div
          style={{
            padding: "10px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div
            style={{
              width: "100%",
              padding: 10,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(239, 239, 239, 0.29)",
              borderRadius: "8px",
            }}
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              onPreview={handlePreview}
              onChange={({ fileList: newFileList }) => setFileList(newFileList)}
              beforeUpload={() => {
                return false;
              }}
              multiple
              accept="image/*"
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload image</div>
              </div>
            </Upload>
          </div>
          <div
            style={{
              width: "100%",
              padding: 10,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Input
              prefix={<GlobalOutlined style={{ color: "skyblue" }} />}
              placeholder="Insert URL and press Enter"
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value); // Set the input value
                setShowHint(e.target.value.trim() !== ""); // Show hint if input is not empty
              }}
              onKeyDown={handleKeyPress}
              style={{
                width: "90%",
                margin: "0 auto",
              }}
            />
            {showHint && (
              <span
                style={{
                  fontSize: "12px",
                  color: "rgb(64, 179, 255)",
                  marginTop: "5px",
                  gap: "5x",
                }}
              >
                Press Enter to add this URL
              </span>
            )}
            {urlList.length > 0 && (
              <List
                style={{
                  width: "90%",
                  margin: "0 auto",
                }}
                dataSource={urlList}
                header={<div>Selected URLs</div>}
                renderItem={(item, index) => (
                  <List.Item
                    key={index}
                    actions={[
                      <a
                        onClick={() => {
                          const newList = urlList.filter((_, i) => i !== index);
                          setUrlList(newList);
                        }}
                      >
                        <MinusCircleFilled style={{ color: "red" }} />
                      </a>,
                    ]}
                  >
                    <div
                      style={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        minWidth: "80%",
                        flex: 1,
                      }}
                    >
                      <Tooltip title={item}>{item}</Tooltip>
                    </div>
                  </List.Item>
                )}
              />
            )}
          </div>

          <Modal
            visible={previewVisible}
            title={previewTitle}
            footer={null}
            onCancel={() => setPreviewVisible(false)}
          >
            <img
              alt="example"
              style={{ width: "100%" }}
              src={previewImage || "/placeholder.svg"}
            />
          </Modal>
        </div>
      </Modal>
    </Card>
  );
}
