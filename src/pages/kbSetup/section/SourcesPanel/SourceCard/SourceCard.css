/* Main card animations */
.source-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Title animations */
.source-card .ant-card-head-title h4 {
  position: relative;
  display: inline-block;
}

.source-card .ant-card-head-title h4::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #1890ff;
  transition: width 0.3s ease;
}

.source-card:hover .ant-card-head-title h4::after {
  width: 100%;
}

/* Button animations */
.source-card .ant-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.source-card .ant-btn:hover {
  transform: translateY(-2px);
}

.source-card .ant-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.source-card .ant-btn:hover::before {
  left: 100%;
}

/* Content area animations */
.source-card .content {
  transition: background-color 0.3s ease;
}

.source-card .content span[style*="background-color: #FFFF00"] {
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Expand/collapse animation for content */
.source-card .content {
  /* max-height: 1000px; */
  /* overflow: hidden; */
  transition: 0.5s ease;
}

/* Image gallery animations */
.source-card .ant-image {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.source-card .ant-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

.source-card .ant-image img {
  transition: transform 0.5s ease;
}

.source-card .ant-image:hover img {
  transform: scale(1.1);
}

/* Add image button animation */
.source-card button[type="dashed"] {
  transition: all 0.3s ease;
  overflow: hidden;
}

.source-card button[type="dashed"]:hover {
  background: #f0f5ff;
  border-color: #1890ff;
}

.source-card button[type="dashed"] .anticon {
  transition: transform 0.3s ease;
}

.source-card button[type="dashed"]:hover .anticon {
  transform: rotate(90deg);
}

/* Tag animations */
.source-card .ant-tag {
  transition: all 0.3s ease;
}

.source-card .ant-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modal animations */
.ant-modal {
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Upload area animations */
.ant-upload.ant-upload-select-picture-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.ant-upload.ant-upload-select-picture-card:hover {
  border-color: #1890ff;
  background: #f0f5ff;
}

.ant-upload.ant-upload-select-picture-card .anticon {
  transition: transform 0.3s ease;
}

.ant-upload.ant-upload-select-picture-card:hover .anticon {
  transform: scale(1.2);
}

/* Edit mode transitions */
.ant-input,
.ant-input-textarea {
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.ant-input:focus,
.ant-input-textarea:focus {
  animation: focusGlow 1.5s infinite alternate;
}

@keyframes focusGlow {
  from {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
  to {
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
  }
}

/* Delete button animation */
.source-card button[icon="DeleteOutlined"] {
  transition: all 0.3s ease;
}

.source-card button[icon="DeleteOutlined"]:hover {
  background-color: #fff1f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* Divider animation */
.source-card .ant-divider {
  position: relative;
  overflow: hidden;
}

.source-card .ant-divider::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(24, 144, 255, 0.2),
    transparent
  );
  animation: dividerShine 3s infinite;
}

@keyframes dividerShine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Popconfirm animation */
.ant-popover {
  animation: popoverFadeIn 0.2s ease;
}

@keyframes popoverFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Image preview mask animation */
.ant-image-mask {
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-image-mask:hover {
  background: rgba(0, 0, 0, 0.7);
}

.ant-image-mask-info {
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.3s ease;
}

.ant-image-mask:hover .ant-image-mask-info {
  transform: translateY(0);
  opacity: 1;
}

/* Loading state animations */
.ant-btn-loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade in animation for newly added elements */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.source-card .ant-col {
  animation: fadeIn 0.3s ease;
}

/* Highlighted text animation */
@keyframes highlightPulse {
  0% {
    background-color: #ffff00;
  }
  50% {
    background-color: #ffffaa;
  }
  100% {
    background-color: #ffff00;
  }
}

.source-card span[style*="background-color: #FFFF00"] {
  animation: highlightPulse 2s infinite;
  transition: all 0.3s ease;
}

.source-card span[style*="background-color: #FFFF00"]:hover {
  background-color: #ffd700 !important;
}
