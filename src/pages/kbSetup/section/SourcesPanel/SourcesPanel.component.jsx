import { useState, useEffect, useRef } from "react";
import { Card, List, Button, Typography, Spin } from "antd";
import {
  ArrowLeftOutlined,
  UnorderedListOutlined,
  FolderOutlined,
  ExclamationCircleOutlined,
  InboxOutlined,
  LoadingOutlined,
  ArrowUpOutlined,
} from "@ant-design/icons";
import TOCDrawer from "../TocDrawer/TocDrawer.component";
import SourceCard from "./SourceCard/SourceCard.component";
import { fetch_toc } from "../../hook/fetchdata";
import { useNavigate } from "react-router-dom";
import { getAllSourcesApi } from "../../../../services/knowledgebase.service";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import FileManagerDrawercomponent from "../FileManagerDrawer/FileManagerDrawer.component";

const { Title } = Typography;

export default function SourcesPanel({
  setHighlight,
  highlight,
  sourcesLoading,
  setDefaultSources,
  fetchDefaultSources,
  updateSourceInList,
  sources,
  onBackToDefault,
  isSubsourcesView,
  setSourceFilter,
  offsetValue,
  sourceFilter,
  setSubSources,
  setOffsetValue,
  filtersTOC,
  setFiltersTOC,
  hasMore: parentHasMore,
  setHasMore: setParentHasMore,
  currentItemCount,
  setCurrentItemCount,
}) {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [fileManager, setFileManagerVisible] = useState(false);
  const listRef = useRef(null);
  const [toc, setToc] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [tocNode, setTocNode] = useState();
  const [isListScrollable, setIsListScrollable] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Use parent hasMore state when available, fallback to local state
  const effectiveHasMore = parentHasMore !== undefined ? parentHasMore : hasMore;
  const effectiveSetHasMore = setParentHasMore || setHasMore;

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    fetchTOC();
  }, []);

  useEffect(() => {
    // Check if the list is scrollable after sources change
    if (listRef.current) {
      setIsListScrollable(
        listRef.current.scrollHeight > listRef.current.clientHeight
      );
    }
  }, [sources]);

  useEffect(() => {
    const handleListScroll = () => {
      if (!listRef.current) return;
      setShowScrollTop(listRef.current.scrollTop > 100);
    };
    const ref = listRef.current;
    if (ref) {
      ref.addEventListener("scroll", handleListScroll);
    }
    return () => {
      if (ref) {
        ref.removeEventListener("scroll", handleListScroll);
      }
    };
  }, [listRef]);

  const scrollToTop = (smooth = true) => {
    if (listRef.current) {
      if (smooth) {
        listRef.current.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        // Instant scroll without animation - just replace the view
        listRef.current.scrollTop = 0;
      }
    }
  };

  const fetchSources = (filter, isRefresh = false) => {
    dispatch(
      getAllSourcesApi({
        data: {
          collection_name: "test_page_info",
          limit: 5,
          filter: filter ? [filter] : [{}],
          // Don't include offset when refreshing to start from beginning
          ...(isRefresh ? {} : {}),
        },
        finalCallback: () => {},
        successCallback: (response) => {
          setDefaultSources(response?.response);
          setSubSources(response?.response);

          // Reset pagination state properly for refresh
          const newHasMore = response?.has_more ?? (!!response?.next_offset);
          effectiveSetHasMore(newHasMore);

          // Reset offset value for fresh start
          if (response?.next_offset) {
            setOffsetValue(response?.next_offset);
          } else {
            setOffsetValue(null);
          }

          if (setCurrentItemCount) {
            setCurrentItemCount(response?.response?.length || 0);
          }
        },
        failureCallback: (errors) => {
          console.error("Failed to fetch initial sources:", errors);
        },
      })
    );
  };

  const fetchSourcesViaOffsetValue = (offsetValue, filtersTOC) => {
    // Guard against duplicate calls while loading
    if (isLoading) return;

    console.log("Fetching more sources with offset:", offsetValue);
    setIsLoading(true);

    dispatch(
      getAllSourcesApi({
        data: {
          collection_name: "test_page_info",
          limit: 5,
          filter: filtersTOC ? [filtersTOC] : [{}],
          offset: offsetValue,
        },
        finalCallback: () => {
          // Always reset loading state regardless of success/failure
          setIsLoading(false);
        },
        successCallback: (response) => {
          console.log("Received response:", response);

          // More explicit check for no more data
          const newHasMore = response?.has_more ?? (!!response?.next_offset && response?.response?.length > 0);

          if (!newHasMore) {
            console.log("No more data to load, setting hasMore to false");
            effectiveSetHasMore(false);
          } else {
            console.log("Setting next offset:", response.next_offset);
            setOffsetValue(response.next_offset);
          }

          // Only append new sources if we actually received some
          if (response?.response && response.response.length > 0) {
            setSubSources((prev) => {
              // Create a set of existing IDs for fast lookup
              const existingIds = new Set(prev.map((item) => item.id_));

              // Filter the response to only include items with new IDs
              const newItems = response.response.filter(
                (item) => !existingIds.has(item.id_)
              );

              console.log(`Adding ${newItems.length} new items`);

              const newList = [...prev, ...newItems];

              // Update current item count if function is available
              if (setCurrentItemCount) {
                setCurrentItemCount(newList.length);
              }

              // Return the combined array
              return newList;
            });
          } else {
            console.log("No new items received, setting hasMore to false");
            effectiveSetHasMore(false);
          }
        },
        failureCallback: (errors) => {
          console.error("Failed to fetch more sources:", errors);
        },
      })
    );
  };

  useEffect(() => {
    let scrollTimeout;

    const handleScroll = () => {
      // Skip if we're in subsources view
      if (isSubsourcesView) {
        console.log("Skipping scroll handler in subsources view");
        return;
      }

      // Skip if reference is not attached
      if (!listRef.current) {
        console.log("List ref not attached");
        return;
      }

      // Skip if we already know there's no more data
      if (!effectiveHasMore) {
        console.log("No more data to load");
        return;
      }

      // Skip if we're already loading
      if (isLoading) {
        console.log("Already loading data");
        return;
      }

      const listEl = listRef.current;

      // Check if we're near the bottom of the scroll (within 50px)
      if (listEl.scrollHeight - listEl.scrollTop <= listEl.clientHeight + 50) {
        // Clear any existing timeout to prevent multiple executions
        clearTimeout(scrollTimeout);

        // Set a small timeout to debounce the scroll event
        scrollTimeout = setTimeout(() => {
          console.log("Near bottom, loading more data. Offset:", offsetValue);
          fetchSourcesViaOffsetValue(offsetValue, filtersTOC);
        }, 200);
      }
    };

    // Attach scroll listener
    const listEl = listRef.current;
    if (listEl) {
      console.log("Attaching scroll listener to list element");
      listEl.addEventListener("scroll", handleScroll);
    }

    return () => {
      // Clean up the event listener
      if (listRef.current) {
        listRef.current.removeEventListener("scroll", handleScroll);
      }
      clearTimeout(scrollTimeout);
    };
  }, [effectiveHasMore, isLoading, isSubsourcesView, offsetValue, filtersTOC]);

  const fetchTOC = async () => {
    try {
      const fetchedTOC = await fetch_toc();
      setToc(fetchedTOC);
    } catch (error) {
      console.error("Error fetching TOC:", error);
    }
  };

  return (
    <Card
      className="sources-panel"
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            {isSubsourcesView && (
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={onBackToDefault}
                type="text"
              />
            )}
            <Title level={4} style={{ margin: 0 }}>
              {"Source Map"}
            </Title>
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              type="primary"
              onClick={() => setFileManagerVisible(true)}
              icon={<FolderOutlined style={{ fontSize: "1.3em" }} />}
              style={{
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              Manager
            </Button>
            <Button
              type="primary"
              onClick={() => setDrawerVisible(true)}
              icon={<UnorderedListOutlined style={{ fontSize: "1.3em" }} />}
              style={{
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              Knowledge Map
            </Button>
          </div>
        </div>
      }
      style={{
        height: "100%",
        borderRadius: "12px",
        overflow: "hidden",
      }}
      bodyStyle={{
        height: "calc(100% - 60px)",
        padding: "20px",
      }}
    >
      <div
        ref={listRef}
        style={{
          height: "100%",
          overflowY: "auto",
          position: "relative",
          marginBottom: "16px",
        }}
        onScroll={() => {
          if (listRef.current) {
            setShowScrollTop(listRef.current.scrollTop > 100);
          }
        }}
      >
        <List
          dataSource={sources}
          renderItem={(item) => (
            <SourceCard
              key={item.id_}
              source={item}
              setHighlight={setHighlight}
              highlight={highlight}
              fetchDefaultSources={fetchDefaultSources}
              // updateSourceInList={updateSourceInList} // No longer needed
              tocNode={tocNode}
              fetchSources={fetchSources}
              fetchTOC={fetchTOC}
              scrollToTop={scrollToTop}
            />
          )}
          style={{ paddingRight: "8px", position: "relative" }}
        />
        {isListScrollable && isLoading && (
          <div style={{ textAlign: "center", padding: "12px" }}>
            <Spin indicator={<LoadingOutlined spin />} />
          </div>
        )}
        {isListScrollable && !effectiveHasMore && !isLoading && (
          <div style={{ textAlign: "center", color: "#888", padding: "12px" }}>
            No more sources to show
          </div>
        )}
        {showScrollTop && isListScrollable && (
          <Button
            type="primary"
            shape="circle"
            icon={<span style={{ fontSize: 18 }}>↑</span>}
            onClick={scrollToTop}
            style={{
              position: "fixed",
              right: 80,
              // Dynamically calculate bottom based on List's bounding rect
              bottom: (() => {
                if (listRef.current) {
                  const rect = listRef.current.getBoundingClientRect();
                  const windowHeight = window.innerHeight;
                  // If the List is shorter than the viewport, stick to 20px above List's bottom
                  if (rect.bottom < windowHeight) {
                    return windowHeight - rect.bottom + 20;
                  }
                }
                // Default: 20px above viewport bottom
                return 20;
              })(),
              zIndex: 1000,
            }}
            className="back-to-top-button"
          />
        )}
      </div>
      <TOCDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        toc={toc}
        setSourceFilter={setSourceFilter}
        setDefaultSources={setDefaultSources}
        setSubSources={setSubSources}
        setTocNode={setTocNode}
        fetchSources={fetchSources}
        setFiltersTOC={setFiltersTOC}
      />
      <FileManagerDrawercomponent
        visible={fileManager}
        fetchSources={fetchSources}
        fetchTOC={fetchTOC}
        onClose={() => setFileManagerVisible(false)}
      />
    </Card>
  );
}
