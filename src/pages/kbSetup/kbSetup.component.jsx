import React, { useState, useEffect } from "react";
import ChatPanel from "./section/chatPanel/chatPanel.component";
import SourcesPanel from "./section/SourcesPanel/SourcesPanel.component";
import { message } from "antd";
import { fetch_docs } from "./hook/fetchdata";

const KbSetupComponent = () => {
  const [subsources, setSubSources] = useState([]);
  const [selectedAssistantIndex, setSelectedAssistantIndex] = useState(null);
  const [defaultSources, setDefaultSources] = useState([]);
  const [sourceFilter, setSourceFilter] = useState(null);
  const [filtersTOC, setFiltersTOC] = useState(null);
  const [offsetValue, setOffsetValue] = useState(null);
  const [sourcesLoading, setSourcesLoading] = useState(false);
  const [highlight, setHighlight] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentItemCount, setCurrentItemCount] = useState(0);
  useEffect(() => {
    if (!subsources.sentence || subsources.sentence.length === 0) {
      setHighlight(false);
    }
  }, [subsources.sentence]);

  useEffect(() => {
    fetchDefaultSources();
  }, []);

  useEffect(() => {
    if (sourceFilter) {
      fetchDefaultSources(offsetValue, sourceFilter, false);
    }
  }, [sourceFilter, offsetValue]);

  const fetchDefaultSources = async (offsetValue, sourceFilter, isRefresh = false) => {
    setSourcesLoading(true);
    try {
      // If this is a refresh (after edit/delete), reset pagination state
      const actualOffsetValue = isRefresh ? null : offsetValue;

      const response = await fetch_docs(actualOffsetValue, sourceFilter);
      const newSources = response?.response ?? [];
      setDefaultSources(newSources);
      setSubSources(newSources);
      setOffsetValue(response?.next_offset);
      setHasMore(response?.has_more ?? (response?.next_offset !== null));
      setCurrentItemCount(newSources.length);
    } catch (error) {
      console.error("Error fetching default sources:", error);
      message.error("Failed to load default sources");
    } finally {
      setSourcesLoading(false);
    }
  };

  // New function to update a specific item without refetching everything
  const updateSourceInList = (updatedSource) => {
    setDefaultSources(prevSources =>
      prevSources.map(source =>
        source.id_ === updatedSource.id_ ? updatedSource : source
      )
    );
    setSubSources(prevSources =>
      prevSources.map(source =>
        source.id_ === updatedSource.id_ ? updatedSource : source
      )
    );
  };

  const handleAssistantMessageClick = (index, sources) => {
    if (selectedAssistantIndex === index) {
      setSelectedAssistantIndex(null);
      setSubSources(defaultSources);
      setHighlight((prev) => !prev);
    } else {
      setSelectedAssistantIndex(index);
      setSubSources(sources);
      setHighlight(true);
    }
  };

  const handleClearChat = () => {
    setSelectedAssistantIndex(null);
    setSubSources(defaultSources);
    message.success("Facts Cleared");
  };

  return (
    <div
      style={{
        display: "flex",
        gap: "20px",
        height: "calc(100% - 30px)", // Account for Content wrapper padding (10px top + 10px bottom) + 10px additional bottom margin
        padding: "0", // Remove any default padding
        margin: "0 0 10px 0", // Add 10px bottom margin
        overflow: "hidden", // Prevent main container from scrolling
        flex: 1, // Take all available space in flex container
      }}
    >
      {/* Chat Panel - Fixed width, full height */}
      <div
        style={{
          flex: "0 0 35%",
          height: "100%", // Take full available height
          display: "flex",
          flexDirection: "column",
        }}
      >
        <ChatPanel
          onClearChat={handleClearChat}
          onAssistantMessageClick={handleAssistantMessageClick}
          selectedAssistantIndex={selectedAssistantIndex}
          setHighlight={setHighlight}
        />
      </div>

      {/* Sources Panel - Flexible width, full height */}
      <div
        style={{
          flex: 1,
          height: "100%", // Take full available height
          display: "flex",
          flexDirection: "column",
          minWidth: 0, // Allow flex item to shrink below its content size
        }}
      >
        <SourcesPanel
          setHighlight={setHighlight}
          highlight={highlight}
          sourcesLoading={sourcesLoading}
          fetchDefaultSources={fetchDefaultSources}
          updateSourceInList={updateSourceInList}
          sources={subsources}
          onBackToDefault={() => {
            setSubSources(defaultSources);
            setSelectedAssistantIndex(null);
          }}
          isSubsourcesView={selectedAssistantIndex !== null}
          setSourceFilter={setSourceFilter}
          defaultSources={defaultSources}
          setDefaultSources={setDefaultSources}
          offsetValue={offsetValue}
          sourceFilter={sourceFilter}
          setSubSources={setSubSources}
          setOffsetValue={setOffsetValue}
          filtersTOC={filtersTOC}
          setFiltersTOC={setFiltersTOC}
          hasMore={hasMore}
          setHasMore={setHasMore}
          currentItemCount={currentItemCount}
          setCurrentItemCount={setCurrentItemCount}
        />
      </div>
    </div>
  );
};

export default KbSetupComponent;
