import {
  But<PERSON>,
  Card,
  Col,
  Form,
  Input,
  Layout,
  message,
  Row,
  Space,
  Spin,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import logoIcon from "../../assets/images/eko-favicon.png";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { userRegistrationApi } from "../../services/users.service";
import { decodeJwt } from "../../utils/jwtDecoder";

const UserRegistrationComponent = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { Title, Text } = Typography;
  const { Content } = Layout;

  const [form] = Form.useForm();
  const location = useLocation();

  // Extract token and username from the URL
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token");
  const username = queryParams.get("username");
  const role = queryParams.get("role");

  const onFinish = (values) => {
    setLoading(true);
    const { username, role, password } = values;

    const newObj = {
      username: username,
      role: role,
      password: password,
      token: token,
    };

    dispatch(
      userRegistrationApi({
        data: newObj,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response.success) {
            message.success(response?.msg);

            const currentUrl = new URL(window.location.href);
            const protocol = currentUrl.protocol;
            const hostWithPort = currentUrl.host;

            const jwtPayload = decodeJwt(token);

            window.location.href = `${protocol}//${hostWithPort}/${jwtPayload?.slug}/login`;
          }
        },
        failureCallback: (error) => {},
      })
    );
  };

  return (
    <div style={styles.container}>
      <Spin spinning={loading} tip="Registering..." size="large">
        <Card style={styles.card} bordered={false}>
          <div style={styles.cardContent}>
            {/* Header Section */}
            <div style={styles.header}>
              <img src={logoIcon} alt="Company Logo" style={styles.logo} />
              <Title level={2} style={styles.title}>
                Complete Registration
              </Title>
              <Text style={styles.subtitle}>
                Set up your account to get started
              </Text>
            </div>

            {token && username && role ? (
              /* Registration form */
              <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                initialValues={{ username, token, role }}
                style={styles.form}
              >
                <Form.Item
                  name="username"
                  label={<span style={styles.label}>Username</span>}
                  style={styles.formItem}
                >
                  <Input
                    size="large"
                    disabled
                    prefix={<UserOutlined style={styles.inputIcon} />}
                    style={styles.input}
                  />
                </Form.Item>

                <Form.Item
                  name="role"
                  label={<span style={styles.label}>Role</span>}
                  style={styles.formItem}
                >
                  <Input
                    size="large"
                    disabled
                    prefix={<UserOutlined style={styles.inputIcon} />}
                    style={styles.input}
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  label={<span style={styles.label}>Password</span>}
                  rules={[
                    { required: true, message: "Please input your password!" },
                  ]}
                  style={styles.formItem}
                >
                  <Input.Password
                    size="large"
                    placeholder="Enter your password"
                    prefix={<LockOutlined style={styles.inputIcon} />}
                    style={styles.input}
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  label={<span style={styles.label}>Confirm Password</span>}
                  dependencies={["password"]}
                  rules={[
                    {
                      required: true,
                      message: "Please confirm your password!",
                    },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue("password") === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          new Error("Passwords do not match!")
                        );
                      },
                    }),
                  ]}
                  style={styles.formItem}
                >
                  <Input.Password
                    size="large"
                    placeholder="Confirm your password"
                    prefix={<LockOutlined style={styles.inputIcon} />}
                    style={styles.input}
                  />
                </Form.Item>

                <Form.Item style={styles.submitFormItem}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    size="large"
                    style={styles.button}
                  >
                    {loading ? "Registering..." : "Complete Registration"}
                  </Button>
                </Form.Item>
              </Form>
            ) : (
              <div style={styles.errorContainer}>
                <ExclamationCircleOutlined style={styles.errorIcon} />
                <Title level={3} style={styles.errorTitle}>
                  Invalid Invitation Link
                </Title>
                <Text style={styles.errorText}>
                  This invitation link is invalid or has expired. Please contact your administrator for a new invitation.
                </Text>
              </div>
            )}
          </div>
        </Card>
      </Spin>
    </div>
  );
};

// Styling for the page - matching login page design
const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
    padding: "20px",
  },
  card: {
    width: "100%",
    maxWidth: 750,
    minWidth: 500,
    padding: 0,
    boxShadow: "0 20px 40px rgba(0, 0, 0, 0.15)",
    borderRadius: "16px",
    backgroundColor: "#fff",
    overflow: "hidden",
  },
  cardContent: {
    padding: "48px 40px",
  },
  header: {
    textAlign: "center",
    marginBottom: "40px",
  },
  logo: {
    display: "block",
    margin: "0 auto 24px auto",
    height: 80,
    width: "auto",
  },
  title: {
    margin: "0 0 8px 0",
    color: "#1a1a1a",
    fontWeight: "600",
    fontSize: "28px",
  },
  subtitle: {
    color: "#666",
    fontSize: "16px",
    margin: 0,
  },
  form: {
    width: "100%",
  },
  formItem: {
    marginBottom: "24px",
  },
  submitFormItem: {
    marginBottom: 0,
    marginTop: "32px",
  },
  label: {
    fontWeight: "600",
    color: "#333",
    fontSize: "14px",
  },
  input: {
    borderRadius: "8px",
    border: "1px solid #d9d9d9",
    fontSize: "16px",
    transition: "all 0.3s ease",
    height: "48px",
  },
  inputIcon: {
    color: "#999",
  },
  button: {
    backgroundColor: "#1890ff",
    borderColor: "#1890ff",
    fontWeight: "600",
    fontSize: "16px",
    height: "48px",
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(24, 144, 255, 0.3)",
    transition: "all 0.3s ease",
  },
  errorContainer: {
    textAlign: "center",
    padding: "40px 20px",
  },
  errorIcon: {
    color: "#ff4d4f",
    fontSize: "48px",
    marginBottom: "16px",
  },
  errorTitle: {
    margin: "0 0 16px 0",
    color: "#1a1a1a",
    fontWeight: "600",
  },
  errorText: {
    color: "#666",
    fontSize: "16px",
    lineHeight: "1.5",
  },
};

export default UserRegistrationComponent;
