import { useState } from "react";
import { Modal, Form, Input, Button, Typography } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { getTenantInfoApi } from "../../services/tenant.service";

const { Text } = Typography;

const TenantIdDialog = ({ visible, onClose }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [errorMessage, setErrorMessage] = useState("");

  const handleTenantValidation = (newSlug) => {
    setLoading(true);
    setErrorMessage(""); // Clear any previous errors

    dispatch(
      getTenantInfoApi({
        params: {
          slug: newSlug,
        },
        finalCallback: () => setLoading(false),
        successCallback: (data) => {
          if (data?.tenant_id) {
            localStorage.setItem("slug", newSlug);
            form.resetFields();
            setErrorMessage("");

            // Close dialog first
            onClose();

            // Navigate to the new tenant URL
            navigate(`/${newSlug}/login`, { replace: true });
          } else {
            setErrorMessage("Tenant information not found. Please contact support.");
          }
        },
        failureCallback: (error) => {
          const status = error?.response?.status;
          console.log("Error validating tenant:", error, status);

          if (status === 404) {
            setErrorMessage("Tenant not found. Please check the tenant ID and try again.");
          } else {
            setErrorMessage("Failed to validate tenant. Please try again later.");
          }
          console.error("Error validating tenant:", error.status);
        },
      })
    );
  };

  const handleOk = () => {
    form.validateFields()
      .then((values) => {
        const newSlug = values.tenantId?.trim();
        if (newSlug) {
          handleTenantValidation(newSlug);
        }
      })
      .catch(() => {
        // Form validation failed, clear any API error messages
        // so built-in validation messages can show
        setErrorMessage("");
      });
  };

  const handleInputChange = () => {
    // Clear error message when user starts typing
    if (errorMessage) {
      setErrorMessage("");
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleOk();
    }
  };

  return (
    <Modal
      title={
        <div style={{ textAlign: "center" }}>
          <ExclamationCircleOutlined
            style={{ color: "#faad14", marginRight: 8, fontSize: 20, fontWeight: 600 }}
          />
          <span>Enter Tenant ID</span>
        </div>
      }
      open={visible}
      onOk={handleOk}
      confirmLoading={loading}
      okText="Validate"
      centered
      width={450}
      maskClosable={false}
      closable={false}
      footer={[
        <Button
          key="validate"
          type="primary"
          loading={loading}
          onClick={handleOk}
          size="large"
          style={{
            borderRadius: 8,
            fontWeight: 600,
            height: 40,
          }}
          icon={<iconify-icon icon="mingcute:right-line" width="24" height="24"></iconify-icon>}
        >
          Continue
        </Button>,
      ]}
    >
      <div style={{ padding: "20px 0 0 0" }}>
        <Text style={{ display: "block", marginBottom: 16, color: "#666" }}>
          Please enter your organization tenant ID to continue.
        </Text>
        <div
          style={{
            backgroundColor: "#f6f8fa",
            border: "1px solid #e1e4e8",
            borderRadius: 8,
            padding: "12px 16px",
            marginBottom: 12,
          }}
        >
          <Text style={{ color: "#586069", fontSize: 14 }}>
            <iconify-icon icon="noto:light-bulb" width="16" height="16"></iconify-icon> <strong>Don't know your tenant ID?</strong> Contact your system administrator or IT support team for assistance.
          </Text>
        </div>
        <Form form={form} layout="vertical">
          <Form.Item
            name="tenantId"
            label={<span style={{ fontWeight: 600 }}>Tenant ID</span>}
            rules={[
              { required: true, message: "Please enter a tenant ID!" },
            ]}
            validateStatus={errorMessage ? "error" : undefined}
            help={errorMessage || undefined}
          >
            <Input
              placeholder="Enter your tenant ID"
              size="large"
              style={{ borderRadius: 8 }}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              allowClear
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default TenantIdDialog;
