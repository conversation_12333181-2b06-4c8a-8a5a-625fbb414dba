import { useEffect, useState } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Spin,
  Typography,
} from "antd";
import {
  UserOutlined,
  LockOutlined,
} from "@ant-design/icons";
import logoIcon from "../../assets/images/eko-favicon.png";
import { useLocation } from "react-router-dom";
import { getTenantInfoApi } from "../../services/tenant.service";
import { useAppDispatch } from "../../hooks/reduxHooks";
import TenantIdDialog from "./TenantIdDialog";

const { Title, Text } = Typography;

const LoginComponent = (props) => {
  const { handleLogin, submitting, slug } = props;
  const location = useLocation();
  const dispatch = useAppDispatch();

  const [initialLoading, setinitialLoading] = useState(true);
  const [tenantDialogVisible, setTenantDialogVisible] = useState(false);

  const onFinish = (val) => {
    handleLogin(val);
  };

  const handleTenantDialogClose = () => {
    setTenantDialogVisible(false);
  };

  useEffect(() => {
    const { source } = location.state || {};
    if (source === "expired") {
      message.warning("Session expired. Please log in again.");
    }
  }, []);

  useEffect(() => {
    if (!slug) {
      setinitialLoading(false);
      setTenantDialogVisible(true);
      return;
    }

    (async () => {
      dispatch(
        getTenantInfoApi({
          params: {
            slug: slug,
          },
          finalCallback: () => setinitialLoading(false),
          successCallback: (data) => {
            if (!data?.tenant_id) {
              message.error(
                "Tenant information not found. Please contact support."
              );
              setTenantDialogVisible(true);
            }
          },
          failureCallback: (error) => {
            const status = error?.response?.status;
            console.error("Error fetching tenant info:", error);

            if (status === 404) {
              // message.error(
              //   "Tenant not found. Please enter a valid tenant ID."
              // );
              setTenantDialogVisible(true);
            } else {
              message.error(
                "Failed to fetch tenant information. Please try again later."
              );
              setTenantDialogVisible(true);
            }
          },
        })
      );
    })();
  }, [slug, dispatch]);

  return (
    <div style={styles.container}>
      <Spin spinning={initialLoading} tip="Logging in..." size="large">
        <Card style={styles.card} bordered={false}>
          <div style={styles.cardContent}>
            {/* Header Section */}
            <div style={styles.header}>
              <img src={logoIcon} alt="Company Logo" style={styles.logo} />
              <Title level={2} style={styles.title}>
                Welcome Back
              </Title>
              <Text style={styles.subtitle}>
                Please sign in to your account
              </Text>
            </div>

            {/* Login form */}
            <Form
              name="login_form"
              layout="vertical"
              onFinish={onFinish}
              style={styles.form}
            >
              <Form.Item
                name="username"
                label={<span style={styles.label}>Username</span>}
                rules={[
                  { required: true, message: "Please input your username!" },
                ]}
                style={styles.formItem}
              >
                <Input
                  size="large"
                  placeholder="Enter your username"
                  prefix={<UserOutlined style={styles.inputIcon} />}
                  style={styles.input}
                />
              </Form.Item>

              <Form.Item
                name="password"
                label={<span style={styles.label}>Password</span>}
                rules={[
                  { required: true, message: "Please input your password!" },
                ]}
                style={styles.formItem}
              >
                <Input.Password
                  size="large"
                  placeholder="Enter your password"
                  prefix={<LockOutlined style={styles.inputIcon} />}
                  style={styles.input}
                />
              </Form.Item>

              <Form.Item style={styles.submitFormItem}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  block
                  size="large"
                  style={styles.button}
                >
                  Sign In
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Card>
      </Spin>

      {/* Tenant ID Dialog */}
      <TenantIdDialog
        visible={tenantDialogVisible}
        onClose={handleTenantDialogClose}
      />
    </div>
  );
};

// Styling for the page
const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
    padding: "2-0px",
  },
  card: {
    width: "100%",
    maxWidth: 750,
    minWidth: 500,
    padding: 0,
    boxShadow: "0 20px 40px rgba(0, 0, 0, 0.15)",
    borderRadius: "16px",
    backgroundColor: "#fff",
    overflow: "hidden",
  },
  cardContent: {
    padding: "48px 40px",
  },
  header: {
    textAlign: "center",
    marginBottom: "40px",
  },
  logo: {
    display: "block",
    margin: "0 auto 24px auto",
    height: 80,
    width: "auto",
  },
  title: {
    margin: "0 0 8px 0",
    color: "#1a1a1a",
    fontWeight: "600",
    fontSize: "28px",
  },
  subtitle: {
    color: "#666",
    fontSize: "16px",
    margin: 0,
  },
  form: {
    width: "100%",
  },
  formItem: {
    marginBottom: "24px",
  },
  submitFormItem: {
    marginBottom: 0,
    marginTop: "32px",
  },
  label: {
    fontWeight: "600",
    color: "#333",
    fontSize: "14px",
  },
  input: {
    borderRadius: "8px",
    border: "1px solid #d9d9d9",
    fontSize: "16px",
    transition: "all 0.3s ease",
    height: "48px",
  },
  inputIcon: {
    color: "#999",
  },
  button: {
    backgroundColor: "#1890ff",
    borderColor: "#1890ff",
    fontWeight: "600",
    fontSize: "16px",
    height: "48px",
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(24, 144, 255, 0.3)",
    transition: "all 0.3s ease",
  },
};

export default LoginComponent;
