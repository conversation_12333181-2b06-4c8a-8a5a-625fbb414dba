import React, { useEffect, useState } from "react";
import { <PERSON>ton, Float<PERSON>utton, Layout, ConfigProvider } from "antd";
import { Navigate, useNavigate } from "react-router-dom";
import SideNav from "../components/SideNav/SideNav";
import AppHeader from "../components/Header/Header";
import { FolderOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../hooks/reduxHooks";
import { verifyTokenApi } from "../services/auth.service";
import { logout } from "../store/slices/profile.slice";
import { getTenantSetupCheckApi } from "../services/tenantSetup.service";

const ProtectedLayout = (props) => {
  console.log("Protected Layout Called", props);

  const { Content } = Layout;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [collapsed, setCollapsed] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isTenantSetupCompleted, setIsTenantSetupCompleted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const { children } = props;

  useEffect(() => {
    // Initialize authentication state
    const token = localStorage.getItem("authToken");
    setIsAuthenticated(!!token);

    // Initialize tenant setup state
    const storedStatus = localStorage.getItem("tenantSetupCompleted");
    if (storedStatus === "true") {
      setIsTenantSetupCompleted(true);
      setIsLoading(false);
    } else if (!!token) {
      // Only check tenant setup if user is authenticated
      dispatch(
        getTenantSetupCheckApi({
          finalCallback: () => {
            setIsLoading(false);
          },
          successCallback: () => {
            setIsTenantSetupCompleted(true);
            localStorage.setItem("tenantSetupCompleted", "true");
          },
          failureCallback: (error) => {
            setIsTenantSetupCompleted(false);
            localStorage.setItem("tenantSetupCompleted", "false");
          },
        })
      );
    } else {
      setIsLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    // Only verify token if user is authenticated
    if (isAuthenticated) {
      dispatch(
        verifyTokenApi({
          finalCallback: () => {},
          successCallback: () => {},
          failureCallback: (error) => {
            // Token is invalid, clear auth state
            localStorage.removeItem("authToken");
            localStorage.removeItem("userId");
            localStorage.removeItem("username");
            localStorage.removeItem("role");
            localStorage.removeItem("tenant_label");
            localStorage.removeItem("nav_permissions");
            localStorage.removeItem("tenantSetupCompleted");
            setIsAuthenticated(false);
          },
        })
      );
    }
  }, [dispatch, isAuthenticated]);

  // Show loading state while checking authentication and tenant setup
  if (isLoading) {
    // return <div>Loading...</div>;
    return null;
  }

  if (!isAuthenticated) {
    const slug = localStorage.getItem("slug");
    return <Navigate to={`/${slug}/login`} replace />;
  }

  // Check if tenant setup is completed for authenticated users
  if (isAuthenticated && !isTenantSetupCompleted) {
    return <Navigate to="/tenant-setup" replace />;
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          motion: false,
        },
      }}
    >
      <Layout
        className="protected-layout"
        id="app-layout"
        // style={{ border: "solid 1px red" }}
      >
        {/* <ProtectedHeader /> */}
        <Layout className="protected-layout__content">
          <SideNav collapsed={collapsed} setCollapsed={setCollapsed} />
          {/* <Layout style={{ marginLeft: collapsedWidth - 5 }}> */}
          <Layout style={{ marginLeft: "0" }}>
            <AppHeader />
            <Content
              style={{
                padding: "10px 20px",
                height: "calc(100vh - 50px)", // Account for header height (50px)
                overflow: "auto", // Allow Content wrapper to scroll
                display: "flex", // Make Content a flex container
                flexDirection: "column", // Stack children vertically
              }}
            >
              {children}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default ProtectedLayout;
