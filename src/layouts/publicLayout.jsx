import React from "react";
import { ConfigProvider, Layout } from "antd";

const PublicLayout = (props) => {
  const { children } = props;
  const slug = localStorage.getItem("slug");

  return (
      <ConfigProvider
        theme={{
          token: {
            motion: false,
          },
        }}
      >
      <Layout className="public-layout" id="app-layout">
        {children}
      </Layout>
    </ConfigProvider>
  );
};

export default PublicLayout;
