import httpBase from "../utils/http.utils";
import {
  GET_EMAIL_CONFIG,
  UPDATE_EMAIL_STATUS,
  ADD_EMAIL_RECIPIENT,
  UPDATE_EMAIL_RECIPIENT_CTA_STATUS,
  UPDATE_EMAIL_RECIPIENT_STATUS,
  DELETE_EMAIL_RECIPIENT,
} from "../constants/url";

export const getEmailConfigApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_EMAIL_CONFIG, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateEmailStatusApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_EMAIL_STATUS, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
}

export const addEmailRecipientApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(ADD_EMAIL_RECIPIENT, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateEmailRecipientCtaStatusApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  const email_id = params.id;
  delete params.id;

  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_EMAIL_RECIPIENT_CTA_STATUS.replace("{{email_id}}", email_id), params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateEmailRecipientStatusApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  const email_id = params.id;
  delete params.id;

  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_EMAIL_RECIPIENT_STATUS.replace("{{email_id}}", email_id), params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
}

export const deleteEmailRecipientApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().delete(DELETE_EMAIL_RECIPIENT.replace("{{email_id}}", params.id));
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};