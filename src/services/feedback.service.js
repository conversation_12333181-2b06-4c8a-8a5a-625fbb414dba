import { EVALUATE_FILTERS, FEEDBACK, FLAG_EVALUATIONS } from "../constants/url";
import httpBase, { handleApiExceptions } from "../utils/http.utils";

export const getAllFiltersApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(EVALUATE_FILTERS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchFeedbackAPI = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(FEEDBACK, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const flagTableRowApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  console.log("DATA -> ", data);
  return async (dispatch) => {
    try {
      const response = await httpBase().post(FLAG_EVALUATIONS, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
