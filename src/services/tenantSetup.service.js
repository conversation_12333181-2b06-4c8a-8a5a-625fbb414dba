import httpBase from "../utils/http.utils";
import {
  TENANT_EDIT_INFO,
  TENANT_GET_INFO,
  TENANT_LANGUAGE_LIST,
  TENANT_ORG_TYPE,
  TENANT_SETUP_CHECK,
  TENANT_STAGE_1,
  TENANT_STAGE_2,
  TENANT_TOOLS,
  TENANT_DUMMY_DATA
} from "../constants/url";

export const getTenantOrgTypesApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().get(TENANT_ORG_TYPE);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getTenantLanguageListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().post(TENANT_LANGUAGE_LIST);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getTenantToolsApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().get(TENANT_TOOLS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateTenantStageOneApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().post(TENANT_STAGE_1, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateTenantStageTwoApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().post(TENANT_STAGE_2, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getTenantSetupCheckApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().get(TENANT_SETUP_CHECK);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getBusinessInfoApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().get(TENANT_GET_INFO);
      successCallback(response?.data?.result);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const editBusinessInfoApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().post(TENANT_EDIT_INFO, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const addDummyDataApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async () => {
    try {
      const response = await httpBase().post(`${TENANT_DUMMY_DATA}?org_type=${params.org_type}`, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
}