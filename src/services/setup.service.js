import {
  SETUP_CATEGORY_TYPE,
  SUPPORT_SAVE,
  BUSINESS_TYPE,
  SALES_SETUP,
  BUSINESS_SETUP,
  GET_BUSINESS_SETUP,
} from "../constants/url";
import httpBase from "../utils/http.utils";

export const getCategoryTypeApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(SETUP_CATEGORY_TYPE);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const uploadFileApi = ({
  url,
  formData,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(url, formData);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const supportSaveApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(SUPPORT_SAVE, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const businessTypeOptionApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(BUSINESS_TYPE);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const salesSetupApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(SALES_SETUP, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const businessSetupApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(BUSINESS_SETUP, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getBusinessSetupApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_BUSINESS_SETUP);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

// export const salesPreSetupApi = ({
//   finalCallback,
//   successCallback,
//   failureCallback,
// }) => {
//   return async (dispatch) => {
//     try {
//       const response = await httpBase().get(CATEGORY_TYPE);
//       successCallback(response?.data);
//     } catch (e) {
//       failureCallback(e);
//     } finally {
//       finalCallback();
//     }
//   };
// };
