import httpBase from "../utils/http.utils";
import { FETCH_PROMPTS, UPDATE_PROMPT } from "../constants/url";

export const fetch_prompt_api = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(FETCH_PROMPTS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};


export const update_prompt_api = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_PROMPT+"/"+data.name, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
