import { GET_TENANT_INFO } from "../constants/url";
import httpBase from "../utils/http.utils";

export const getTenantInfoApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_TENANT_INFO, {
        params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
