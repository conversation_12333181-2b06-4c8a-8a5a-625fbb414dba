import {
  TOPIC_CRUD,
  TOPIC_BATCH
} from "../constants/url";
import httpBase from "../utils/http.utils";

export const getTopicsApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(TOPIC_CRUD, {
        params: params,
      });
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  };
}

export const updateTopicApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(TOPIC_CRUD, params);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  };
}

export const deleteTopicApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().delete(TOPIC_CRUD, {
        params: params,
      });
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  };
}

export const createTopicApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(TOPIC_CRUD, params);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  };
}

export const bulkCreateTopicApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(TOPIC_BATCH, params);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  };
}