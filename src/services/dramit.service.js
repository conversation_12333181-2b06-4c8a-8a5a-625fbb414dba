import { message } from "antd";
import { FINAL, ADD_DOC, UPDATE_DOC, DELETE_DOC } from "../constants/url";
import httpBase from "../utils/http.utils";

import { redirect } from "react-router-dom";
export const handleSentAPI = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(FINAL, data);
      successCallback(response.data?.response || "Ai response");
    } catch (e) {
      if (e.response?.status === 401) {
        message.warning("Session expired. Please log in again.");
        localStorage.removeItem("authToken");
        localStorage.removeItem("userId");
        localStorage.removeItem("username");
        localStorage.removeItem("role");
        localStorage.removeItem("tenant_label");
        localStorage.removeItem("nav_permissions");
        localStorage.removeItem("tenantSetupCompleted");
        const slug = localStorage.getItem("slug");
        // window.location.href = "/login";
        redirect(`/${slug}/login`);

        return;
      }
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

// need review not done
export const addDocAPI = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(ADD_DOC, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const handleUpdateAPI = ({
  data,
  successCallback,
  finalCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_DOC, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const handleDeleteAPI = ({
  data,
  successCallback,
  finalCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().delete(DELETE_DOC, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
