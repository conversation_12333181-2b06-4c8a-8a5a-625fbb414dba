import { ALL_CATEGORIES, CATEGORIES } from "../constants/url";
import httpBase from "../utils/http.utils";

export const getCategoriesListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(ALL_CATEGORIES);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const deleteCategoryByIdApi = ({
  id,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().delete(`${CATEGORIES}/${id}`);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
