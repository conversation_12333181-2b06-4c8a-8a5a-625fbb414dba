import {
  GENERATE_REPLY,
  FETCH_CHAT_HISTORY,
  DELETE_CHAT_HISTORY,
} from "../constants/url";
import httpBase from "../utils/http.utils";

export const generateReplyApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(GENERATE_REPLY, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchChatHistory = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      // Send `user_id` as a query parameter
      const apidata = await httpBase().post(FETCH_CHAT_HISTORY, null, {
        params: params,
      });

      const response = [...apidata?.data?.data].reverse();

      successCallback({
        responseData: response,
        responseMeta: apidata?.data?.meta,
      });
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const chatDeleteAPI = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(DELETE_CHAT_HISTORY, null, {
        params: { user_id: params.user_id },
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
