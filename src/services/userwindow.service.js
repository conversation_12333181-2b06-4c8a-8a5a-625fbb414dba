import { USER_WINDOW } from "../constants/url";
import httpBase from "../utils/http.utils";

export const getUserWindowApi = ({
  userId,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const url = USER_WINDOW.replace(":id", userId);
      const response = await httpBase().get(url);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};