import httpBase from "../utils/http.utils";
import {
  CTA_ASSIGN_AGENT,
  CTA_BY_ID,
  CTA_FETCH,
  CTA_FILTERS,
  CTA_GET_AGENTS,
  CTA_GET_TOPICS,
  CTA_RESOLVE,
  GET_TOPIC,
} from "../constants/url";

export const getAllCtaDataApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(CTA_FETCH, [], {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getCtaFiltersApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CTA_FILTERS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getCtaTopicsApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CTA_GET_TOPICS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getTopicsApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_TOPIC, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const markCtaAsResolvedApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  const apiData = {
    cta_type: data.type,
    id: data.id,
    resolved_by: localStorage.getItem("username"),
    remarks: data.remarks,
  };

  return async (dispatch) => {
    try {
      const response = await httpBase().post(CTA_RESOLVE, apiData);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getAgentsApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(CTA_GET_AGENTS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getCtaByIdApi = ({
  id,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(`${CTA_BY_ID}/${id}`);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const assignAgentApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CTA_ASSIGN_AGENT, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};