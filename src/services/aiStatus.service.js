import httpBase from "../utils/http.utils";
import {
  GET_AI_STATUS,
  ENABLE_AI_REPLY,
  UPDATE_AI_SCHEDULE,
  GET_CHANNEL_STATUS,
} from "../constants/url";

export const getAIStatusApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_AI_STATUS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getChannelsStatusApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_CHANNEL_STATUS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const enableAIReplyApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(ENABLE_AI_REPLY, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateAIScheduleApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(UPDATE_AI_SCHEDULE, params);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
