import { INVITE_USER } from "../constants/url";
import httpBase, { handleApiExceptions } from "../utils/http.utils";

export const inviteUser = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(INVITE_USER, data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("authToken")}`,
        },
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};
