import httpBase, { handleApiExceptions } from "../utils/http.utils";
import {
  AGENT_REPLY,
  CHAT_DATA_BY_ID,
  CTA_TYPES,
  CUSTOMERS,
  CUSTOMERS_AI_RESPONSES,
  CUSTOMERS_GET_TAGS,
  GET_ALL_CATEGORIES,
  GET_ALL_PRODUCTS,
  LATEST_MESSAGES,
  SET_MESSAGE_VERIFICATION,
  SEND_AGENT_REPLY,
  GET_PRODUCTS,
  GET_LANGUAGES,
  GET_SENTIMENTS,
  RESOLVE_CTA_TYPE,
  GET_TOPICS,
  NEW_CUSTOMER,
  ALL_FILTERS,
} from "../constants/url";

export const getCustomersDataApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CUSTOMERS_AI_RESPONSES, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const sendCustomerMessageApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(NEW_CUSTOMER, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const getCustomerMessagesById = ({
  id,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(
        `${CUSTOMERS}/${id}/${LATEST_MESSAGES}`
      );
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const getCustomerMessagesByIds = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(CHAT_DATA_BY_ID, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

// sendAgentMessageApi

export const sendAgentMessageApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(AGENT_REPLY, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchCTAOptionsApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CTA_TYPES);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchTagsListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(CUSTOMERS_GET_TAGS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getFiltersApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(ALL_FILTERS, { params: params });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchProductsListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_TOPICS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchLanguagesListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_LANGUAGES);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchSentimentsListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_SENTIMENTS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchCategoryListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(GET_ALL_CATEGORIES);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const fetchProductListApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(GET_ALL_PRODUCTS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const messageVerificationApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(SET_MESSAGE_VERIFICATION, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const sendAssistantMessageApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(SEND_AGENT_REPLY, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const resolveCtaApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(RESOLVE_CTA_TYPE, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
