import {
  REPORTING_AVERAGE_PROCESSING_TIME,
  REPORTING_CHANNEL_MSG_COUNT,
  REPORTING_CTA_COUNT,
  REPORTING_CTA_TYPE_COUNT,
  REPORTING_EVALUATION_COUNT,
  REPORTING_MESSAGE_COUNT,
  REPORTING_UNIQUE_USERS_PER_DAY,
  REPORTING_LANGUAGE_COUNT,
  REPORTING_TOPIC_COUNT,
} from "../constants/url";
import httpBase from "../utils/http.utils";

export const getReportingAverageProcessingTimeApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_AVERAGE_PROCESSING_TIME, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingChannelMsgCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_CHANNEL_MSG_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingCtaCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_CTA_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingEvaluationCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_EVALUATION_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingMessageCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_MESSAGE_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingUniqueUsersPerDayApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_UNIQUE_USERS_PER_DAY, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingLanguageCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_LANGUAGE_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getReportingCtaTypeCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_CTA_TYPE_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
};

export const getTopicCountApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(REPORTING_TOPIC_COUNT, data);
      successCallback(response?.data);
    }
    catch (e) {
      failureCallback(e);
    }
    finally {
      finalCallback();
    }
  }
}