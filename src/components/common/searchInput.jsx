import React, { useState } from "react";
import { Input, Space } from "antd";
import { SearchOutlined } from "@ant-design/icons";

const SearchInput = ({
  setSearchKeywords,
  searchKeywords,
  placeholder,
  style,
}) => {
  // const [searchText, setSearchText] = useState("");

  // const onSearch = (value) => {
  //   setSearchKeywords(value);
  // };

  const handleInputChange = (e) => {
    const { value } = e.target;
    // setSearchText(value);
    setSearchKeywords(value);
    // onSearch(value); // Call onSearch function with each keystroke
  };

  return (
    <Input
      placeholder={placeholder ?? "Search..."}
      suffix={
        <SearchOutlined
          style={{
            marginRight: "5px",
            // color: "rgb(217, 217, 217)",
            fontSize: "16px",
          }}
        />
      }
      allowClear
      value={searchKeywords}
      onChange={handleInputChange}
      style={{
        border: "none",
        background: "#F8FAFB",
        // marginRight: "20px", If any button is needed
      }}
    />
  );
};

export default SearchInput;
