import React, { useState } from "react";
import {
  Button,
  Form,
  Input,
  Popconfirm,
  Table,
  Modal,
  Upload,
  message,
} from "antd";

const EditableCell = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  ...restProps
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    form.setFieldsValue({
      [dataIndex]: record[dataIndex],
    });
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setIsModalVisible(false);
      handleSave({
        ...record,
        ...values,
      });
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  let childNode = children;
  if (editable) {
    childNode = (
      <>
        <div
          className="editable-cell-value-wrap"
          style={{
            paddingInlineEnd: 24,
            minWidth: "100px",
            maxWidth: "320px",
            overflow: "hidden",
            display: "-webkit-box",
            WebkitBoxOrient: "vertical",
            WebkitLineClamp: 10,
            lineHeight: "1.5em",
            wordBreak: "break-word",
            cursor: "pointer",
          }}
          onClick={showModal}
        >
          {children}
        </div>
        <Modal
          title={`Edit ${title}`}
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Form form={form}>
            <Form.Item
              name={dataIndex}
              rules={[
                {
                  required: true,
                  message: `${title} is required.`,
                },
              ]}
            >
              <Input.TextArea
                autoSize={{ minRows: 3, maxRows: 15 }}
                placeholder={`Please enter ${title}`}
              />
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  }
  return <td {...restProps}>{childNode}</td>;
};

const EditableDataTable = ({ data: rowsDataApi, columns: columnsApiData }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [currentRecord, setCurrentRecord] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [tempImagePreview, setTempImagePreview] = useState(null);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [addForm] = Form.useForm();
  const [newRowImage, setNewRowImage] = useState(null);

  const handleImagePreview = (imageUrl, record) => {
    setPreviewImage(imageUrl);
    setCurrentRecord(record);
    setPreviewVisible(true);
    setTempImagePreview(null);
    setSelectedFile(null);
  };

  const handleUpload = async (file) => {
    try {
      const reader = new FileReader();

      reader.onload = () => {
        setSelectedFile(file);
        setTempImagePreview(reader.result);
      };

      reader.onerror = () => {
        message.error("Failed to read file");
      };

      reader.readAsDataURL(file);
      return false;
    } catch (error) {
      message.error("Failed to update image");
      return false;
    }
  };

  const handleNewRowUpload = async (file) => {
    try {
      const reader = new FileReader();
      reader.onload = () => {
        setNewRowImage(reader.result);
      };
      reader.readAsDataURL(file);
      return false;
    } catch (error) {
      message.error("Failed to upload image");
      return false;
    }
  };

  const handleSaveImage = () => {
    if (!selectedFile || !tempImagePreview) {
      message.warning("Please select a new image first");
      return;
    }

    const newData = [...dataSource];
    const index = newData.findIndex((item) => item.key === currentRecord.key);

    if (index > -1) {
      newData[index] = {
        ...newData[index],
        image: tempImagePreview,
      };
      setDataSource(newData);
      setPreviewImage(tempImagePreview);
      setSelectedFile(null);
      message.success("Image updated successfully");
      setPreviewVisible(false);
    }
  };

  const newColumns = columnsApiData.map((item) => {
    if (item === "image") {
      return {
        title: item,
        dataIndex: item,
        render: (text, record) => (
          <img
            src={record[item]}
            alt="Not Found!"
            style={{
              width: "50px",
              height: "50px",
              objectFit: "cover",
              cursor: "pointer",
            }}
            onClick={() => handleImagePreview(record[item], record)}
          />
        ),
      };
    }

    return {
      title: item,
      dataIndex: item,
      editable: true,
    };
  });

  const defaultColumns = [
    ...newColumns,
    {
      title: "operation",
      dataIndex: "operation",
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Popconfirm
            title="Sure to delete?"
            onConfirm={() => handleDelete(record.key)}
          >
            <a>Delete</a>
          </Popconfirm>
        ) : null,
    },
  ];

  const [dataSource, setDataSource] = useState(rowsDataApi);
  const [count, setCount] = useState(1000);

  const handleDelete = (key) => {
    const newData = dataSource.filter((item) => item.key !== key);
    setDataSource(newData);
  };

  const showAddModal = () => {
    setIsAddModalVisible(true);
    setNewRowImage(null);
    addForm.resetFields();
  };

  const handleAddModalCancel = () => {
    setIsAddModalVisible(false);
    setNewRowImage(null);
    addForm.resetFields();
  };

  const handleAddModalOk = async () => {
    try {
      const values = await addForm.validateFields();
      const newData = {
        ...values,
        key: count,
        image: newRowImage,
      };

      setDataSource([...dataSource, newData]);
      setCount(count + 1);
      setIsAddModalVisible(false);
      setNewRowImage(null);
      addForm.resetFields();
      message.success("Row added successfully");
    } catch (error) {
      message.error("Please fill in all required fields");
    }
  };

  const handleSave = (row) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
  };

  const components = {
    body: {
      cell: EditableCell,
    },
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }

    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave,
      }),
    };
  });

  const uploadProps = {
    beforeUpload: handleUpload,
    showUploadList: false,
    accept: "image/*",
  };

  const newRowUploadProps = {
    beforeUpload: handleNewRowUpload,
    showUploadList: false,
    accept: "image/*",
  };

  return (
    <>
      <Button
        onClick={showAddModal}
        style={{
          marginBottom: 16,
          fontWeight: "500",
        }}
      >
        Add a row
      </Button>
      <Table
        components={components}
        rowClassName={() => "editable-row"}
        bordered
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ x: "max-content" }}
      />

      {/* Add New Row Modal */}
      <Modal
        title="Add New Row"
        visible={isAddModalVisible}
        onOk={handleAddModalOk}
        onCancel={handleAddModalCancel}
      >
        <Form
          form={addForm}
          layout="vertical"
          style={{
            maxHeight: "calc(80vh - 180px)",
            overflowY: "auto",
            paddingRight: "16px",
          }}
        >
          {columnsApiData.map((column) => {
            if (column === "image") {
              return (
                <Form.Item key={column} label="Image" name={column}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      gap: 18,
                    }}
                  >
                    {newRowImage && (
                      <img
                        src={newRowImage}
                        alt="Preview"
                        style={{
                          width: 100,
                          height: 100,
                          objectFit: "cover",
                        }}
                      />
                    )}
                    <Upload {...newRowUploadProps}>
                      <Button type="primary">Upload Image</Button>
                    </Upload>
                  </div>
                </Form.Item>
              );
            }
            return (
              <Form.Item
                key={column}
                label={column}
                name={column}
                rules={[{ required: true, message: `Please enter ${column}` }]}
              >
                <Input.TextArea autoSize={{ minRows: 1, maxRows: 6 }} />
              </Form.Item>
            );
          })}
        </Form>
      </Modal>

      {/* Preview/Edit Image Modal */}
      <Modal
        visible={previewVisible}
        footer={[
          selectedFile ? (
            <div style={{ display: "flex", justifyContent: "center", gap: 8 }}>
              <Button key="save" type="primary" onClick={handleSaveImage}>
                Save
              </Button>
              <Button
                key="cancel"
                onClick={() => {
                  setSelectedFile(null);
                  setTempImagePreview(null);
                }}
              >
                Cancel
              </Button>
            </div>
          ) : (
            <div style={{ textAlign: "center" }}>
              <Upload key="upload" {...uploadProps}>
                <Button type="primary">Replace Image</Button>
              </Upload>
            </div>
          ),
        ]}
        onCancel={() => setPreviewVisible(false)}
      >
        <img
          alt="Preview"
          style={{
            width: "100%",
            marginBottom: "16px",
            marginTop: "32px",
          }}
          src={tempImagePreview || previewImage}
        />
        {selectedFile && (
          <div style={{ marginBottom: "16px", color: "#666" }}>
            New image selected: {selectedFile.name}
          </div>
        )}
      </Modal>
    </>
  );
};

export default EditableDataTable;
