import React, { useRef, useEffect, useState } from "react";
import ChatBubbleModal from "./chatBubbleModal";
import { Typography } from "antd";
import ImageCollage from "./imageCollage";

const { Paragraph } = Typography;

const ChatBubble = ({
  message,
  //  isVerified
}) => {
  const messageRef = useRef(null);
  const [isTruncated, setIsTruncated] = useState(false);

  //...
  const [isChatBubbleModalOpen, setIsChatBubbleModalOpen] = useState(false);

  useEffect(() => {
    const element = messageRef.current;
    if (element) {
      // Check if the content is truncated
      setIsTruncated(element.scrollHeight > element.clientHeight);
    }
  }, [message]);

  return (
    <div>
      <div
        ref={messageRef}
        className={!message?.media_ids?.length > 0 && "message-text"}
        style={{
          color:
            message?.role !== "user" &&
            message?.verified === false &&
            "#777777",
        }}
      >
        {message?.media_ids?.length > 0 && (
          <ImageCollage
            data={message?.media_ids}
            setIsChatBubbleModalOpen={setIsChatBubbleModalOpen}
          />
        )}
        <p
          style={{
            marginBottom: 0,
            whiteSpace: "pre-wrap",
            wordWrap: "break-word",
          }}
        >
          {message?.content}
        </p>
      </div>
      {isTruncated &&
        message?.verified !== false &&
        message?.role !== "system" &&
        message?.sender !== "system" && (
          <p
            style={{
              textAlign: "center",
              fontSize: "12px",
              marginTop: "12px",
              color: "#777777",
              textDecoration: "underline",
              marginBottom: 0,
              cursor: "pointer",
            }}
            onClick={() => setIsChatBubbleModalOpen(true)}
          >
            See More
          </p>
        )}
      <ChatBubbleModal
        isChatBubbleModalOpen={isChatBubbleModalOpen}
        setIsChatBubbleModalOpen={setIsChatBubbleModalOpen}
        message={message}
      />
    </div>
  );
};

export default ChatBubble;
