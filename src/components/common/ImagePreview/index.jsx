import React from "react";
import { Mo<PERSON>, Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import "./index.css";

const ImagePreview = ({ imgSrc, handleCancel, previewVisible }) => {
  return (
    <Modal
      title={null}
      open={previewVisible}
      footer={null}
      onCancel={handleCancel}
      centered
      width="100vw"
      bodyStyle={{
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: 0,
      }}
      closeIcon={
        <Button
          style={{
            width: "40px",
            height: "40px",
            borderRadius: "50%",
            background: "rgba(23, 23, 23, 0.64)",
            border: "none",
            color: "white",
            zIndex: 1000,
          }}
          onClick={handleCancel}
        >
          <CloseOutlined style={{ fontSize: "24px", fontWeight: "bold" }} />
        </Button>
      }
    >
      <div
        style={{
          width: "100vw",
          height: "100vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          background: "transparent", // Remove unwanted backgrounds
        }}
      >
        <img
          src={imgSrc}
          alt="Full Size"
          style={{
            maxWidth: "99vw",
            maxHeight: "99vh",
            objectFit: "contain",
            borderRadius: "8px",
            background: "transparent", // Ensure image background stays clear
          }}
        />
      </div>
    </Modal>
  );
};

export default ImagePreview;
