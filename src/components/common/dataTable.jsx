import React from "react";
import { Table } from "antd";
import DataTablePagination from "./dataTablePagination";

const DataTable = ({ data, columns }) => {
  // const finalColumns = columns?.map((item) => {
  //   return {
  //     title: item,
  //     dataIndex: item,
  //     key: item,
  //   };
  // });

  const finalColumns = columns.map((item) => {
    if (item === "images") {
      return {
        title: item,
        dataIndex: item,
        key: item,
        render: (text, record) => (
          <img
            src={record[item]}
            alt="Not Found!"
            style={{ width: "50px", height: "50px", objectFit: "cover" }}
          />
        ),
      };
    }

    return {
      title: item,
      dataIndex: item,
      key: item,
    };
  });

  return (
    <>
      <Table dataSource={data} columns={finalColumns} pagination={false} />
      <DataTablePagination />
    </>
  );
};
export default DataTable;
