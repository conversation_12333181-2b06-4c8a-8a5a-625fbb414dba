// src/components/LoginForm/LoginForm.jsx
import React, { useState } from "react";
import { Form, Input, Button, Card, message, Spin } from "antd";
import { login } from "../../utils/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

const LoginForm = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const user = await login(values.username, values.password);
      dispatch({ type: "LOGIN_SUCCESS", payload: user });

      navigate("/dashboard"); // Redirect to dashboard
    } catch (error) {
      message.error(error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card
      title="Login"
      style={{ width: 300, margin: "auto", marginTop: "100px" }}
      extra={<Spin spinning={loading} size="default"></Spin>}
    >
      <Form name="login_form" onFinish={onFinish}>
        <Form.Item
          name="username"
          rules={[{ required: true, message: "Please input your Username!" }]}
        >
          <Input placeholder="Username" />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[{ required: true, message: "Please input your Password!" }]}
        >
          <Input.Password placeholder="Password" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" block>
            Log in
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default LoginForm;
