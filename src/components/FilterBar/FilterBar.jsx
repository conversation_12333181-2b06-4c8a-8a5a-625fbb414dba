// src/components/FilterBar/FilterBar.jsx
import React from 'react';
import { Card, Row, Col, Select, DatePicker, Input, Form } from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  SearchOutlined,
} from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

const FilterBar = ({
  profile,
  dateRange,
  searchTerm,
  handleProfileChange,
  handleDateChange,
  handleSearch,
}) => {
  return (
    <Card
      bordered={false}
      style={{
        marginBottom: '24px',
        background: '#fff',
      }}
    >
      <Form layout="vertical">
        <Row gutter={[16, 16]} align="middle">
          {/* Channel Selector */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="Channel">
              <Select
                showSearch
                value={profile}
                onChange={handleProfileChange}
                placeholder="Select Channel"
                suffixIcon={<UserOutlined />}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.children
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                style={{ width: '100%' }}
              >
                <Option value="facebook">Facebook</Option>
                <Option value="twitter">Twitter</Option>
                <Option value="instagram">Instagram</Option>
                {/* Add more options as needed */}
              </Select>
            </Form.Item>
          </Col>

          {/* Date Range Picker */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="Date Range">
              <RangePicker
                style={{ width: '100%' }}
                onChange={handleDateChange}
                allowClear
                suffixIcon={<CalendarOutlined />}
                placeholder={['Start Date', 'End Date']}
                format="YYYY-MM-DD"
              />
            </Form.Item>
          </Col>

          {/* Search Input */}
          <Col xs={24} sm={12} md={12}>
            <Form.Item label="Search">
              <Search
                placeholder="Search..."
                allowClear
                enterButton={<SearchOutlined />}
                size="middle"
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterBar;
