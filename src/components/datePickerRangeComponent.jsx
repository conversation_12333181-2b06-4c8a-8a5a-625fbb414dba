import React from "react";
import { DatePicker } from "antd";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;

const DatePickerRangeComponent = ({ width, setDateRangeValue }) => {
  // Disabling future dates
  const disableFutureDates = (current) => {
    // Disable dates greater than today
    return current && current > dayjs().endOf("day");
  };

  const handleDateChange = (dates, dateStrings) => {
    if (dates === null) {
      setDateRangeValue({});
      return;
    }

    const start_date = dateStrings[0];
    const end_date = dateStrings[1];
    setDateRangeValue({ start_date, end_date });
  };

  return (
    <RangePicker
      defaultValue={[dayjs().subtract(24, "hour"), dayjs()]} // Last 24 hours
      onChange={handleDateChange}
      disabledDate={disableFutureDates}
      style={{ width: width ?? "100%" }}
    />
  );
};

export default DatePickerRangeComponent;
