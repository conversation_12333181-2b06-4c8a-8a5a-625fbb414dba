import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Layout, <PERSON>u, <PERSON><PERSON>, <PERSON>con<PERSON>rm, Mo<PERSON>, Tooltip } from "antd";
import {
  HomeOutlined,
  MessageFilled,
  PlayCircleOutlined,
  PoweroffOutlined,
  SettingOutlined,
  UsergroupAddOutlined,
  UserAddOutlined,
  AuditOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  LockOutlined,
  <PERSON>boltOutlined,
  BellOutlined,
  FileTextOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation } from "react-router-dom";
import "./SideNav.css"; // Import the CSS file for custom styles
import { logout } from "../../store/slices/profile.slice.js";

import InviteAgentsForm from "../../pages/Inviteuser/inviteuser.component.jsx";
import { FcDocument } from "react-icons/fc";
const { Sider } = Layout;

const SideNav = ({ collapsed, setCollapsed }) => {
  const [isInviteModalVisible, setIsInviteModalVisible] = useState(false);
  const [selectedKey, setSelectedKey] = useState("");

  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  // Set the selected key based on the current path
  useEffect(() => {
    if (
      location.pathname === "/knowledge-base" ||
      location.pathname.startsWith("/knowledge-base/")
    ) {
      setSelectedKey("/knowledge-base");
    } else if (
      location.pathname === "/resolve-conflict" ||
      location.pathname.startsWith("/resolve-conflict/")
    ) {
      setSelectedKey("/knowledge-base");
    } else {
      setSelectedKey(location.pathname);
    }
  }, [location.pathname]);

  const showInviteModal = () => {
    setIsInviteModalVisible(true);
  };
  const handleInviteModalClose = () => {
    setIsInviteModalVisible(false);
  };

  // Get role from local storage
  const userRole = localStorage.getItem("role");

  const handleLogout = () => {
    const slug = localStorage.getItem("slug");
    dispatch(logout());
    navigate(`/${slug}/login`);
  };

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const allowed_navs =
    JSON.parse(localStorage.getItem("nav_permissions")) || [];

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      trigger={null}
      className="custom-sider"
      width={200}
      collapsedWidth={80}
    >
      <div
        className="logo-container"
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "0px",
        }}
      >
        <Button
          type="text"
          className="collapse-button"
          icon={
            collapsed ? (
              <MenuUnfoldOutlined className="menu-icon" />
            ) : (
              <MenuFoldOutlined className="menu-icon" />
            )
          }
          onClick={toggleCollapsed}
        >
          {!collapsed && <span style={{ fontWeight: "bold" }}>Menu</span>}
        </Button>
      </div>

      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[selectedKey]}
        className={collapsed ? "custom-menu collapsed-menu" : "custom-menu"}
      >
        {allowed_navs.includes("Dashboard") && (
          <Menu.Item
            key="/dashboard"
            icon={<HomeOutlined className="menu-icon" />}
            onClick={() => navigate("/dashboard")}
            className="menu-item"
          >
            Dashboard
          </Menu.Item>
        )}

        {allowed_navs.includes("Channels") && (
          <Menu.Item
            key="/channels"
            icon={<MessageOutlined className="menu-icon" />}
            onClick={() => navigate("/channels")}
            className="menu-item"
          >
            Channels
          </Menu.Item>
        )}

        {allowed_navs.includes("KnowledgeBase") && (
          <Menu.Item
            key="/knowledge-base"
            icon={<FileTextOutlined className="menu-icon" />}
            onClick={() => navigate("/knowledge-base")}
            className="menu-item"
          >
            Knowledge Base
          </Menu.Item>
        )}

        {allowed_navs.includes("Playground") && (
          <Menu.Item
            key="/playground"
            icon={<PlayCircleOutlined className="menu-icon" />}
            onClick={() => navigate("/playground")}
            className="menu-item"
          >
            Playground
          </Menu.Item>
        )}

        {allowed_navs.includes("Feedback Log") && (
          <Menu.Item
            key="/feedback-log"
            icon={<AuditOutlined className="menu-icon" />}
            onClick={() => navigate("/feedback-log")}
            className="menu-item"
          >
            Feedback Log
          </Menu.Item>
        )}

        {allowed_navs.includes("CTA") && (
          <Menu.Item
            key="/cta"
            icon={<ThunderboltOutlined className="menu-icon" />}
            onClick={() => navigate("/cta")}
            className="menu-item"
          >
            Call To Action
          </Menu.Item>
        )}

        {allowed_navs.includes("Setup") && (
          <Menu.Item
            key="/setup"
            icon={<SettingOutlined className="menu-icon" />}
            onClick={() => navigate("/setup")}
            className="menu-item"
          >
            Setup
          </Menu.Item>
        )}

        {userRole !== "agent" && allowed_navs.includes("User Registration") && (
          <Menu.Item
            className="menu-item"
            icon={<UserAddOutlined />}
            onClick={showInviteModal}
          >
            Invite User
          </Menu.Item>
        )}

        <Menu.Item
          key="/settings"
          icon={<SettingOutlined className="menu-icon" />}
          onClick={() => navigate("/settings")}
          className="menu-item"
        >
          Settings
        </Menu.Item>

        {/* Logout as a Menu.Item with Popconfirm */}
        <Popconfirm
          title="Are you sure you want to log out?"
          onConfirm={handleLogout}
          okText="Yes"
          cancelText="No"
        >
          <Menu.Item
            key="logout"
            icon={<PoweroffOutlined className="menu-icon" />}
            className="menu-item"
            style={{
              position: "absolute",
              bottom: "1vh",
              width: "calc(100% - 8px)",
            }}
          >
            Logout
          </Menu.Item>
        </Popconfirm>
      </Menu>

      <Modal
        title="Invite a new User"
        open={isInviteModalVisible}
        onCancel={handleInviteModalClose}
        footer={null}
        width={800}
        destroyOnClose
      >
        <InviteAgentsForm onClose={handleInviteModalClose} />
      </Modal>
    </Sider>
  );
};

export default SideNav;
