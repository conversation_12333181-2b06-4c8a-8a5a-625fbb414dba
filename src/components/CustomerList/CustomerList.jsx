import React, { useState, useRef, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks.js";
import {
  Layout,
  List,
  Avatar,
  Spin,
  DatePicker,
  Collapse,
  Badge,
  Form,
  message,
  Button,
  Tag,
  Tooltip,
  Row,
  Col,
  Space,
  Typography,
} from "antd";
import {
  CalendarOutlined,
  FilterOutlined,
  MessageOutlined,
  CommentOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  RobotOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  CheckCircleFilled,
  CheckCircleTwoTone,
  CheckSquareOutlined,
  CheckSquareFilled,
  CheckSquareTwoTone,
  TagOutlined,
  CloseSquareOutlined,
  DownOutlined,
} from "@ant-design/icons";
import "./CustomerList.css";
import SearchInput from "../common/searchInput";
import {
  fetchCTAOptionsApi,
  fetchLanguagesList<PERSON>pi,
  fetchProductsList<PERSON><PERSON>,
  fetchSentimentsList<PERSON><PERSON>,
  fetchTags<PERSON>ist<PERSON><PERSON>,
  getCustomersData<PERSON>pi,
  getFilters<PERSON>pi,
} from "../../services/channels.service.js";
import { ReloadOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { logout } from "../../store/slices/profile.slice.js";
import { useNavigate } from "react-router-dom";
import { transformString } from "../../helpers/channelNameTransformation.js";
import { debounceScroll } from "../../helpers/debounceScroll.js";
import FilterModal from "./FilterModal.jsx";
import ContactModal from "./sections/ContactModal/index.jsx";
import { checkBalance } from "../../helpers/checkBalance.js";

const { Sider } = Layout;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { Text } = Typography;

const CustomerList = ({
  setCustomerInfo,
  selectedCustomerId,
  handleCustomerClick,
  setSelectedCustomerId,
  refreshCustomerList,
}) => {
  const location = useLocation();
  const { phoneID, navigatedFromCta } = location.state || {};

  // State to manage the collapse panel
  const [activeKey, setActiveKey] = useState([]); // Empty array means collapsed

  // State for enhanced collapsible active filters
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);

  // State for showing/hiding active filters section
  const [showActiveFilters, setShowActiveFilters] = useState(true);

  // Form reference for controlling form fields
  const [form] = Form.useForm();

  const [totalPages, setTotalPages] = useState();

  const scrollRef = useRef(null);
  const [showBackTopButton, setShowBackTopButton] = useState(false); //control visibility of the back to top button

  const navigate = useNavigate();
  const { state } = location;

  // Fetch All Customers
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  // Customers
  const [customers, setCustomers] = useState([]);

  // Scroll Pagination
  const [page, setPage] = useState(1);

  // Channel select options
  const [channel, setChannel] = useState();

  const [selectedProduct, setSelectedProduct] = useState();

  const [selectedLanguage, setSelectedLanguage] = useState();

  const [selectedSentiment, setSelectedSentiment] = useState();

  // CTA select options
  const [ctaOptions, setCTAOptions] = useState();
  const [ctaStatus, setCTAStatus] = useState();

  const [tagsList, setTagsList] = useState();

  const [productsList, setProductsList] = useState();

  const [languagesList, setLanguagesList] = useState();

  const [sentimentsList, setSentimentsList] = useState();

  const [selectedCTA, setSelectedCTA] = useState();
  const [selectedCTAStatus, setSelectedCTAStatus] = useState();

  // Search keyword
  const [searchKeywords, setSearchKeywords] = useState(phoneID);

  const { dateRangeValue } = useAppSelector((state) => state.profile);

  // Date Range FILTER
  // const [dates, setDates] = useState([
  //   dayjs().subtract(1, "day").format("YYYY-MM-DD"),
  //   dayjs().format("YYYY-MM-DD"),
  // ]);

  // Check if dates are passed as null from navigation state
  const [dates, setDates] = useState(
    navigatedFromCta
      ? undefined
      : [
          dayjs(dateRangeValue.start_date).format("YYYY-MM-DD"),
          dayjs(dateRangeValue.end_date).format("YYYY-MM-DD"),
        ]
  );

  // const [dateRangeAntdValue, setDateRangeAntdValue] = useState([
  //   dayjs().subtract(1, "day"),
  //   dayjs(),
  // ]);

  // Initialize date picker values based on navigation state
  const [dateRangeAntdValue, setDateRangeAntdValue] = useState(
    navigatedFromCta
      ? undefined
      : [dayjs(dateRangeValue.start_date), dayjs(dateRangeValue.end_date)]
  );

  const handleCTAOptions = (value) => {
    setSelectedCTA(value);
  };

  const handleCTAStatus = (value) => {
    setSelectedCTAStatus(value);
  };

  const handleCollapseChange = (key) => {
    setActiveKey(key);
  };

  // .................................
  // Function to generate consistent colors based on names
  const getNameColor = (name) => {
    const colors = [
      "#f56a00",
      "#7265e6",
      "#ffbf00",
      "#00a2ae",
      "#1890ff",
      "#52c41a",
      "#722ed1",
      "#eb2f96",
    ];

    // Simple hash function to get consistent color for a name
    let hash = 0;
    if (!name) return colors[0];
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  // Helper function to get customer initials
  const getInitials = (name) => {
    if (!name || name === "N/A") return "?";
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // .................................

  // Add a function to handle clearing all filters
  const handleClearFilters = () => {
    // Reset all your filter states here
    setChannel();
    setSelectedCTA();
    setSelectedCTAStatus();
    setDates();
    setDateRangeAntdValue();
    setSearchKeywords();
    setSelectedProduct();
    setSelectedLanguage();
    setSelectedSentiment();

    // Reset form fields to ensure UI components reflect the cleared values
    form.resetFields();

    // Display a success message to user
    message.success("All filters have been cleared");
  };

  // Determine if the panel is collapsed
  const isPanelCollapsed = !activeKey.includes("1");

  // Panel header with modified Clear All button
  const panelHeader = (
    <span>
      <FilterOutlined /> Filters
      {isPanelCollapsed ? (
        <>
          <span style={{ marginLeft: 8, fontSize: 14, color: "#607D8B" }}>
            {channel && (
              <span style={{ marginLeft: 4 }}>
                <span style={{ marginRight: "5px", color: "grey" }}>•</span>
                {channel.charAt(0).toUpperCase() + channel.slice(1)}
              </span>
            )}
            {selectedCTA && (
              <span style={{ marginLeft: 12 }}>
                <span
                  style={{
                    marginRight: "5px",
                    color: "grey",
                  }}
                >
                  •
                </span>
                {transformString(selectedCTA)}
              </span>
            )}
          </span>
        </>
      ) : (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleClearFilters();
          }}
          color="danger"
          variant="text"
          style={{
            marginLeft: "12px",
            paddingLeft: "8px",
            paddingRight: "8px",
            height: "24px",
          }}
          disabled={
            !channel &&
            !selectedCTA &&
            !selectedCTAStatus &&
            !dates &&
            !searchKeywords &&
            !selectedProduct &&
            !selectedLanguage &&
            !selectedSentiment
          }
        >
          Clear All
        </Button>
      )}
    </span>
  );

  // Detect Scroll Event
  const handleScroll = debounceScroll(() => {
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;

    // Show the back to top button when user scrolls
    if (scrollTop > 200) {
      setShowBackTopButton(true);
    } else {
      setShowBackTopButton(false);
    }

    if (
      scrollTop + clientHeight + 60 >= scrollHeight &&
      !loading &&
      page <= totalPages
    ) {
      fetchAllCustomers();
    }
  }, 100);

  // Fetch All Customers
  const fetchAllCustomers = () => {
    setLoading(true);

    dispatch(
      getCustomersDataApi({
        params: {
          start_date: dates?.length > 0 ? dates[0] : null,
          end_date: dates?.length > 0 ? dates[1] : null,
          page: page,
          page_size: "10",
          search: searchKeywords,
          cta: selectedCTA,
          status: selectedCTAStatus,
          tags: channel,
          topic: selectedProduct,
          language: selectedLanguage,
          sentiment: selectedSentiment,
        },

        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          setCustomers((prevCustomers) => [...prevCustomers, ...response.data]);
          setTotalPages(response?.total_pages);
          setPage((prevPage) => prevPage + 1);
        },
        failureCallback: (errors) => {
          console.log("Error in getCustomersDataApi -> ", errors);
        },
      })
    );
  };

  // Fetch InitialCustomerList
  const fetchInitialCustomerList = () => {
    setLoading(true);
    setCustomers([]); // Clear existing customers first

    dispatch(
      getCustomersDataApi({
        params: {
          start_date: dates?.length > 0 ? dates[0] : null,
          end_date: dates?.length > 0 ? dates[1] : null,
          page: 1,
          page_size: "10",
          search: searchKeywords,
          cta: selectedCTA,
          status: selectedCTAStatus,
          tags: channel,
          topic: selectedProduct,
          language: selectedLanguage,
          sentiment: selectedSentiment,
        },

        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          setCustomers(response.data);
          setTotalPages(response?.total_pages);
          setPage(2);
          if (response?.data?.length > 0) {
            setSelectedCustomerId(response?.data?.[0]?.customer_id);
            setCustomerInfo(response?.data?.[0]);
          }
        },
        failureCallback: (errors) => {
          console.log("Error in getCustomersDataApi -> ", errors);
        },
      })
    );
  };

  const handleChannelChange = (value) => {
    setChannel(value);
  };

  const fetchCTAOptions = () => {
    dispatch(
      fetchCTAOptionsApi({
        finalCallback: () => {},
        successCallback: (response) => {
          const ctaList = response?.types?.map((item) => {
            return { label: transformString(item?.name), value: item?.name };
          });
          const ctaStatus = response?.status?.map((item) => {
            return { label: transformString(item?.name), value: item?.name };
          });

          setCTAOptions(ctaList);
          setCTAStatus(ctaStatus);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchFilters = () => {
    dispatch(
      getFiltersApi({
        params: { filter_route: "ai_response" },
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.channel?.map((item) => {
            return { label: item, value: item };
          });
          setTagsList(list);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchProductsList = () => {
    dispatch(
      fetchProductsListApi({
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.map((item) => {
            return { label: item?.topic, value: item?.topic };
          });
          setProductsList(list);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchLanguagesList = () => {
    dispatch(
      fetchLanguagesListApi({
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.map((item) => {
            return { label: item, value: item };
          });
          setLanguagesList(list);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchSentimentsList = () => {
    dispatch(
      fetchSentimentsListApi({
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.map((item) => {
            return { label: item, value: item };
          });
          setSentimentsList(list);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  // Handle Date range filter
  const handleDateChange = (values) => {
    if (values) {
      const stringDates = values.map((date) => date.format("YYYY-MM-DD"));
      const newDateRangeAntdValue = [
        dayjs(stringDates[0]),
        dayjs(stringDates[1]),
      ];
      setDateRangeAntdValue(newDateRangeAntdValue);
      setDates(stringDates);
    } else {
      setDates();
    }
  };

  useEffect(() => {
    if (refreshCustomerList) {
      fetchInitialCustomerList();
    }
  }, [refreshCustomerList]);

  // UseEffects
  useEffect(() => {
    // Fetch
    fetchInitialCustomerList();
  }, [
    dates,
    searchKeywords,
    selectedCTA,
    selectedCTAStatus,
    channel,
    selectedProduct,
    selectedLanguage,
    selectedSentiment,
  ]);

  useEffect(() => {
    fetchCTAOptions();
    fetchFilters();
    fetchProductsList();
    fetchLanguagesList();
    fetchSentimentsList();
  }, []);

  // Scroll to top
  const scrollToTop = () => {
    scrollRef.current.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div>
      <Sider
        width={400}
        theme="light"
        style={{
          borderRadius: "8px",
          paddingBottom: "36px",
          height: "90vh",
          overflow: "hidden",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <FilterModal
            ctaOptions={ctaOptions}
            ctaStatus={ctaStatus}
            tagsList={tagsList}
            productsList={productsList}
            languagesList={languagesList}
            sentimentsList={sentimentsList}
            selectedCTA={selectedCTA}
            selectedCTAStatus={selectedCTAStatus}
            channel={channel}
            selectedProduct={selectedProduct}
            selectedLanguage={selectedLanguage}
            selectedSentiment={selectedSentiment}
            dateRangeAntdValue={dateRangeAntdValue}
            handleCTAOptions={handleCTAOptions}
            handleCTAStatus={handleCTAStatus}
            handleChannelChange={handleChannelChange}
            handleDateChange={handleDateChange}
            setSelectedProduct={setSelectedProduct}
            setSelectedLanguage={setSelectedLanguage}
            setSelectedSentiment={setSelectedSentiment}
            setSearchKeywords={setSearchKeywords}
          />

          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
            }}
          >
            <ContactModal />

            {/* Collapsible Icon for Active Filters */}
            {(selectedCTA ||
              selectedCTAStatus ||
              channel ||
              selectedProduct ||
              selectedLanguage ||
              selectedSentiment ||
              (dates && dates.length > 0) ||
              searchKeywords) && (
              <div
                style={{
                  margin: "5px",
                  marginRight: "8px",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <div
                  onClick={() => setShowActiveFilters(!showActiveFilters)}
                  style={{
                    cursor: "pointer",
                    padding: "8px",
                    borderRadius: "50%",
                    backgroundColor: "#fafafa",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    transition: "all 0.2s ease",
                    width: "32px",
                    height: "32px",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#f0f0f0";
                    e.currentTarget.style.borderColor = "#d9d9d9";
                    e.currentTarget.style.transform = "scale(1.1)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "#fafafa";
                    e.currentTarget.style.borderColor = "#e8e8e8";
                    e.currentTarget.style.transform = "scale(1)";
                  }}
                >
                  <DownOutlined
                    style={{
                      fontSize: "16px",
                      color: "#8c8c8c",
                      transition: "transform 0.2s ease",
                      transform: showActiveFilters
                        ? "rotate(180deg)"
                        : "rotate(0deg)",
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Active Filters - FeedbackLog Style */}
        {showActiveFilters &&
          (selectedCTA ||
            selectedCTAStatus ||
            channel ||
            selectedProduct ||
            selectedLanguage ||
            selectedSentiment ||
            (dates && dates.length > 0) ||
            searchKeywords) && (
            <div
              style={{
                margin: "10px",
                marginTop: 0,
                padding: "12px 16px",
                borderRadius: "8px",
                border: "1px solid #e6f0ff",
              }}
            >
              <Row align="middle">
                <Col>
                  <Text
                    style={{
                      fontSize: "14px",
                      fontWeight: "600",
                      marginRight: "8px",
                    }}
                  >
                    Active Filters:
                  </Text>
                  <Space size={10} wrap>
                    {/* Date Filter */}
                    {dates && dates.length > 0 && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => {
                          setDates(undefined);
                          setDateRangeAntdValue(undefined);
                        }}
                      >
                        <CalendarOutlined /> Date:{" "}
                        {dayjs(dates[0]).format("MMM DD")} —{" "}
                        {dayjs(dates[1]).format("MMM DD")}
                      </Tag>
                    )}

                    {/* CTA Filter */}
                    {selectedCTA && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSelectedCTA(undefined)}
                      >
                        <FilterOutlined /> CTA: {transformString(selectedCTA)}
                      </Tag>
                    )}

                    {/* Status Filter */}
                    {selectedCTAStatus && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSelectedCTAStatus(undefined)}
                      >
                        {selectedCTAStatus === "resolved" ? (
                          <CheckSquareOutlined />
                        ) : selectedCTAStatus === "unresolved" ? (
                          <CloseSquareOutlined />
                        ) : (
                          <CheckCircleOutlined />
                        )}
                        Status: {transformString(selectedCTAStatus)}
                      </Tag>
                    )}

                    {/* Channel Filter */}
                    {channel && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setChannel(undefined)}
                      >
                        <MessageOutlined /> Channel:{" "}
                        {channel.charAt(0).toUpperCase() + channel.slice(1)}
                      </Tag>
                    )}

                    {/* Topic Filter */}
                    {selectedProduct && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSelectedProduct(undefined)}
                      >
                        <TagOutlined /> Topic: {selectedProduct}
                      </Tag>
                    )}

                    {/* Language Filter */}
                    {selectedLanguage && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSelectedLanguage(undefined)}
                      >
                        <CommentOutlined /> Language: {selectedLanguage}
                      </Tag>
                    )}

                    {/* Sentiment Filter */}
                    {selectedSentiment && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSelectedSentiment(undefined)}
                      >
                        <ExclamationCircleOutlined /> Sentiment:{" "}
                        {selectedSentiment}
                      </Tag>
                    )}

                    {/* Search Filter */}
                    {searchKeywords && (
                      <Tag
                        style={{
                          borderRadius: "6px",
                          padding: "4px 8px",
                          fontSize: "13px",
                          fontWeight: 500,
                          border: "none",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                        closable
                        onClose={() => setSearchKeywords(undefined)}
                      >
                        Search:{" "}
                        {searchKeywords.length > 15
                          ? searchKeywords.substring(0, 15) + "..."
                          : searchKeywords}
                      </Tag>
                    )}
                  </Space>
                </Col>
              </Row>
            </div>
          )}

        <div
          style={{
            paddingLeft: "10px",
            paddingRight: "10px",
            paddingBottom: "10px",
            display: "flex",
            alignItems: "center",
            gap: "12px",
          }}
        >
          <SearchInput
            setSearchKeywords={setSearchKeywords}
            searchKeywords={searchKeywords}
          />
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={() => {
              fetchInitialCustomerList();
            }}
            disabled={loading}
          />
        </div>

        <Spin spinning={loading}>
          <List
            ref={scrollRef}
            onScroll={handleScroll}
            itemLayout="horizontal"
            dataSource={customers}
            style={{
              height:
                showActiveFilters &&
                (selectedCTA ||
                  selectedCTAStatus ||
                  channel ||
                  selectedProduct ||
                  selectedLanguage ||
                  selectedSentiment ||
                  (dates && dates.length > 0) ||
                  searchKeywords)
                  ? "calc(75vh - 60px)"
                  : "75vh",
              overflowY: "auto",
            }}
            renderItem={(item) => (
              <List.Item
                key={item.id}
                onClick={() => {
                  setSelectedCustomerId(item?.customer_id);
                  setCustomerInfo(item);
                  return handleCustomerClick(item?.customer_id);
                }}
                className={`customer-list-item ${
                  selectedCustomerId === item.customer_id ? "selected" : ""
                }`}
              >
                <List.Item.Meta
                  style={{
                    marginRight: "8px",
                    wordBreak: "break-word",
                  }}
                  avatar={
                    <Badge count={item?.unreadCount} offset={[0, 5]}>
                      <Avatar
                        style={{
                          backgroundColor: getNameColor(item?.customer_name),
                          fontSize: "14px",
                        }}
                      >
                        {getInitials(item?.customer_name)}
                      </Avatar>
                    </Badge>
                  }
                  title={
                    <div className="customer-title">
                      <div>
                        <span
                          style={{
                            fontWeight:
                              selectedCustomerId === item.customer_id
                                ? "600"
                                : "500",
                          }}
                        >
                          {item?.customer_name ?? "N/A"}
                        </span>
                      </div>

                      <div
                        style={{
                          flex: 1,
                          display: "flex",
                          justifyContent: "flex-end",
                          alignItems: "center",
                          paddingLeft: "18px",
                        }}
                      >
                        <div>
                          {item?.channel && (
                            <>
                              <Tag
                                color="blue"
                                style={{
                                  fontSize: "12px",
                                  padding: "0px 7px",
                                  marginRight: "0px",
                                  verticalAlign: "super",
                                  fontWeight: "500",
                                }}
                                icon={<CommentOutlined />}
                              >
                                {item?.channel}
                              </Tag>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  }
                  description={
                    <>
                      <div
                        style={{
                          display: "flex",
                        }}
                      >
                        <div
                          style={{
                            display: "-webkit-box",
                            webkitBoxOrient: "vertical",
                            WebkitLineClamp: 2, // Limit to 2 lines
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            lineHeight:
                              "1.5" /* Adjust based on your font size */,
                            maxHeight:
                              "calc(1.5em * 2)" /* line-height * number of lines */,
                          }}
                        >
                          {item?.latest_chat_content?.role === "user" ||
                          item?.latest_chat_content?.role === "agent" ? (
                            <UserOutlined
                              className="message-icon"
                              style={{ marginRight: "6px", color: "#69c0ff" }}
                            />
                          ) : (
                            item?.latest_chat_content?.role === "assistant" && (
                              <RobotOutlined
                                className="message-icon"
                                style={{
                                  marginRight: "6px",
                                  color: "#69c0ff",
                                }}
                              />
                            )
                          )}

                          {item?.latest_chat_content?.content === "" ? (
                            <p
                              style={{
                                fontStyle: "italic",
                                display: "inline",
                                color: "#999999",
                              }}
                            >
                              Sent an Image
                            </p>
                          ) : (
                            item?.latest_chat_content?.content ??
                            "No messages found"
                          )}
                        </div>
                        {!item?.ai_replied && (
                          <div>
                            <Tooltip title="No Reply from AI">
                              <ExclamationCircleOutlined
                                style={{
                                  paddingLeft: "8px",
                                  fontSize: "16px",
                                  color: "#faad14",
                                }}
                              />
                            </Tooltip>
                          </div>
                        )}
                      </div>

                      {item?.cta_distinct_count?.length > 0 && (
                        <div
                          style={{
                            marginTop: "8px",
                            display: "flex",
                            gap: "4px",
                          }}
                        >
                          {item?.cta_distinct_count
                            ?.slice(0, 2)
                            .map((ctaItem, index) => (
                              <Button
                                style={{
                                  padding: "1.5px 6px",
                                  height: "24px",
                                  fontSize: "12px",
                                  borderRadius: "4px",
                                  color: "#1890ff",
                                }}
                                key={index}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // navigate("/cta");
                                  navigate("/cta", {
                                    state: {
                                      type: ctaItem?.type,
                                      status:
                                        ctaItem?.unresolved_count === 0
                                          ? "resolved"
                                          : "open",
                                      search: item.email ?? item?.customer_id,
                                      dates: null, // Pass null dates to reset date range in CTA component
                                    },
                                  });
                                }}
                              >
                                {transformString(ctaItem?.type)}
                                {ctaItem?.unresolved_count > 1 ? (
                                  <Badge
                                    count={ctaItem?.unresolved_count}
                                    size="small"
                                    style={{
                                      backgroundColor: "#40a9ff",
                                      marginLeft: "2px",
                                      fontSize: "10px",
                                      height: "16px",
                                      lineHeight: "16px",
                                      minWidth: "16px",
                                    }}
                                  />
                                ) : (
                                  ctaItem?.unresolved_count === 0 && (
                                    <CheckCircleOutlined
                                      style={{
                                        fontSize: "14px",
                                        marginLeft: "2px",
                                      }}
                                    />
                                  )
                                )}
                              </Button>
                            ))}

                          {item?.cta_distinct_count?.length > 2 && (
                            <Tooltip
                              color="#fafafa"
                              title={
                                <div>
                                  {item?.cta_distinct_count
                                    ?.slice(2)
                                    .map((ctaItem, index) => (
                                      <Tag
                                        style={{
                                          height: "24px",
                                          marginBottom: "8px",
                                        }}
                                      >
                                        {transformString(ctaItem?.type)}
                                      </Tag>
                                    ))}
                                </div>
                              }
                            >
                              <Tag
                                style={{
                                  paddingTop: "1px",
                                  paddingBottom: "1px",
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              >
                                +{item?.cta_distinct_count?.length - 2}
                              </Tag>
                            </Tooltip>
                          )}
                        </div>
                      )}

                      {item?.latest_chat_content?.created_at && (
                        <div
                          style={{
                            textAlign: "end",
                            fontSize: "12px",
                            marginTop: "8px",
                            color: "#b8b8b8",
                          }}
                        >
                          {(() => {
                            const messageDate = dayjs(
                              item?.latest_chat_content?.created_at
                            );
                            const today = dayjs();

                            // If message is from today, show time only (e.g., "4:13 PM")
                            if (
                              messageDate.format("YYYY-MM-DD") ===
                              today.format("YYYY-MM-DD")
                            ) {
                              return messageDate.format("h:mm A");
                            }

                            // If message is from this week (last 7 days), show day name (e.g., "Sun")
                            if (messageDate.isAfter(today.subtract(7, "day"))) {
                              return messageDate.format("ddd");
                            }

                            // Otherwise show month and day (e.g., "Dec 10")
                            return messageDate.format("MMM D");
                          })()}
                        </div>
                      )}
                    </>
                  }
                />
              </List.Item>
            )}
          />
          {showBackTopButton && (
            <Button
              type="primary"
              shape="circle"
              icon={<span style={{ fontSize: 18 }}>↑</span>}
              onClick={() => {
                scrollToTop();
                setTimeout(() => {
                  fetchInitialCustomerList();
                }, 600);
              }}
              className="back-to-top-button"
            />
          )}
        </Spin>
      </Sider>
    </div>
  );
};

export default CustomerList;
