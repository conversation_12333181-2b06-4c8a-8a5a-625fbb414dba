import React, { useEffect, useState } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Tooltip,
  message,
  Card,
  Tag,
  Row,
  Col,
} from "antd";
import {
  PlusOutlined,
  UserOutlined,
  EditOutlined,
  FormOutlined,
} from "@ant-design/icons";
import "./index.css";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { sendCustomerMessageApi } from "../../../../services/channels.service";
import PhoneInput from "react-phone-input-2";
import { isPossiblePhoneNumber } from "react-phone-number-input";
import "react-phone-input-2/lib/style.css";
import {
  getMessageTemplatesApi,
  getMsgFlag,
} from "../../../../services/customers.service";
import { transformString } from "../../../../helpers/channelNameTransformation";
import { checkBalance } from "../../../../helpers/checkBalance";
import { BiMessageRoundedEdit } from "react-icons/bi";

const ContactModal = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [flag, setFlag] = useState(false);
  const [form] = Form.useForm();
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewMessage, setPreviewMessage] = useState("");
  const [templates, setTemplates] = useState([]);
  const [phoneError, setPhoneError] = useState("");
  const [templateError, setTemplateError] = useState("");

  // All your existing functions remain the same...
  const showModal = async () => {
    const balance = await checkBalance();

    // If balance is 0, show a message
    if (balance < 1) {
      message.error(
        "You don't have enough credits. Please recharge your account."
      );
      return;
    }

    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setSelectedTemplate(null);
    setPreviewMessage("");
  };

  const handleSubmit = (values) => {
    const isPhoneNumberValid = isPossiblePhoneNumber(
      values.country_code + values.phone_number
    );
    const templateIsEmpty =
      !selectedTemplate || !selectedTemplate.template_name;

    let error = false;
    if (!isPhoneNumberValid) {
      setPhoneError("Please enter a valid phone number");
      error = true;
    }
    if (templateIsEmpty) {
      setTemplateError("Please select a template");
      error = true;
    }
    if (error) {
      return;
    }

    setLoading(true);

    // Extract template variables
    const template_vars = {};
    let finalMessage = previewMessage;

    if (selectedTemplate && selectedTemplate.variables) {
      Object.keys(selectedTemplate.variables).forEach((varKey) => {
        // Get the value from the form field for this variable
        const fieldValue = values[`variable_${varKey}`];
        if (fieldValue) {
          template_vars[varKey] = fieldValue;

          // Replace any remaining placeholders in the message with actual values
          const placeholder = `{{${varKey}}}`;
          finalMessage = finalMessage.replace(
            new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
            fieldValue
          );
        }
      });
    }

    const formattedData = {
      channel: values.channel,
      country_code: values.country_code,
      name: values.name || "",
      phone_number: values.phone_number,
      message: finalMessage, // Use the fully replaced message here
      template_id: selectedTemplate?.sid,
      template_vars: template_vars,
    };

    console.log("VALUES: -> ", formattedData);

    dispatch(
      sendCustomerMessageApi({
        data: formattedData,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: () => {
          message.success("Contact created successfully");
          setIsModalVisible(false);
          form.resetFields();
          setSelectedTemplate(null);
          setPreviewMessage("");
        },
        failureCallback: () => {},
      })
    );
  };
  const handleTemplateChange = (templateName) => {
    const template = templates.find((t) => t.template_name === templateName);
    setSelectedTemplate(template);

    // Clear any existing variable field values
    const currentValues = form.getFieldsValue();
    Object.keys(currentValues).forEach((key) => {
      if (key.startsWith("variable_")) {
        form.setFieldValue(key, "");
      }
    });

    // If template has variables, check if any of them refer to customer name
    if (template.variables && Object.keys(template.variables).length > 0) {
      const customerNameKeys = Object.keys(template.variables).filter(
        (key) =>
          template.variables[key].toLowerCase().includes("customer") ||
          template.variables[key].toLowerCase().includes("name")
      );

      // If there's a customer name variable and the user has already entered a name
      if (customerNameKeys.length > 0 && currentValues.name) {
        // Set the customer name variable value to the already entered name
        customerNameKeys.forEach((key) => {
          form.setFieldValue(`variable_${key}`, currentValues.name);
        });

        // Update preview with the new values
        const updatedValues = form.getFieldsValue();
        updatePreview(template, updatedValues);
      } else {
        // Just show the template text with placeholders
        setPreviewMessage(template.text.replace(/\/n/g, "\n"));
      }
    } else {
      // If template has no variables, set the message directly
      const previewText = template.text.replace(/\/n/g, "\n");
      setPreviewMessage(previewText);
    }
  };

  const updatePreview = (template, values) => {
    let preview = template.text;

    // Replace only the variables that have values
    Object.keys(template.variables || {}).forEach((varKey) => {
      const placeholder = `{{${varKey}}}`;
      const value = values[`variable_${varKey}`];

      // Only replace if there's a value, otherwise keep the placeholder
      if (value) {
        // Replace all occurrences of this specific placeholder
        preview = preview.replace(
          new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
          value
        );
      }
    });

    // Replace /n with actual newlines for preview
    preview = preview.replace(/\/n/g, "\n");
    setPreviewMessage(preview);
  };

  const handleVariableChange = (varKey, value) => {
    const currentValues = form.getFieldsValue();
    currentValues[`variable_${varKey}`] = value;
    updatePreview(selectedTemplate, currentValues);
  };

  const renderPreviewMessage = (message) => {
    if (!selectedTemplate) return message;

    // Split by both variable placeholders and regular text
    const parts = message.split(/({{.*?}})/);

    return parts.map((part, index) => {
      // Check if this part is a variable placeholder
      const match = part.match(/{{(\d+)}}/);
      if (match && selectedTemplate.variables) {
        const varKey = match[1];
        const varName = selectedTemplate.variables[varKey];
        const varValue = form.getFieldValue(`variable_${varKey}`);

        // If we have a value for this variable, show the value
        if (varName && varValue) {
          return <span key={index}>{varValue}</span>;
        }

        // If we found a matching variable name but no value, show it in a tag
        if (varName) {
          return (
            <Tag key={index} color="blue" style={{ margin: "0 2px" }}>
              {varName}
            </Tag>
          );
        }
      }

      // Regular text (including replaced values)
      return <span key={index}>{part}</span>;
    });
  };

  const channelOptions = [{ label: "WhatsApp", value: "whatsapp" }];

  const beforeFormSubmit = () => {
    const currentValues = form.getFieldsValue();
    if (!currentValues.name && currentValues.phone_number) {
      form.setFieldsValue({ name: currentValues.phone_number });
    }
    form.submit();
  };

  const fetchMessageTemplates = () => {
    dispatch(
      getMessageTemplatesApi({
        finalCallback: () => {},
        successCallback: (response) => {
          setTemplates(response);
        },
        failureCallback: () => {},
      })
    );
  };

  useEffect(() => {
    dispatch(
      getMsgFlag({
        finalCallback: () => {},
        successCallback: (response) => {
          setFlag(response?.flag);
        },
        failureCallback: () => {},
      })
    );
    fetchMessageTemplates();
  }, []);

  return (
    <>
      {flag && (
        <Tooltip title="Send new message">
          <Button
            shape="circle"
            icon={<FormOutlined />}
            onClick={showModal}
            style={{
              marginRight: "12px",
              boxShadow: "none",
              border: "none",
              color: "#777777",
            }}
          />
        </Tooltip>
      )}

      <Modal
        title={
          <div className="modal-title">
            <h3>Send New Message</h3>
            <p className="modal-subtitle">
              Add a new customer to your contact list
            </p>
          </div>
        }
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel} className="cancel-button">
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={beforeFormSubmit}
            disabled={loading}
            loading={loading}
            className="submit-button"
          >
            Create Contact
          </Button>,
        ]}
        width={1000}
        centered
        className="contact-modal two-column-layout"
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <div className="form-section">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  channel: "whatsapp",
                  country_code: "",
                  message: "",
                  name: "",
                  phone_number: "",
                }}
                className="contact-form"
              >
                <Form.Item
                  name="name"
                  label="Name"
                  extra="(Optional) Name will be same as phone number."
                  style={{ fontWeight: "400" }}
                >
                  <Input
                    prefix={<UserOutlined className="site-form-item-icon" />}
                    placeholder="Enter customer name"
                    className="form-input"
                    onChange={(e) => {
                      if (selectedTemplate && selectedTemplate.variables) {
                        const customerNameKeys = Object.keys(
                          selectedTemplate.variables
                        ).filter(
                          (key) =>
                            selectedTemplate.variables[key]
                              .toLowerCase()
                              .includes("customer") ||
                            selectedTemplate.variables[key]
                              .toLowerCase()
                              .includes("name")
                        );

                        if (customerNameKeys.length > 0) {
                          // Update the customer name variables with the new name
                          customerNameKeys.forEach((key) => {
                            form.setFieldValue(
                              `variable_${key}`,
                              e.target.value
                            );
                          });

                          // Important: Update preview with the new values
                          const currentValues = form.getFieldsValue();
                          updatePreview(selectedTemplate, currentValues);

                          // Force a re-render of the preview component
                          setPreviewMessage((prev) =>
                            updatePreview(selectedTemplate, currentValues)
                          );
                        }
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name="phone_input"
                  label="Phone Number"
                  validateStatus={phoneError ? "error" : ""}
                  help={phoneError || ""}
                >
                  <div className="phone-input-wrapper">
                    <PhoneInput
                      country={"np"}
                      enableSearch={true}
                      inputClass="form-input"
                      containerClass="phone-input-container"
                      buttonClass="country-dropdown"
                      searchClass="country-search"
                      dropdownClass="country-dropdown-list"
                      value={form.getFieldValue("phone_input")}
                      inputStyle={{
                        width: "100%",
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "400",
                        borderRadius: "8px",
                        padding: "6px 6px 6px 52px",
                      }}
                      buttonStyle={{
                        borderRadius: "8px 0 0 8px",
                        borderRight: "none",
                        backgroundColor: "#fafafa",
                      }}
                      onChange={(phone, countryData) => {
                        setPhoneError("");
                        const countryCode = countryData.dialCode
                          ? `+${countryData.dialCode}`
                          : "";
                        const fullNumber = phone;
                        const phoneNumberOnly = countryData.dialCode
                          ? fullNumber.replace(countryData.dialCode, "")
                          : fullNumber;
                        const cleanedPhoneNumber = phoneNumberOnly.replace(
                          /\D/g,
                          ""
                        );

                        form.setFieldsValue({
                          country_code: countryCode,
                          phone_number: cleanedPhoneNumber,
                          phone_input: phone,
                        });
                        // Only validate when the length is sufficient
                        if (phone.length >= 8) {
                          form.validateFields(["phone_input"]);
                        }
                      }}
                      preferredCountries={["in", "us", "np", "gb"]}
                      autoFormat={true}
                      placeholder="Enter phone number"
                      disableSearchIcon={false}
                      searchPlaceholder="Search country"
                      isValid={(value) => {
                        // Allow any input during typing
                        return true;
                      }}
                    />
                  </div>
                </Form.Item>

                <Form.Item name="country_code" hidden={true}>
                  <Input />
                </Form.Item>

                <Form.Item name="phone_number" hidden={true}>
                  <Input />
                </Form.Item>

                <Form.Item
                  name="channel"
                  label="Channel"
                  rules={[
                    { required: true, message: "Please select a channel" },
                  ]}
                >
                  <Select
                    options={channelOptions}
                    placeholder="Select messaging channel"
                    style={{ width: "100%" }}
                    className="form-select"
                    dropdownClassName="channel-dropdown"
                  />
                </Form.Item>

                <Form.Item
                  name="template"
                  label="Select Template"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: "Please select a template",
                  //   },
                  // ]}
                  validateStatus={templateError ? "error" : ""}
                  help={templateError}
                >
                  <Select
                    placeholder="Select a message template"
                    onChange={(e) => {
                      handleTemplateChange(e);
                      setTemplateError("");
                    }}
                    options={templates.map((template) => ({
                      label: transformString(template.template_name),
                      value: template.template_name,
                    }))}
                  />
                </Form.Item>

                {selectedTemplate &&
                  selectedTemplate.variables &&
                  Object.keys(selectedTemplate.variables).length > 0 && (
                    <Card
                      title="Template Variables"
                      style={{ marginBottom: 16 }}
                    >
                      {Object.keys(selectedTemplate.variables).map((varKey) => (
                        <Form.Item
                          key={varKey}
                          name={`variable_${varKey}`}
                          label={selectedTemplate.variables[varKey]}
                          rules={[
                            {
                              required: true,
                              message: `Please enter ${selectedTemplate.variables[varKey]}`,
                            },
                          ]}
                        >
                          <Input
                            placeholder={`Enter ${selectedTemplate.variables[varKey]}`}
                            onChange={(e) =>
                              handleVariableChange(varKey, e.target.value)
                            }
                          />
                        </Form.Item>
                      ))}
                    </Card>
                  )}
              </Form>
            </div>
          </Col>

          <Col xs={24} md={12}>
            <div className="preview-section">
              {previewMessage ? (
                <Card
                  title="Message Preview"
                  className="preview-card sticky-card"
                  style={{ marginTop: 0 }}
                >
                  <div
                    style={{
                      whiteSpace: "pre-wrap",
                      margin: 0,
                      fontFamily: "inherit",
                      lineHeight: "1.8",
                      minHeight: "150px",
                    }}
                  >
                    {renderPreviewMessage(previewMessage)}
                  </div>
                </Card>
              ) : (
                <Card
                  title="Message Preview"
                  className="preview-card sticky-card empty-preview"
                  style={{ marginTop: 0 }}
                >
                  <div
                    style={{
                      textAlign: "center",
                      color: "#bfbfbf",
                      padding: "40px 0",
                      minHeight: "150px",
                    }}
                  >
                    Select a template to see the preview
                  </div>
                </Card>
              )}
            </div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default ContactModal;
