/* Two Column Layout */
.contact-modal.two-column-layout .ant-modal-body {
  padding: 24px;
  overflow: visible;
}

/* Common form styling - NEW SECTION FOR CONSISTENCY */
.contact-form .ant-form-item {
  margin-bottom: 20px;
}

.contact-form .ant-form-item-label > label,
.contact-form .ant-form-item-extra,
.contact-form input,
.contact-form .ant-select-selection-item,
.contact-form .ant-select-selection-placeholder,
.contact-form .ant-card-head-title,
.phone-input-container input,
.contact-form .ant-input::placeholder {
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Make sure placeholders have consistent styling */
.contact-form .ant-input::placeholder,
.contact-form .ant-select-selection-placeholder {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Ensure PhoneInput text is consistent */
.react-tel-input .form-input {
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Fix template variables card styling */
.contact-form .ant-card .ant-form-item-label > label,
.contact-form .ant-card .ant-input {
  font-weight: 400 !important;
  font-size: 14px !important;
}

.form-section {
  height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f5f5f5;
}

.form-section::-webkit-scrollbar {
  width: 8px;
}

.form-section::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.form-section::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4px;
}

.form-section::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.preview-section {
  position: sticky;
  top: 0;
  height: fit-content;
}

.sticky-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.preview-card {
  /* Removed hover effect */
}

.empty-preview {
  background-color: #fafafa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-modal.two-column-layout {
    width: 95% !important;
    max-width: 600px;
  }

  .form-section {
    height: auto;
    overflow-y: visible;
    padding-right: 0;
  }

  .preview-section {
    position: relative;
    margin-top: 24px;
  }

  .sticky-card {
    position: relative !important;
    top: 0 !important;
  }
}

/* Modal styling */
.contact-modal .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: visible;
  position: relative;
  z-index: 1000;
}

.contact-modal .ant-modal-header {
  padding: 12px 12px 0;
  border-bottom: none;
  background-color: white;
}

.contact-modal .ant-modal-body {
  padding: 8px 12px 12px;
}

.contact-modal .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 8px 12px;
}

/* Title styling */
.modal-title {
  text-align: center;
}

.modal-title h3 {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.modal-subtitle {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  font-weight: 400;
}

/* Form styling */
.contact-form .ant-form-item-label > label {
  color: #262626;
  /* Changed from font-weight: 500 to 400 for consistency */
  font-weight: 400;
}

/* Unified input styling */
.form-input,
.form-select .ant-select-selector,
.form-textarea,
.phone-input-container .form-input {
  border-radius: 8px !important;
  padding: 6px 12px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.3s !important;
  height: 40px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.form-input:hover,
.form-select .ant-select-selector:hover,
.form-textarea:hover,
.phone-input-container .form-input:hover {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
}

.form-input:focus,
.form-select .ant-select-selector:focus,
.form-textarea:focus,
.phone-input-container .form-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  background-color: white !important;
  outline: none !important;
}

.site-form-item-icon {
  color: #bfbfbf;
  margin-right: 8px;
}

/* Button styling */
.submit-button {
  background: #1890ff;
  border-radius: 6px;
  border: none;
  height: 34px;
  font-weight: 500;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.submit-button:hover {
  background: #40a9ff;
}

.cancel-button {
  border-radius: 6px;
  height: 34px;
  font-weight: 500;
}

/* Channel dropdown */
.channel-dropdown .ant-select-item {
  padding: 8px 10px;
  border-radius: 4px;
  font-weight: 400;
}

.channel-dropdown .ant-select-item-option-selected {
  background-color: #e6f7ff;
  font-weight: 400;
}

/* Uniform Phone input styling */
.phone-input-container {
  width: 100%;
  position: relative;
}

.phone-input-container .form-input {
  width: 100% !important;
  padding-left: 50px !important;
  /* Remove specific background color to match other inputs */
  font-weight: 400 !important;
}

/* Fix for country dropdown button to match input styling */
.react-tel-input .flag-dropdown {
  position: absolute;
  top: 0;
  bottom: 0;
  padding: 0;
  border-radius: 8px 0 0 8px !important;
  border: 1px solid #d9d9d9 !important;
  border-right: none !important;
  background-color: #fafafa !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  height: 40px !important; /* Match input height */
}

.react-tel-input .flag-dropdown:hover {
  border-color: #40a9ff !important;
}

.react-tel-input .flag-dropdown.open {
  z-index: 1050 !important;
  background-color: #f0f0f0 !important;
}

.react-tel-input .selected-flag {
  padding: 0 0 0 8px;
  border-radius: 8px 0 0 8px !important;
  display: flex;
  align-items: center;
  height: 100% !important;
  width: 42px !important;
}

.country-dropdown {
  background-color: #fafafa !important;
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.country-search {
  margin: 10px !important;
  padding: 6px 12px !important;
  border-radius: 8px !important;
  border: 1px solid #d9d9d9 !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.react-tel-input .selected-flag:hover,
.react-tel-input .selected-flag:focus {
  background-color: #f0f0f0 !important;
}

.react-tel-input .country-list {
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  margin-top: 8px !important;
  z-index: 9999 !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  width: 300px !important;
  left: 0 !important;
  top: 100% !important;
}

.react-tel-input .country-list .country {
  padding: 8px 12px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.react-tel-input .country-list .country:hover {
  background-color: #f5f5f5 !important;
}

.react-tel-input .country-list .country.highlight {
  background-color: #e6f7ff !important;
}

/* Fix for dropdown arrow */
.react-tel-input .selected-flag .arrow {
  display: block !important;
  position: relative !important;
  left: 20px !important;
}

.react-tel-input .selected-flag .arrow.up {
  border-top: none !important;
  border-bottom: 4px solid #555 !important;
}

.phone-input-wrapper {
  position: relative;
  width: 100%;
}

/* Make sure dropdown appears above other elements */
.country-dropdown-list {
  position: absolute !important;
  z-index: 9999 !important;
  width: 300px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  background-color: white !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure the flag button is visible and clickable */
.react-tel-input .flag-dropdown {
  z-index: 1 !important;
}

/* Synchronize focus states between flag dropdown and input */
.phone-input-container .form-input:focus ~ .flag-dropdown,
.phone-input-container .form-input:hover ~ .flag-dropdown,
.react-tel-input .flag-dropdown.open {
  border-color: #40a9ff !important;
}

.phone-input-container .form-input:focus ~ .flag-dropdown {
  border-color: #1890ff !important;
}

/* Card styling for consistency */
.contact-modal .ant-card {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.contact-modal .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px;
  min-height: 40px;
}

.contact-modal .ant-card-head-title {
  font-size: 14px;
  font-weight: 400;
}

.contact-modal .ant-card-body {
  padding: 16px;
}

/* Select dropdown to match input styling */
.form-select .ant-select-selector {
  height: 40px !important;
  padding: 0 11px !important;
}

.form-select .ant-select-selection-search-input {
  height: 38px !important;
}

.form-select .ant-select-selection-item {
  line-height: 38px !important;
  font-weight: 400 !important;
}

/* Template Variables Card */
.contact-form .ant-card .ant-form-item-label > label {
  font-weight: 400 !important;
}

/* Message Preview */
.preview-card .ant-card-head-title {
  font-weight: 400 !important;
}

/* Select Placeholder */
.ant-select-selection-placeholder {
  font-weight: 400 !important;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .contact-modal {
    max-width: 90% !important;
  }

  .country-dropdown-list {
    width: 250px !important;
  }
}
