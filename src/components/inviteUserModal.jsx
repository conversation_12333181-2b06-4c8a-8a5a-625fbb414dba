import React, { useEffect, useState } from "react";
import {
  Modal,
  Input,
  Select,
  Button,
  Form,
  Row,
  Col,
  message,
  Space,
  Typography,
  Card,
  Divider,
} from "antd";
import {
  CheckCircleTwoTone,
  CopyOutlined,
  PlusOutlined,
  ShareAltOutlined,
  LinkOutlined,
} from "@ant-design/icons";
import { useAppDispatch } from "../hooks/reduxHooks";
import { userInvitationApi } from "../services/users.service";

const { Option } = Select;

const InviteUserModal = ({
  isUserInviteModalVisible,
  setIsUserInviteModalVisible,
}) => {
  const [username, setUsername] = useState("");
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState("");
  const [form] = Form.useForm();
  const [showInviteLink, setShowInviteLink] = useState(false);
  const [invitationURL, setInvitationURL] = useState("");
  const dispatch = useAppDispatch();

  const { Text, Title } = Typography;

  const roleOptions = ["admin", "agent"];

  useEffect(() => {
    if (isUserInviteModalVisible) {
      setUsername("");
      setRole("");
      setShowInviteLink(false);
      setInvitationURL("");
      form.resetFields();
    }
  }, [isUserInviteModalVisible]);

  const handleOk = (values) => {
    setLoading(true);
    dispatch(
      userInvitationApi({
        data: values,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response.success) {
            message.success(response?.msg || "Invitation link generated successfully!");

            const generatedURL = response?.registration_token
              ? `${window.location.origin}/invitation?token=${response?.registration_token}&username=${values?.username}&role=${values?.role}`
              : "";

            setInvitationURL(generatedURL);
            setShowInviteLink(true);
          } else {
            message.error(response?.msg || "Failed to invite user.");
          }
        },
        failureCallback: (error) => {},
      })
    );
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(invitationURL);
      message.success("Invitation link copied to clipboard!");
    } catch (err) {
      message.error("Failed to copy link. Please copy manually.");
    }
  };

  const handleShareLink = () => {
    if (navigator.share) {
      navigator.share({
        title: "User Invitation",
        text: "You've been invited to join our platform!",
        url: invitationURL,
      }).catch(() => {
        handleCopyLink(); // Fallback to copy
      });
    } else {
      handleCopyLink(); // Fallback to copy
    }
  };

  const handleClose = () => {
    setIsUserInviteModalVisible(false);
    setShowInviteLink(false);
    setInvitationURL("");
  };

  return (
    <div>
      <Modal
        title={
          <div style={{ textAlign: "center" }}>
            <Title level={3} style={{ margin: 0, fontWeight: "600" }}>
              {showInviteLink ? "Invitation Link Ready!" : "Invite a User"}
            </Title>
          </div>
        }
        onCancel={handleClose}
        width={showInviteLink ? 600 : 420}
        open={isUserInviteModalVisible}
        transitionName=""
        maskTransitionName=""
        footer={null}
        destroyOnClose
      >
        {!showInviteLink ? (
          // Invitation Form
          <Form form={form} layout="vertical" onFinish={handleOk}>
            <Form.Item
              label="Username/Email"
              name="username"
              style={{ marginTop: "24px", marginBottom: "16px" }}
              rules={[
                { required: true, message: "Please input the username!" },
                {
                  min: 3,
                  message: "Username must be at least 3 characters.",
                },
              ]}
            >
              <Input
                placeholder="Enter new agent username/email"
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="Role"
              name="role"
              rules={[{ required: true, message: "Please select a role!" }]}
              style={{ marginBottom: "24px" }}
            >
              <Select placeholder="Assign a role" size="large">
                {roleOptions.map((role) => (
                  <Option key={role} value={role}>
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Row gutter={12}>
                <Col span={12}>
                  <Button
                    onClick={handleClose}
                    size="large"
                    block
                  >
                    Cancel
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<PlusOutlined />}
                    loading={loading}
                    size="large"
                    block
                  >
                    Generate Invite
                  </Button>
                </Col>
              </Row>
            </Form.Item>
          </Form>
        ) : (
          // Invitation Link Display
          <div style={{ padding: "8px 0" }}>
            <div style={{ textAlign: "center", marginBottom: "24px" }}>
              <CheckCircleTwoTone
                twoToneColor="#52c41a"
                style={{ fontSize: "48px", marginBottom: "16px" }}
              />
              <Text style={{ fontSize: "16px", color: "#666" }}>
                Share this link with the user to complete their registration
              </Text>
            </div>

            <Card
              style={{
                marginBottom: "24px",
                border: "2px dashed #d9d9d9",
                backgroundColor: "#fafafa"
              }}
            >
              <Space direction="vertical" style={{ width: "100%" }}>
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <LinkOutlined style={{ color: "#1890ff" }} />
                  <Text strong>Invitation Link</Text>
                </div>
                <Text
                  copyable={{
                    text: invitationURL,
                    tooltips: ["Copy link", "Copied!"],
                  }}
                  style={{
                    fontSize: "14px",
                    wordBreak: "break-all",
                    padding: "12px",
                    background: "#fff",
                    borderRadius: "6px",
                    border: "1px solid #e8e8e8",
                    display: "block",
                  }}
                >
                  {invitationURL}
                </Text>
              </Space>
            </Card>

            <Row gutter={12}>
              <Col span={8}>
                <Button
                  onClick={handleClose}
                  size="large"
                  block
                >
                  Done
                </Button>
              </Col>
              <Col span={8}>
                <Button
                  icon={<CopyOutlined />}
                  onClick={handleCopyLink}
                  size="large"
                  block
                >
                  Copy Link
                </Button>
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  icon={<ShareAltOutlined />}
                  onClick={handleShareLink}
                  size="large"
                  block
                >
                  Share
                </Button>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default InviteUserModal;
