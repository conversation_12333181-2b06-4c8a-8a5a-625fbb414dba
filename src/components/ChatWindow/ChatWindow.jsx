import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Typo<PERSON>,
  Avatar,
  Space,
  Input,
  Button,
  Upload,
  Menu,
  Dropdown,
  message,
  Divider,
  Row,
  Col,
  Spin,
  Tooltip,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  SendOutlined,
  PaperClipOutlined,
  DeleteOutlined,
  MoreOutlined,
  CloseOutlined,
  DeleteTwoTone,
  PlusCircleOutlined,
  SmileFilled,
  FileImageFilled,
  PictureOutlined,
  LoadingOutlined,
  ReloadOutlined,
  PhoneOutlined,
  MailOutlined,
} from "@ant-design/icons";
import { styles } from "../../pages/playground/styles";
import { useAppDispatch } from "../../hooks/reduxHooks";
import {
  generateReplyApi,
  fetchChatHistory,
  chatDeleteAPI,
} from "../../services/playground.service";
import SingleChatItem from "../../pages/playground/sections/ChatWindowSection/SingleChatItem";
import { imageUploadApi } from "../../services/knowledgebase.service";
import "../../pages/playground/sections/ChatWindowSection/index.css";
import SingleChatItemChannel from "./SingleChatItemChannel";
import { sendAssistantMessageApi } from "../../services/channels.service";
import { transformString } from "../../helpers/channelNameTransformation";
import dayjs from "dayjs"; // Import dayjs for date handling
import { useNavigate } from "react-router-dom";
import { logout } from "../../store/slices/profile.slice.js";
import { checkBalance } from "../../helpers/checkBalance.js";

const { Text, Title, Paragraph } = Typography;
const { TextArea } = Input;

// Safe YouTube URL checker for SingleChatItemChannel component
// This function should be exported or copied to SingleChatItemChannel.jsx
const isYouTubeVideo = (url) => {
  // First check if url is a string
  if (!url || typeof url !== "string") {
    return false;
  }

  // Now safely check if it's a YouTube URL
  return url.includes("youtube.com") || url.includes("youtu.be");
};

// Date Separator Component
const DateSeparator = ({ date }) => {
  // Format the date appropriately
  const formatDate = (dateString) => {
    const today = dayjs().startOf("day");
    const yesterday = today.subtract(1, "day");
    const messageDate = dayjs(dateString).startOf("day");

    if (messageDate.isSame(today)) {
      return "Today";
    } else if (messageDate.isSame(yesterday)) {
      return "Yesterday";
    } else {
      // For older dates, format as "Jan 1, 2025"
      return messageDate.format("MMM D, YYYY");
    }
  };

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "16px 0",
        width: "100%",
      }}
    >
      <div
        style={{
          backgroundColor: "#f0f0f0",
          borderRadius: "16px",
          padding: "4px 12px",
          display: "inline-block",
        }}
      >
        <Text style={{ fontSize: "12px", color: "#666" }}>
          {formatDate(date)}
        </Text>
      </div>
    </div>
  );
};

const ChatWindowSection = ({
  customerInfo,
  responseData,
  setResponseData,
  setVisible,
  setDrawerData,
  selectedCustomerId,
  setRefreshCustomerList,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [spinning, setSpinning] = useState(false);
  const [loadingSendMsng, setLoadingSendMsng] = useState(false);

  // Loading state
  const [loading, setLoading] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [userInput, setUserInput] = useState("");
  const [fileList, setFileList] = useState([]);
  const [fileURL, setFileURL] = useState([]);
  const [imagePreview, setImagePreview] = useState([]);

  // Response page number metaData from response
  const [conversationsMeta, setConversationsMeta] = useState();

  // Reference to chat container for scrolling
  const chatContainerRef = useRef(null);

  // Store selectedCustomerId in a ref to ensure we always have the latest value
  const selectedCustomerIdRef = useRef(null);

  // New states for infinite scrolling
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const lastScrollPositionRef = useRef(0);

  // New state to track if user has intentionally scrolled
  const [hasScrolled, setHasScrolled] = useState(false);

  // Update the ref whenever selectedCustomerId changes
  useEffect(() => {
    selectedCustomerIdRef.current = selectedCustomerId;
  }, [selectedCustomerId]);

  useEffect(() => {
    if (selectedCustomerId) {
      setSpinning(true);
      fetchChatHis();
    }
  }, [selectedCustomerId]);

  // Scroll to bottom whenever conversations change, but only if we're not loading more at the top
  useEffect(() => {
    if (shouldScrollToBottom) {
      scrollToBottom();
    }
  }, [conversations, shouldScrollToBottom]);

  // Track which page is currently being loaded to prevent duplicate requests
  const loadingPageRef = useRef(null);

  // Add scroll event listener with debounce to prevent multiple rapid calls
  useEffect(() => {
    // Debounce function to prevent multiple calls in quick succession
    let scrollTimeout = null;

    const handleScroll = () => {
      const container = chatContainerRef.current;
      if (!container) return;

      // Store the current scroll position
      const currentScrollTop = container.scrollTop;

      // Check if user is scrolling up (away from the bottom)
      if (
        currentScrollTop <
        container.scrollHeight - container.clientHeight - 50
      ) {
        setHasScrolled(true);
      } else {
        // If user is at the bottom of the chat, they're not trying to view older messages
        setHasScrolled(false);
      }

      lastScrollPositionRef.current = currentScrollTop;

      // Clear any existing timeout to debounce the scroll event
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Set a new timeout to handle the scroll event
      scrollTimeout = setTimeout(() => {
        // Check if user has scrolled to the top (with a small threshold)
        if (currentScrollTop <= 30 && !isLoadingMore && hasMore) {
          // Make sure we're not already loading the next page
          const nextPage = page + 1;
          if (loadingPageRef.current !== nextPage) {
            loadMoreConversations();
          }
        }
      }, 250); // 250ms debounce
    };

    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
      // Clear the timeout on cleanup
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [isLoadingMore, hasMore, page, selectedCustomerId]); // Added selectedCustomerId to dependencies

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  const loadMoreConversations = () => {
    // Get the latest selectedCustomerId from the ref
    const currentCustomerId = selectedCustomerIdRef.current;
    const nextPage = page + 1;

    // Add safeguard to prevent API call if customer ID is missing or if already loading this page
    if (
      isLoadingMore ||
      !hasMore ||
      !currentCustomerId ||
      loadingPageRef.current === nextPage
    ) {
      console.log("Skipping loadMoreConversations:", {
        isLoadingMore,
        hasMore,
        currentCustomerId,
        currentlyLoadingPage: loadingPageRef.current,
        attemptedPage: nextPage,
      });
      return;
    }

    // Mark this page as currently being loaded to prevent duplicate requests
    loadingPageRef.current = nextPage;

    // Disable auto-scroll when loading more
    setShouldScrollToBottom(false);
    setIsLoadingMore(true);

    // Save the current scroll height to restore position later
    const scrollHeight = chatContainerRef.current?.scrollHeight || 0;

    dispatch(
      fetchChatHistory({
        params: {
          user_id: currentCustomerId, // Use the ref value here
          page_number: nextPage,
        },
        finalCallback: () => {
          setIsLoadingMore(false);
          // Clear the loading page reference when done
          loadingPageRef.current = null;

          // After new content is loaded, maintain the scroll position
          setTimeout(() => {
            if (chatContainerRef.current) {
              const newScrollHeight = chatContainerRef.current.scrollHeight;
              const heightDifference = newScrollHeight - scrollHeight;
              chatContainerRef.current.scrollTop =
                heightDifference > 0 ? heightDifference : 0;
            }
          }, 50);
        },
        successCallback: (response) => {
          if (!response.responseData || response.responseData.length === 0) {
            setHasMore(false);
            return;
          }

          setConversations((prevConversations) => {
            return [...response.responseData, ...prevConversations];
          });

          setPage(nextPage);
        },
        failureCallback: (error) => {
          console.error("Error loading more conversations:", error);
          setHasMore(false);
        },
      })
    );
  };

  const fetchChatHis = () => {
    // Make sure we have a valid customer ID
    if (!selectedCustomerIdRef.current) {
      console.log("No customer ID available for fetchChatHis");
      setSpinning(false);
      setLoading(false);
      return;
    }

    // Reset pagination data when fetching initial history
    setPage(1);
    setHasMore(true);
    setShouldScrollToBottom(true);
    setHasScrolled(false); // Reset scrolled state when fetching chat history

    dispatch(
      fetchChatHistory({
        params: {
          user_id: selectedCustomerIdRef.current,
          page_number: 1,
        },
        finalCallback: () => {
          setSpinning(false);
          setLoading(false);
          // Ensure scroll to bottom after loading conversations
          setTimeout(scrollToBottom, 100);
        },
        successCallback: (response) => {
          setConversations(response?.responseData || []);
          setConversationsMeta(response?.responseMeta);

          // Check if there might be more data to load
          if (!response?.responseData || response.responseData.length === 0) {
            setHasMore(false);
          }
        },
        failureCallback: (error) => {
          console.log("Error:", error);
          setHasMore(false);
        },
      })
    );
  };

  const sendMessage = (urls) => {
    // Check if we have a valid customer ID
    if (!selectedCustomerIdRef.current) {
      message.error("Cannot send message: Customer ID is missing");
      setLoadingSendMsng(false);
      return;
    }

    const newUrls = urls?.map((item) => item?.url);

    // When sending a message, we always want to scroll to bottom
    setShouldScrollToBottom(true);
    setHasScrolled(false); // Reset scroll state when sending a new message

    // Create new user message
    const newUserMessage = {
      role: "assistant",
      content: userInput,
      created_at: new Date().toISOString(),
      reply_urls: newUrls,
    };

    // Update conversations
    setConversations((prev) => {
      const updatedConversations = [...prev];
      if (updatedConversations?.length > 0) {
        updatedConversations[updatedConversations?.length - 1].push(
          newUserMessage
        );
      } else {
        updatedConversations.push([newUserMessage]);
      }
      return updatedConversations;
    });

    // Reset input and image preview
    setUserInput("");
    setImagePreview([]);

    const data = {
      user_id: selectedCustomerIdRef.current,
      content: userInput,
      media_url: urls,
      channel:
        customerInfo?.channel.charAt(0).toLowerCase() +
        customerInfo?.channel.slice(1),
    };

    dispatch(
      sendAssistantMessageApi({
        data: data,
        finalCallback: () => {
          setRefreshCustomerList(true);
          setLoadingSendMsng(false);
        },
        successCallback: (response) => {
          console.log("RESPONSE sendAssistantMessageApi -> ", response);
        },
        failureCallback: (error) => {
          message.error(
            error.response.data.detail.message || "Failed to send message"
          );
        },
      })
    );
  };

  const handleSendMessage = async () => {
    const balance = await checkBalance();

    if (balance < 1) {
      // Show an error message when balance is zero
      message.error(
        "You don't have enough credits. Please recharge your account."
      );
      return;
    }

    if (!userInput?.trim() && (!fileList || fileList?.length === 0)) return;

    // Check if we have a valid customer ID
    if (!selectedCustomerIdRef.current) {
      message.error("Cannot send message: Customer ID is missing");
      return;
    }

    console.log("imagePreview", fileList);
    setLoadingSendMsng(true);
    console.log("User Input -> ", userInput?.trim());

    if (fileList && fileList?.length > 0) {
      const formData = new FormData();
      fileList.forEach((file) => {
        formData.append("files", file.originFileObj);
      });

      dispatch(
        imageUploadApi({
          data: formData,
          params: { source: "test" },
          finalCallback: () => {
            // setLoadingSendMsng(false);
          },
          successCallback: (response) => {
            const urls = response?.data?.map((item) => item);
            setFileURL(urls);
            sendMessage(urls); // Now pass the uploaded image URLs
            setFileList([]);
          },
          failureCallback: (error) => {
            console.error("Image Upload Error", error);
            message.error("Failed to upload images. Please try again.");
            setLoadingSendMsng(false);
          },
        })
      );
    } else {
      sendMessage([]);
    }
  };

  // Handle file change
  const handleFileChange = ({ fileList }) => {
    const file = fileList[fileList?.length - 1]; // Get last selected file

    if (file && file.originFileObj) {
      const validExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      const fileExtension = file.name.split(".").pop().toLowerCase();

      if (validExtensions.includes(fileExtension)) {
        console.log("fileList", fileList);
        setFileList(fileList);
        const newPreviews = [];

        fileList.forEach((file) => {
          if (file.originFileObj) {
            const reader = new FileReader();
            reader.onload = (event) => {
              newPreviews.push(event.target.result);

              // Only update state once after all files are processed
              if (newPreviews?.length === fileList?.length) {
                setImagePreview(newPreviews);
              }
            };
            reader.readAsDataURL(file.originFileObj);
          }
        });
      } else {
        message.error(
          "Only image files (jpg, jpeg, png, gif, bmp, webp) are allowed."
        );
        setFileList(
          fileList.filter((file) =>
            validExtensions.includes(file.name.split(".").pop().toLowerCase())
          )
        );
      }
    } else {
      message.error("File upload failed. Please try again.");
    }
  };

  // Remove an image preview
  const removeImagePreview = (index) => {
    setFileList((prevFileList) =>
      prevFileList.filter((_, idx) => idx !== index)
    );
    setImagePreview((prevState) => prevState.filter((_, idx) => idx !== index));
  };

  // Render messages with date separators
  const renderMessagesWithDateSeparators = (conversations) => {
    if (!conversations || conversations.length === 0) return [];

    const result = [];
    let currentDate = null;

    conversations.flatMap((conversation, convIndex) => {
      const sortedMessages = [...conversation].sort((a, b) => {
        const timeA = new Date(a.created_at || 0).getTime();
        const timeB = new Date(b.created_at || 0).getTime();
        return timeA - timeB;
      });

      sortedMessages.forEach((message, msgIndex) => {
        // Skip messages without created_at timestamp
        if (!message.created_at) {
          result.push(
            <SingleChatItemChannel
              key={`msg-no-date-${msgIndex}-${convIndex}`}
              message={message}
              convIndex={convIndex}
              msgIndex={msgIndex}
              setVisible={setVisible}
              setResponseData={setResponseData}
              setDrawerData={setDrawerData}
              isYouTubeVideo={isYouTubeVideo} // Pass the safe function to the component
              customerInfo={customerInfo}
              fetchChatHis={fetchChatHis}
              setRefreshCustomerList={setRefreshCustomerList}
            />
          );
          return;
        }

        // Get message date without time (start of day)
        const messageDate = dayjs(message.created_at).startOf("day");

        // If this is a new date, add a separator
        if (!currentDate || !messageDate.isSame(currentDate)) {
          currentDate = messageDate;
          result.push(
            <DateSeparator
              key={`date-${message.created_at}-${convIndex}-${msgIndex}`}
              date={message.created_at}
            />
          );
        }

        // Add the message
        result.push(
          <SingleChatItemChannel
            key={`msg-${message.created_at}-${msgIndex}-${convIndex}`}
            message={message}
            convIndex={convIndex}
            msgIndex={msgIndex}
            setVisible={setVisible}
            setResponseData={setResponseData}
            setDrawerData={setDrawerData}
            isYouTubeVideo={isYouTubeVideo} // Pass the safe function to the component
            customerInfo={customerInfo}
            fetchChatHis={fetchChatHis}
            setRefreshCustomerList={setRefreshCustomerList}
          />
        );
      });
    });

    return result;
  };

  return (
    <div
      style={{
        height: "100%",
        width: "100%",
        marginLeft: "20px",
      }}
    >
      <Card
        bodyStyle={{ padding: 0, height: "100%" }}
        style={{
          minHeight: "91vh",
          display: "flex",
          flexDirection: "column",
          width: "100%",
        }}
        title={
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <div
              style={{
                display: "flex",
                gap: "12px",
              }}
            >
              <Title level={4} style={{ margin: 0 }}>
                {customerInfo?.customer_name ?? (
                  <span style={{ color: "#999999" }}>N/A</span>
                )}
              </Title>

              {(customerInfo?.channel === "Website" && customerInfo?.email) ||
              (customerInfo?.channel === "Whatsapp" &&
                customerInfo?.customer_id) ? (
                <Tooltip
                  title={
                    customerInfo?.channel === "Whatsapp" ? (
                      <div
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                          navigator.clipboard.writeText(
                            customerInfo.customer_id
                          );
                          message.success("Phone number copied to clipboard!");
                        }}
                      >
                        {"+" + customerInfo?.customer_id || ""}
                        <span
                          style={{
                            marginLeft: "8px",
                            fontSize: "12px",
                            color: "#1890ff",
                          }}
                        >
                          (Click to copy)
                        </span>
                      </div>
                    ) : (
                      customerInfo?.channel === "Website" && (
                        <div
                          style={{ cursor: "pointer" }}
                          onClick={() => {
                            navigator.clipboard.writeText(customerInfo.email);
                            message.success(
                              "Email address copied to clipboard!"
                            );
                          }}
                        >
                          {customerInfo?.email || ""}
                          <span
                            style={{
                              marginLeft: "8px",
                              fontSize: "12px",
                              color: "#1890ff",
                            }}
                          >
                            (Click to copy)
                          </span>
                        </div>
                      )
                    )
                  }
                  color="#ffffff"
                  overlayInnerStyle={{
                    color: "#000000",
                    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                  }}
                >
                  <Button
                    type="default"
                    icon={
                      customerInfo?.channel === "Whatsapp" ? (
                        <PhoneOutlined />
                      ) : (
                        customerInfo?.channel === "Website" && <MailOutlined />
                      )
                    }
                    onClick={() => {
                      if (customerInfo?.channel === "Whatsapp") {
                        navigator.clipboard.writeText(
                          customerInfo?.customer_id || ""
                        );
                        message.success("Phone number copied to clipboard!");
                      } else if (customerInfo?.channel === "Website") {
                        navigator.clipboard.writeText(
                          customerInfo?.email || ""
                        );
                        message.success("Email address copied to clipboard!");
                      } else {
                        message.info("No phone number or email available");
                      }
                    }}
                  />
                </Tooltip>
              ) : (
                <></>
              )}
            </div>

            <Button
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchChatHis();
              }}
              disabled={loading}
            />
          </div>
        }
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            height: "100%",
          }}
        >
          <div
            ref={chatContainerRef}
            style={{
              height: "calc(100vh - 250px)",
              maxHeight: "100vh",
              overflowY: "auto",
              width: "95%",
              padding: "16px",
              display: "flex",
              flexDirection: "column",
              paddingBottom: "20px",
            }}
          >
            {/* Loading indicator at the top when fetching more conversations */}
            {hasMore && (
              <div
                style={{
                  textAlign: "center",
                  height: "30px",
                  marginBottom: "10px",
                }}
              >
                <Spin
                  indicator={<LoadingOutlined spin />}
                  spinning={isLoadingMore}
                />
              </div>
            )}

            {/* "Conversation ends here" only shows if:
                1. There are no more messages to load (hasMore is false)
                2. There are actual conversations to display
                3. User has scrolled up (hasScrolled is true)
                4. User is near the top (lastScrollPositionRef.current <= 100) */}
            {!hasMore &&
              conversations.length > 0 &&
              hasScrolled &&
              lastScrollPositionRef.current <= 100 && (
                <div
                  style={{
                    textAlign: "center",
                    padding: "12px",
                    color: "#888",
                    fontSize: "13px",
                    borderRadius: "8px",
                    margin: "10px 0 20px 0",
                    boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "8px",
                  }}
                >
                  <div
                    style={{
                      height: "1px",
                      backgroundColor: "#ddd",
                      width: "40px",
                    }}
                  />
                  <span>Conversation ends here</span>
                  <div
                    style={{
                      height: "1px",
                      backgroundColor: "#ddd",
                      width: "40px",
                    }}
                  />
                </div>
              )}

            {/* Render messages with date separators */}
            {renderMessagesWithDateSeparators(conversations)}
          </div>

          <div
            style={{
              position: "fixed",
              bottom: "30px",
              zIndex: 999,
              width: "50%",
              display: "flex",
              justifyContent: "center",
              padding: "10px",
            }}
          >
            {hasScrolled && (
              <Button
                type="primary"
                shape="circle"
                icon={<span style={{ fontSize: 18 }}>↓</span>}
                onClick={scrollToBottom}
                aria-label="Scroll to bottom"
                style={{
                  position: "fixed",
                  left: "66%",
                  transform: "translateX(-50%)",
                  bottom: "120px", // Adjust this value as needed

                  zIndex: 1000,
                  paddingBottom: 10,
                }}
                className="back-to-top-button"
              />
            )}
            <div
              style={{
                width: "100%",
              }}
            >
              {/* Image Preview Section */}
              {imagePreview?.length > 0 && (
                <div
                  style={{
                    display: "flex",
                    gap: "8px",
                    flexWrap: "wrap",
                    justifyContent: "left",
                    backgroundColor: "#f9f9f9",
                    marginBottom: "8px",
                  }}
                >
                  {imagePreview?.map((image, index) => (
                    <div key={index} style={{ position: "relative" }}>
                      <img
                        src={image}
                        alt={`Preview ${index}`}
                        style={{
                          width: "80px",
                          height: "80px",
                          borderRadius: "8px",
                          border: "solid 1px #E5E7EB",
                        }}
                      />
                      <Button
                        type="text"
                        icon={<DeleteOutlined style={{ color: "red" }} />}
                        onClick={() => removeImagePreview(index)}
                        style={{
                          position: "absolute",
                          top: "-10px",
                          right: "-10px",
                          zIndex: 1,
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Chat Input Section (Full Width) */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginBottom: "8px",
                }}
              >
                <div
                  style={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    gap: "10px",
                    padding: "12px",
                    backgroundColor: "white",
                    borderRadius: "12px",
                    border: "1px solid rgba(102, 101, 101, 0.24)",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                  }}
                >
                  {/* Upload Button */}
                  <Upload
                    fileList={fileList}
                    onChange={handleFileChange}
                    beforeUpload={() => false}
                    showUploadList={false}
                    multiple
                  >
                    <Button icon={<PlusCircleOutlined />} type="text" />
                  </Upload>

                  {/* Message Input (Expands Fully) */}
                  <TextArea
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="Type your message..."
                    autoSize={{ minRows: 1, maxRows: 3 }}
                    onPressEnter={(e) => {
                      if (!e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    disabled={loading || !selectedCustomerIdRef.current}
                    style={{
                      flex: 1,
                      border: "none",
                      outline: "none",
                      background: "transparent",
                      width: "100%", // Ensures full width usage
                    }}
                  />

                  {/* Send Button */}
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSendMessage}
                    loading={loadingSendMsng}
                    disabled={!selectedCustomerIdRef.current}
                  >
                    Send
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChatWindowSection;
