import { useEffect, useState } from "react";
import {
  Card,
  Empty,
  Button,
  message,
  Tooltip,
  Pagination,
  Spin,
  Popconfirm,
  Typography,
} from "antd";
import {
  FilePdfOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
  InfoCircleOutlined,
} from "@ant-design/icons";
import httpBase from "../../utils/http.utils";
const { Title, Text } = Typography;

const ExistingFiles = ({
  existingFiles,
  page,
  loading,
  limit,
  totalFiles,
  setPage,
  fetchExistingFiles,
}) => {
  const handlePreview = async (file) => {
    try {
      const response = await httpBase().get(`/get_doc/Files/${file}`, {
        responseType: "json",
      });
      if (response.data && response.data.presigned_url) {
        window.open(response?.data?.presigned_url, "_blank");
      } else {
        console.error("Presigned URL not found in the response.");
      }
    } catch (error) {
      console.error("Error fetching file:", error);
      message.error("Failed to open file.");
    }
  };

  const handleDelete = async (fileName) => {
    try {
      const response = await httpBase().delete(`/delete_file/${fileName}`);
      if (response.status === 200) {
        message.success("File deleted successfully");
        fetchExistingFiles(page);
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      message.error("Failed to delete file");
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Get file extension
  const getFileExtension = (filename) => {
    return filename
      .slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2)
      .toUpperCase();
  };

  // Generate a color based on file extension
  const getColorForExtension = (extension) => {
    const colors = {
      PDF: "#e74c3c",
      DOC: "#3498db",
      DOCX: "#3498db",
      XLS: "#2ecc71",
      XLSX: "#2ecc71",
      TXT: "#95a5a6",
      CSV: "#f39c12",
      JPG: "#9b59b6",
      JPEG: "#9b59b6",
      PNG: "#1abc9c",
    };

    return colors[extension] || "#34495e";
  };

  return (
    <Card
      style={{
        // marginTop: 5,
        borderRadius: "12px",
        boxShadow: "0 6px 16px rgba(0, 0, 0, 0.08)",
        overflow: "hidden",
        border: "none",
      }}
      bodyStyle={{
        padding: "0",
      }}
    >
      <div style={{ padding: "20px" }}>
        {loading ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "60px 0",
              gap: "16px",
            }}
          >
            <Spin size="large" />
            <span style={{ color: "#6c757d", marginTop: "12px" }}>
              Loading your files...
            </span>
          </div>
        ) : existingFiles.length > 0 ? (
          <>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
                gap: "20px",
                padding: "8px 0 24px",
              }}
            >
              {existingFiles.map((file) => {
                const fileExt = getFileExtension(file);
                const colorCode = getColorForExtension(fileExt);

                return (
                  <Card
                    key={file}
                    hoverable
                    style={{
                      borderRadius: "8px",
                      overflow: "hidden",
                      height: "100%",
                      transition: "all 0.2s ease",
                      border: "1px solid #eaeaea",
                    }}
                    bodyStyle={{
                      padding: "0",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      style={{
                        background: `${colorCode}10`,
                        padding: "20px 16px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        borderBottom: "1px solid #eaeaea",
                      }}
                    >
                      <div
                        style={{
                          width: "64px",
                          height: "70px",
                          borderRadius: "8px",
                          background: "white",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                          position: "relative",
                        }}
                      >
                        <FilePdfOutlined
                          style={{
                            fontSize: "28px",
                            color: colorCode,
                          }}
                        />
                        <div
                          style={{
                            position: "absolute",
                            bottom: "-6px",
                            right: "-6px",
                            background: colorCode,
                            color: "white",
                            fontSize: "10px",
                            padding: "2px 6px",
                            borderRadius: "4px",
                            fontWeight: "bold",
                          }}
                        >
                          {fileExt}
                        </div>
                      </div>
                    </div>

                    <div style={{ padding: "16px", flex: 1 }}>
                      <Tooltip title={file} placement="top">
                        <div
                          style={{
                            fontWeight: "500",
                            color: "#333",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            marginBottom: "16px",
                          }}
                        >
                          {file.length > 20
                            ? file.substring(0, 17) + "..."
                            : file}
                        </div>
                      </Tooltip>

                      <div
                        style={{
                          display: "flex",
                          gap: "8px",
                          marginTop: "auto",
                        }}
                      >
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => handlePreview(file)}
                          type="primary"
                          style={{
                            borderRadius: "6px",
                            flex: 1,
                            boxShadow: "none",
                            backgroundColor: colorCode,
                            borderColor: colorCode,
                          }}
                        >
                          View
                        </Button>

                        <Popconfirm
                          title={
                            <div style={{ maxWidth: "220px" }}>
                              <div
                                style={{
                                  fontWeight: "500",
                                  marginBottom: "8px",
                                }}
                              >
                                Delete this file?
                              </div>
                              <div
                                style={{
                                  color: " #e74c3c",
                                  fontSize: "12px",
                                  fontWeight: "500",
                                }}
                              >
                                This will remove all related knowledge base
                                entries.
                              </div>
                            </div>
                          }
                          onConfirm={() => handleDelete(file)}
                          okText="Yes"
                          cancelText="No"
                          placement="topRight"
                          okButtonProps={{ danger: true }}
                        >
                          <Button
                            icon={<DeleteOutlined />}
                            danger
                            style={{
                              borderRadius: "6px",
                            }}
                          />
                        </Popconfirm>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                marginTop: "16px",
                borderTop: "1px solid #f0f0f0",
                paddingTop: "20px",
              }}
            >
              <Pagination
                current={page}
                pageSize={limit}
                total={totalFiles}
                onChange={handlePageChange}
                showSizeChanger={false}
              />
            </div>
          </>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            imageStyle={{ height: 60 }}
            description={
              <div>
                <p style={{ fontSize: "16px", margin: "8px 0" }}>
                  No files in your knowledge base yet
                </p>
                <p style={{ fontSize: "14px", color: "#6c757d" }}>
                  Upload files to start building your knowledge base
                </p>
              </div>
            }
            style={{ padding: "60px 20px" }}
          />
        )}
      </div>
    </Card>
  );
};

export default ExistingFiles;
