import React, { useEffect, useRef, useState } from "react";
import {
  Upload,
  Button,
  message,
  Card,
  Progress,
  Tooltip,
  Empty,
  Spin,
} from "antd";
import {
  UploadOutlined,
  FileOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  FilePdfOutlined,
  EyeOutlined,
  LeftOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { uploadFileApi } from "../../services/setup.service";
import httpBase from "../../utils/http.utils";
import "./index.css";
import ScrollableList from "./ScrollableList";
import ExistingFiles from "./ExistingFiles";
import {
  retrieveDocumentsApi,
  retrieveTocApi,
} from "../../services/knowledgebase.service";
import { useNavigate } from "react-router-dom";
import { logout } from "../../store/slices/profile.slice";

const statusIcons = {
  Pending: <ClockCircleOutlined style={{ color: "gray" }} />,
  Executing: <SyncOutlined spin style={{ color: "blue" }} />,
  Completed: <CheckCircleOutlined style={{ color: "green" }} />,
};

// const MultiFileUpload = ({ url, setResponseData }) => {
const MultiFileUpload = ({
  endpointResponse,
  setEndpointResponse,
  setTocList,
  simulateData,
  uploadStatus,
  setUploadStatus,
  showExistingFiles,
  setShowExistingFiles,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const wsRef = useRef(null);

  const [fileList, setFileList] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);

  // .........CHECK
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  // .........

  const [page, setPage] = useState(1);
  const [totalFiles, setTotalFiles] = useState(0);
  const [loading, setLoading] = useState(false);
  const [limit] = useState(5);

  const fetchToc = () => {
    dispatch(
      retrieveTocApi({
        finalCallback: () => {},
        successCallback: (response) => {
          setTocList(response);
        },
        failureCallback: () => {},
      })
    );
  };

  const fetchDocuments = () => {
    dispatch(
      retrieveDocumentsApi({
        data: {
          collection_name: "test_page_info",
          limit: 10,
          filter: [{}],
        },
        finalCallback: () => {},
        successCallback: (response) => {
          setEndpointResponse(response.response);
        },
        failureCallback: () => {},
      })
    );
  };

  useEffect(() => {
    fetchExistingFiles();
    fetchToc();
    fetchDocuments();
    return () => {
      // Cleanup WebSocket on component unmount
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [page]);

  const fetchExistingFiles = async (currentPage) => {
    setLoading(true);
    try {
      const response = await httpBase().get(
        `/list_docs?prefix=Files&page=${currentPage}&limit=${limit}`
      );
      if (response.status === 200) {
        setExistingFiles(response.data.files);
        // setExistingFiles();
        setTotalFiles(response.data.total_files);
        setShowExistingFiles(true);
      }
    } catch (error) {
      console.error("Error fetching files:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = () => {
    setUploading(true);
    const formData = new FormData();
    fileList.forEach((file) => {
      formData.append("files", file);
    });

    try {
      // Use the WebSocket base URL for consistency
      const ws = new WebSocket(
        `${
          process.env.VITE_WS_BASE_URL
        }/setup_files?token=${localStorage.getItem("authToken")}`
      );

      wsRef.current = ws;

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setUploadStatus(data);

        const isCompleted = data["Finishing Up"] === "Completed";
        const hasError = Object.values(data).some((status) =>
          status.includes("Error")
        );

        if (isCompleted || hasError) {
          ws.close();
          setUploading(false);
          if (isCompleted) {
            message.success("Processing completed successfully!");
            setFileList([]);
            fetchExistingFiles();
            fetchToc();
            fetchDocuments();
          }
          if (hasError) {
            message.error("An error occurred during processing.");
          }
        }
      };

      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        message.error("Connection error occurred.");
        setUploading(false);
      };

      // Use the same base URL for HTTP requests
      ws.onopen = async () => {
        try {
          const response = await httpBase().post(
            "/setup_files", // Use the same endpoint as WebSocket
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                accept: "application/json",
              },
            }
          );

          if (response.status !== 200) {
            throw new Error("Upload failed");
          }
        } catch (error) {
          console.error("Error during upload:", error);
          message.error("Upload failed. Please try again.");
          ws.close();
          setUploading(false);
        }
      };
    } catch (error) {
      console.error("Error setting up connection:", error);
      message.error("Failed to establish connection.");
      setUploading(false);
    }
  };

  const props = {
    multiple: true,
    showUploadList: false, // Hide default list as we'll create our own

    onRemove(file) {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },

    beforeUpload: (file) => {
      // setFileList([...fileList, file]);
      setFileList((prevFileList) => [...prevFileList, file]);
      return false;
    },
    fileList,
  };

  const getFileIcon = (file) => {
    if (file.status === "done") {
      return (
        <CheckCircleOutlined style={{ color: "#52c41a", fontSize: "20px" }} />
      );
    }
    return <FileOutlined style={{ color: "#1890ff", fontSize: "20px" }} />;
  };

  // ...................................
  const renderUploadStatus = () => {
    if (!uploadStatus) return null;

    const totalSteps = Object.keys(uploadStatus).length;
    const completedSteps = Object.values(uploadStatus).filter(
      (status) => status === "Completed"
    ).length;
    const progress = Math.round((completedSteps / totalSteps) * 100);

    return (
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            {/* <Button
              type="text"
              icon={<LeftOutlined />}
              onClick={() => setUploadStatus(null)}
            /> */}
            <div
              style={{
                flex: 1,
                textAlign: "center",
              }}
            >
              <span
                style={{
                  color: "#444444",
                  textAlign: "center",
                  fontSize: "24px",
                }}
              >
                Upload Progress
              </span>
            </div>
          </div>
        }
        style={{ width: "100%" }}
      >
        <Progress
          percent={progress}
          status={
            Object.values(uploadStatus).some((status) =>
              status.includes("Error")
            )
              ? "exception"
              : completedSteps === totalSteps
              ? "success"
              : "active"
          }
        />
        {Object.entries(uploadStatus).map(([step, status]) => {
          let icon = statusIcons[status] || (
            <LoadingOutlined style={{ color: "gray" }} />
          );
          if (status.includes("Error")) {
            icon = <CloseCircleOutlined style={{ color: "red" }} />;
          }

          return (
            <div key={step} className="status-item">
              <span className="status-text">{step}:</span>
              <span className={`status ${status.toLowerCase()}`}>{status}</span>
              <span className="status-icon">{icon}</span>
            </div>
          );
        })}
      </Card>
    );
  };

  return (
    <>
      {!uploadStatus ? (
        <div
          style={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <div
            style={{
              textAlign: "center",
              marginBottom: "24px",
              marginTop: "32px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flexDirection: "column",
              gap: "12px",
            }}
          >
            <Upload {...props}>
              <Button
                size="large"
                style={{
                  width: "64px",
                  height: "64px",
                  fontSize: "24px",
                  borderRadius: "50%",
                }}
              >
                <UploadOutlined />
              </Button>
            </Upload>
            <p style={{ color: "#666" }}>Select multiple documents to upload</p>
          </div>

          <div style={{ marginBottom: "12px" }}>
            {fileList.map((file) => (
              <div
                key={file.uid}
                style={{
                  padding: "12px",
                  marginBottom: "8px",
                  background: "#fff",
                  borderRadius: "6px",
                  border: "1px solid #f0f0f0",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  minWidth: "400px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  {getFileIcon(file)}
                  <div>
                    <div style={{ fontWeight: 500 }}>{file.name}</div>
                    <div style={{ fontSize: "12px", color: "#666" }}>
                      {(file.size / 1024).toFixed(1)} KB
                    </div>
                  </div>
                </div>

                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                  }}
                >
                  {file.status === "uploading" && (
                    <Progress
                      percent={Math.round(file.percent)}
                      size="small"
                      style={{ width: "100px", margin: 0 }}
                    />
                  )}
                  <DeleteOutlined
                    onClick={() => props.onRemove(file)}
                    style={{
                      color: "#ff4d4f",
                      fontSize: "16px",
                      cursor: "pointer",
                    }}
                  />
                </div>
              </div>
            ))}
          </div>

          <div
            style={{
              border: "2px dashed #d9d9d9",
              borderRadius: "8px",
              padding: "20px",
              background: "#fafafa",
              textAlign: "center",
              marginBottom: "20px",
              minWidth: "600px",
            }}
          >
            <Button
              type="primary"
              size="large"
              style={{
                height: "48px",
                padding: "0 32px",
                fontSize: "16px",
                minWidth: "300px",
              }}
              disabled={fileList.length === 0}
              onClick={handleUpload}
            >
              Upload
            </Button>
            <p style={{ marginTop: "8px", color: "#666" }}>
              Upload documents inorder to generate a knowledge base
            </p>
          </div>

          {/* {showExistingFiles && (
            <ExistingFiles
              existingFiles={existingFiles}
              page={page}
              loading={loading}
              limit={limit}
              totalFiles={totalFiles}
              setPage={setPage}
            />
          )} */}

          {showExistingFiles && existingFiles?.length > 0 && (
            <ExistingFiles
              existingFiles={existingFiles}
              page={page}
              loading={loading}
              limit={limit}
              totalFiles={totalFiles}
              setPage={setPage}
              fetchExistingFiles={fetchExistingFiles}
            />
          )}
        </div>
      ) : (
        <div
          style={{
            height: "100%",
            display: "flex",
            justifyContent: "center",
          }}
        >
          {!endpointResponse ? (
            <>
              {/* Show upload progress only when uploadStatus is available */}
              {uploadStatus && renderUploadStatus()}
            </>
          ) : (
            <>
              <Card style={{ width: "100%" }}>
                <ScrollableList
                  listData={
                    endpointResponse?.documents ??
                    endpointResponse?.source_nodes ??
                    endpointResponse
                  }
                  setUploadStatus={setUploadStatus}
                  setShowExistingFiles={setShowExistingFiles}
                  simulateData={simulateData}
                />
              </Card>
            </>
          )}
        </div>
      )}
    </>
  );
};

export default MultiFileUpload;
