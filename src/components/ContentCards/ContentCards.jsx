// src/components/ContentCards/ContentCards.jsx
import React, { useState } from "react";
import { Row, Col, Card, List, Avatar, Spin } from "antd";
import { UserOutlined } from "@ant-design/icons";

const ContentCards = ({ conversations, loading }) => {
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const handleCustomerClick = (customerID) => {
    setSelectedCustomer(customerID);
  };

  return (
    <Row gutter={[2, 2]}>
      {/* Customers List */}
      <Col lg={8} md={6} sm={24}>
        <Card title="Customers">
          {loading ? (
            <Spin />
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={conversations}
              renderItem={(item) => (
                <List.Item
                  onClick={() => handleCustomerClick(item)}
                  style={{ cursor: "pointer" }}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={item.name}
                    description={`Messages: ${item.messages.length}`}
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      </Col>

      {/* Messages Display */}
      <Col lg={16} md={18} sm={24}>
        <Card title="Messages">
          {selectedCustomer ? (
            <List
              dataSource={selectedCustomer.messages}
              renderItem={(message) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={
                          message.sender === "customer" ? (
                            <UserOutlined />
                          ) : (
                            <UserOutlined />
                          )
                        }
                      />
                    }
                    title={`${message.sender} at ${new Date(
                      message.timestamp
                    ).toLocaleString()}`}
                    description={message.text}
                  />
                </List.Item>
              )}
            />
          ) : (
            <p>Select a customer to view messages</p>
          )}
        </Card>
      </Col>
    </Row>
  );
};

export default ContentCards;
