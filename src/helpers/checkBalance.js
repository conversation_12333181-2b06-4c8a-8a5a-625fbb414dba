import httpBase from "../utils/http.utils";

export const checkBalance = async () => {
  try {
    const response = await httpBase().get("/credits/balance");
    console.log("response -> ", response);
    if (!response.statusText === "OK") {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    // console.log("data -> ", response.data.balance.remaining);
    return response.data.balance.remaining;
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
