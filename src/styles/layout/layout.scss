@use "./header" as *;

html,
body {
  height: 100%;
}

#app-layout {
  min-height: 100vh;
}

.public-layout {
  display: flex;
  align-items: center;
  justify-content: center;
}

.protected-layout {
  &__content {
    // background-color: "#f5f5f5";
    // background-color: "#f8f4f1";
    // background-color: "#eaecef";
    // background-color: "#eef1f5";
    background-color: "#f2f2f2";
  }
}

.table-row-selected {
  background-color: #f5f7ff !important;
}
