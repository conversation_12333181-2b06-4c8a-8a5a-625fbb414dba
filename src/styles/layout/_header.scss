.protected-header {
  background-color: #ffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  line-height: 1;
  padding: 0 20px;

  // LOGO
  .logo {
    cursor: pointer;
    color: black;
    font-size: 1.5em;
    font-weight: bold;
    display: flex;
    align-items: center;

    img {
      height: 50px;
      width: 50px;
      object-fit: contain;
    }

    span {
      color: black;
      font-size: 22px;
      font-weight: 400;
      letter-spacing: 2px;
      display: flex;
      align-items: center;
    }
  }

  // Logout Section
  .logout-section {
    display: flex;
    color: #ff4d4f;
    align-items: center;
    cursor: pointer;
  }

  // Menu Items
  .ant-menu-overflow {
    flex: 1;
    margin-left: 32px;
    border-bottom: none;
  }
}
